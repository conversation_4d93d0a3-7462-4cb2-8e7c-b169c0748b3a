/* 响应式设计配置文件 */

/* 断点定义 */
$breakpoint-small: 375px;
$breakpoint-medium: 750px;
$breakpoint-large: 1024px;

/* 混合器 - 小屏幕 */
@mixin small-screen {
  @media (max-width: #{$breakpoint-small}) {
    @content;
  }
}

/* 混合器 - 中等屏幕 */
@mixin medium-screen {
  @media (max-width: #{$breakpoint-medium}) {
    @content;
  }
}

/* 混合器 - 大屏幕 */
@mixin large-screen {
  @media (min-width: #{$breakpoint-large}) {
    @content;
  }
}

/* 混合器 - 横屏模式 */
@mixin landscape-mode {
  @media (orientation: landscape) and (max-height: 500px) {
    @content;
  }
}

/* 混合器 - 竖屏模式 */
@mixin portrait-mode {
  @media (orientation: portrait) {
    @content;
  }
}

/* 安全区域适配混合器 */
@mixin safe-area-padding {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 响应式字体大小 */
@mixin responsive-font($base-size) {
  font-size: $base-size;
  
  @include medium-screen {
    font-size: $base-size * 0.9;
  }
  
  @include small-screen {
    font-size: $base-size * 0.8;
  }
  
  @include landscape-mode {
    font-size: $base-size * 0.7;
  }
}

/* 响应式尺寸 */
@mixin responsive-size($width, $height) {
  width: $width;
  height: $height;
  
  @include medium-screen {
    width: $width * 0.9;
    height: $height * 0.9;
  }
  
  @include small-screen {
    width: $width * 0.8;
    height: $height * 0.8;
  }
  
  @include landscape-mode {
    width: $width * 0.7;
    height: $height * 0.7;
  }
}

/* 响应式间距 */
@mixin responsive-spacing($margin, $padding: null) {
  margin: $margin;
  @if $padding {
    padding: $padding;
  }
  
  @include medium-screen {
    margin: $margin * 0.8;
    @if $padding {
      padding: $padding * 0.8;
    }
  }
  
  @include small-screen {
    margin: $margin * 0.6;
    @if $padding {
      padding: $padding * 0.6;
    }
  }
}

/* 游戏元素位置适配 */
@mixin game-element-position($top, $left, $right: null, $bottom: null) {
  top: $top;
  left: $left;
  @if $right {
    right: $right;
  }
  @if $bottom {
    bottom: $bottom;
  }
  
  @include landscape-mode {
    @if $top {
      top: $top * 0.8;
    }
    @if $bottom {
      bottom: $bottom * 0.8;
    }
  }
}
