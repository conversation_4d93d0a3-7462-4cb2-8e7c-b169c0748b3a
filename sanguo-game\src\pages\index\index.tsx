import { View, Text, Image } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import './index.scss'
import { gameConfig, Character, LevelConfig } from '../../config/gameConfig'

// 游戏状态类型
type GameState = 'start' | 'playing' | 'gameOver'
type ScanResult = 'good' | 'bad' | null

export default function Index() {
  // 游戏状态
  const [gameState, setGameState] = useState<GameState>('start')
  const [currentLevel, setCurrentLevel] = useState<LevelConfig>(gameConfig.levels[0])
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null)
  const [speechText, setSpeechText] = useState('')
  
  // 游戏数据
  const [timeLeft, setTimeLeft] = useState(0)
  const [hiredCount, setHiredCount] = useState(0)
  const [errorCount, setErrorCount] = useState(0)
  const [gameRunning, setGameRunning] = useState(false)
  
  // UI状态
  const [infoCardEnlarged, setInfoCardEnlarged] = useState(false)
  const [showScanner, setShowScanner] = useState(false)
  const [scanResult, setScanResult] = useState<ScanResult>(null)
  const [showScanResult, setShowScanResult] = useState(false)
  const [showGameOver, setShowGameOver] = useState(false)
  const [gameResult, setGameResult] = useState<'success' | 'fail'>('fail')

  // 页面加载
  useLoad(() => {
    console.log('三国打工人游戏加载完成')
    initializeGame()
  })

  // 初始化游戏
  const initializeGame = () => {
    setCurrentLevel(gameConfig.levels[0])
    setTimeLeft(gameConfig.levels[0].timeLimit)
  }

  // 获取随机对话
  const getRandomSpeech = (speeches: string[]) => {
    return speeches[Math.floor(Math.random() * speeches.length)]
  }

  // 生成下一个角色
  const generateNextCharacter = () => {
    if (!currentLevel) return
    
    const availableCharacters = currentLevel.characters
    const randomCharacter = availableCharacters[Math.floor(Math.random() * availableCharacters.length)]
    
    setCurrentCharacter(randomCharacter)
    setSpeechText(getRandomSpeech(randomCharacter.speeches.start))
  }

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (gameRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            endGame()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [gameRunning, timeLeft])

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 开始游戏
  const startGame = () => {
    setGameState('playing')
    setGameRunning(true)
    setHiredCount(0)
    setErrorCount(0)
    setTimeLeft(currentLevel.timeLimit)
    setShowGameOver(false)
    generateNextCharacter()
  }

  // 结束游戏
  const endGame = () => {
    setGameRunning(false)
    setGameState('gameOver')
    setShowGameOver(true)
    
    // 判断游戏结果
    if (hiredCount >= currentLevel.targetCount && errorCount <= currentLevel.maxErrors) {
      setGameResult('success')
    } else {
      setGameResult('fail')
    }
  }

  // 做出决定（录用/淘汰）- 不再有扫描动画
  const makeDecision = (decision: 'hire' | 'reject') => {
    if (!gameRunning || !currentCharacter) return

    if (decision === 'hire') {
      // 录用逻辑
      const newHiredCount = hiredCount + 1
      setHiredCount(newHiredCount)
      
      // 如果录用了不符合条件的人，增加错误计数
      if (!currentCharacter.isReal) {
        setErrorCount(prev => prev + 1)
      }
      
      // 显示录用对话
      setSpeechText(getRandomSpeech(currentCharacter.speeches.hired))
      
      // 检查是否达到目标人数
      if (newHiredCount >= currentLevel.targetCount) {
        setTimeout(() => endGame(), 2000)
        return
      }
    } else {
      // 淘汰逻辑 - 显示淘汰对话
      setSpeechText(getRandomSpeech(currentCharacter.speeches.rejected))
    }

    // 检查错误次数
    if (errorCount >= currentLevel.maxErrors) {
      setTimeout(() => endGame(), 2000)
      return
    }

    // 继续下一个角色
    setTimeout(() => {
      generateNextCharacter()
    }, 2000) // 2秒后切换到下一个角色
  }

  // 使用探测仪 - 只有这里才有扫描动画
  const useDetector = () => {
    if (!gameRunning || !currentCharacter) return
    
    setShowScanner(true)
    
    setTimeout(() => {
      setShowScanner(false)
      
      // 判断扫描结果
      const result = currentCharacter.isReal ? 'good' : 'bad'
      setScanResult(result)
      setShowScanResult(true)
      
      // 3秒后隐藏扫描结果
      setTimeout(() => {
        setShowScanResult(false)
        setScanResult(null)
      }, 3000)
    }, gameConfig.globalSettings.scanDuration)
  }

  // 添加时间
  const addTime = () => {
    if (!gameRunning) return
    setTimeLeft(prev => prev + currentLevel.addTimeAmount)
  }

  // 切换资料卡大小
  const toggleInfoCard = () => {
    setInfoCardEnlarged(!infoCardEnlarged)
  }

  // 重新开始游戏
  const restartGame = () => {
    setShowGameOver(false)
    startGame()
  }

  return (
    <View className='game-container'>
      {/* 背景 */}
      <View className='background'></View>

      {/* 开始界面 */}
      {gameState === 'start' && (
        <View className='start-screen'>
          <Text className='game-title'>三国打工人</Text>
          <Text className='game-description'>
            在众多冒充者中招到真正的三国打工人！
            仔细查看资料，使用探测仪识别真伪。
          </Text>
          <View className='start-button' onClick={startGame}>
            <Text className='start-button-text'>开始游戏</Text>
          </View>
        </View>
      )}

      {/* 游戏界面 */}
      {gameState === 'playing' && (
        <>
          {/* 标题栏 */}
          <View className='title-bar'>
            <Image className='title' src={require('../../images/背景上部标题(临时工招聘会).png')} />
            <View className='timer'>{formatTime(timeLeft)}</View>
          </View>

          {/* 游戏主体 */}
          <View className='game-main'>
            {/* 角色 */}
            {currentCharacter && (
              <Image
                className='character'
                src={require(`../../images/${currentCharacter.image}`)}
              />
            )}
          </View>
          
          {/* 对话气泡 */}
          <View className='speech-bubble'>
            <Text className='speech-text'>{speechText}</Text>
          </View>

          {/* 资料卡 */}
          {currentCharacter && (
            <View
              className={`info-card ${infoCardEnlarged ? 'enlarged' : ''}`}
              onClick={toggleInfoCard}
            >
              <View className='info-content'>
                <Text className='info-name'>{currentCharacter.name}</Text>
                {currentCharacter.traits.map((trait, index) => (
                  <Text key={index} className='info-item'>{trait}</Text>
                ))}
              </View>
            </View>
          )}

          {/* 底部按钮 */}
          <View className='bottom-bar'>
            <View className='buttons'>
              <Image
                className='hire-button'
                src={require('../../images/录用按钮.png')}
                onClick={() => makeDecision('hire')}
              />
              <Image
                className='reject-button'
                src={require('../../images/淘汰按钮.png')}
                onClick={() => makeDecision('reject')}
              />
            </View>
          </View>

          {/* 目标槽位 */}
          <View className='targets'>
            {Array.from({ length: currentLevel.targetCount }, (_, i) => (
              <View
                key={i}
                className={`target-slot ${i < hiredCount ? 'filled' : ''}`}
              />
            ))}
          </View>

          {/* 工具 */}
          <View className='tools'>
            <Image
              className='detector'
              src={require('../../images/探测图标.png')}
              onClick={useDetector}
            />
            <Image
              className='time-add'
              src={require('../../images/加时图标.png')}
              onClick={addTime}
            />
          </View>
        </>
      )}

      {/* 扫描器 */}
      {showScanner && (
        <View className='scanner'>
          <Image className='scan-mask' src={require('../../images/扫描时的黑色遮罩.png')} />
          <Image className='scan-frame' src={require('../../images/扫描时的框.png')} />
          <Image className='scan-bar' src={require('../../images/扫描条(由上向下普速).png')} />
        </View>
      )}

      {/* 扫描结果 */}
      {showScanResult && scanResult && (
        <View className='scan-result'>
          <Image className='result-bg' src={require('../../images/扫描后弹窗文字的背景框.png')} />
          <Text className='result-text'>
            {scanResult === 'good' ? '这家伙看上去很不错' : '这家伙看上去不靠谱'}
          </Text>
        </View>
      )}

      {/* 游戏结束界面 */}
      {showGameOver && (
        <View className='game-over'>
          <Text className='game-over-title'>
            {gameResult === 'success' ? '招聘成功！' : '招聘失败！'}
          </Text>
          <Text className='result-text'>
            录用人数: {hiredCount}/{currentLevel.targetCount}
            {'\n'}错误次数: {errorCount}/{currentLevel.maxErrors}
          </Text>
          <View className='restart-button' onClick={restartGame}>
            <Text className='restart-button-text'>重新开始</Text>
          </View>
        </View>
      )}
    </View>
  )
}
