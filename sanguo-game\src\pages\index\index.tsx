import { View, Text, Image } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import './index.scss'
import { gameConfig, Character, LevelConfig } from '../../config/gameConfig'
import { responsiveUtils } from '../../utils/responsive'

// 静态导入图片
const images = {
  '张飞主角色招聘图.png': require('../../images/张飞主角色招聘图.png'),
  '录用按钮.png': require('../../images/录用按钮.png'),
  '淘汰按钮.png': require('../../images/淘汰按钮.png'),
  '背景上部标题(临时工招聘会).png': require('../../images/背景上部标题(临时工招聘会).png'),
  '探测图标.png': require('../../images/探测图标.png'),
  '加时图标.png': require('../../images/加时图标.png'),
  '扫描时的黑色遮罩.png': require('../../images/扫描时的黑色遮罩.png'),
  '扫描时的框.png': require('../../images/扫描时的框.png'),
  '扫描条(由上向下普速).png': require('../../images/扫描条(由上向下普速).png'),
  '扫描后弹窗文字的背景框.png': require('../../images/扫描后弹窗文字的背景框.png'),
  '招聘成功的盖章.png': require('../../images/招聘成功的盖章.png'),
  '招聘失败的淘汰盖章.png': require('../../images/招聘失败的淘汰盖章.png'),
  '桌子.png': require('../../images/桌子.png'),
  '椅子.png': require('../../images/椅子.png')
}

// 游戏状态类型
type GameState = 'start' | 'playing' | 'gameOver'
type ScanResult = 'good' | 'bad' | null

export default function Index() {
  // 游戏状态
  const [gameState, setGameState] = useState<GameState>('start')
  const [currentLevel, setCurrentLevel] = useState<LevelConfig>(gameConfig.levels[0])
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null)
  const [speechText, setSpeechText] = useState('')
  
  // 游戏数据
  const [timeLeft, setTimeLeft] = useState(0)
  const [hiredCount, setHiredCount] = useState(0)
  const [errorCount, setErrorCount] = useState(0)
  const [gameRunning, setGameRunning] = useState(false)
  
  // UI状态
  const [infoCardEnlarged, setInfoCardEnlarged] = useState(false)
  const [showScanner, setShowScanner] = useState(false)
  const [scanAnimationActive, setScanAnimationActive] = useState(false)
  const [scanResult, setScanResult] = useState<ScanResult>(null)
  const [showScanResult, setShowScanResult] = useState(false)
  const [showGameOver, setShowGameOver] = useState(false)
  const [gameResult, setGameResult] = useState<'success' | 'fail'>('fail')

  // 拖拽状态
  const [isDragging, setIsDragging] = useState(false)
  const [dragType, setDragType] = useState<'hire' | 'reject' | null>(null)
  const [dragPosition, setDragPosition] = useState({ x: 0, y: 0 })
  const [isOverInfoCard, setIsOverInfoCard] = useState(false)
  const [stampProgress, setStampProgress] = useState(0)
  const [isStamping, setIsStamping] = useState(false)
  const [showStampResult, setShowStampResult] = useState(false)

  // 页面加载
  useLoad(() => {
    console.log('三国打工人游戏加载完成')
    initializeGame()
    initializeResponsive()
  })

  // 初始化游戏
  const initializeGame = () => {
    setCurrentLevel(gameConfig.levels[0])
    setTimeLeft(gameConfig.levels[0].timeLimit)
  }

  // 初始化响应式适配
  const initializeResponsive = () => {
    // 确保响应式工具已初始化
    responsiveUtils.init()

    // 添加屏幕变化监听
    const handleResize = () => {
      responsiveUtils.updateScreenInfo()
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
    }
  }

  // 获取随机对话
  const getRandomSpeech = (speeches: string[]) => {
    return speeches[Math.floor(Math.random() * speeches.length)]
  }

  // 生成下一个角色
  const generateNextCharacter = () => {
    if (!currentLevel) return
    
    const availableCharacters = currentLevel.characters
    const randomCharacter = availableCharacters[Math.floor(Math.random() * availableCharacters.length)]
    
    setCurrentCharacter(randomCharacter)
    setSpeechText(getRandomSpeech(randomCharacter.speeches.start))
  }

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (gameRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            endGame()
            return 0
          }
          return prev - 1
        })
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [gameRunning, timeLeft])

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 开始游戏
  const startGame = () => {
    setGameState('playing')
    setGameRunning(true)
    setHiredCount(0)
    setErrorCount(0)
    setTimeLeft(currentLevel.timeLimit)
    setShowGameOver(false)
    generateNextCharacter()
  }

  // 结束游戏
  const endGame = () => {
    setGameRunning(false)
    setGameState('gameOver')
    setShowGameOver(true)
    
    // 判断游戏结果
    if (hiredCount >= currentLevel.targetCount && errorCount <= currentLevel.maxErrors) {
      setGameResult('success')
    } else {
      setGameResult('fail')
    }
  }

  // 做出决定（录用/淘汰）- 不再有扫描动画
  const makeDecision = (decision: 'hire' | 'reject') => {
    if (!gameRunning || !currentCharacter) return

    if (decision === 'hire') {
      // 录用逻辑
      const newHiredCount = hiredCount + 1
      setHiredCount(newHiredCount)
      
      // 如果录用了不符合条件的人，增加错误计数
      if (!currentCharacter.isReal) {
        setErrorCount(prev => prev + 1)
      }
      
      // 显示录用对话
      setSpeechText(getRandomSpeech(currentCharacter.speeches.hired))
      
      // 检查是否达到目标人数
      if (newHiredCount >= currentLevel.targetCount) {
        setTimeout(() => endGame(), 2000)
        return
      }
    } else {
      // 淘汰逻辑 - 显示淘汰对话
      setSpeechText(getRandomSpeech(currentCharacter.speeches.rejected))
    }

    // 检查错误次数
    if (errorCount >= currentLevel.maxErrors) {
      setTimeout(() => endGame(), 2000)
      return
    }

    // 继续下一个角色
    setTimeout(() => {
      generateNextCharacter()
    }, 2000) // 2秒后切换到下一个角色
  }

  // 使用探测仪 - 只有这里才有扫描动画
  const useDetector = () => {
    if (!gameRunning || !currentCharacter) return

    setShowScanner(true)
    setScanAnimationActive(true)

    setTimeout(() => {
      // 扫描动画完成后停止动画，但保持扫描界面，显示扫描结果
      setScanAnimationActive(false)
      const result = currentCharacter.isReal ? 'good' : 'bad'
      setScanResult(result)
      setShowScanResult(true)
      // 注意：不再自动隐藏扫描器和结果，等待用户点击
    }, gameConfig.globalSettings.scanDuration)
  }

  // 处理点击屏幕隐藏扫描相关界面
  const handleScreenClick = (event: any) => {
    // 只有在显示扫描器或扫描结果时才处理点击
    if (showScanner || showScanResult) {
      // 阻止事件冒泡，避免干扰其他交互
      event.stopPropagation()
      setShowScanner(false)
      setScanAnimationActive(false)
      setShowScanResult(false)
      setScanResult(null)
    }
  }

  // 添加时间
  const addTime = () => {
    if (!gameRunning) return
    setTimeLeft(prev => prev + currentLevel.addTimeAmount)
  }

  // 切换资料卡大小
  const toggleInfoCard = () => {
    setInfoCardEnlarged(!infoCardEnlarged)
  }

  // 重新开始游戏
  const restartGame = () => {
    setShowGameOver(false)
    startGame()
  }

  // 拖拽开始
  const handleDragStart = (type: 'hire' | 'reject', event: any) => {
    if (!gameRunning || !currentCharacter) return

    setIsDragging(true)
    setDragType(type)

    // 获取触摸位置
    const touch = event.touches[0]
    setDragPosition({ x: touch.clientX, y: touch.clientY })
  }

  // 拖拽移动
  const handleDragMove = (event: any) => {
    if (!isDragging) return

    const touch = event.touches[0]
    setDragPosition({ x: touch.clientX, y: touch.clientY })

    // 精确的信息卡片区域检测
    const screenWidth = window.innerWidth || 375
    const screenHeight = window.innerHeight || 667

    // 信息卡片精确位置 (根据CSS中的实际位置计算)
    // CSS: left: 0rpx, width: 60%, bottom: -10rpx, height: 70%
    const cardLeft = 0                        // left: 0rpx
    const cardRight = screenWidth * 0.6       // width: 60%
    const cardTop = screenHeight * 0.3        // height: 70% -> top: 30%
    const cardBottom = screenHeight           // bottom: -10rpx -> 接近屏幕底部

    const isOver = touch.clientX >= cardLeft &&
                   touch.clientX <= cardRight &&
                   touch.clientY >= cardTop &&
                   touch.clientY <= cardBottom

    console.log('拖拽检测:', {
      touchX: touch.clientX,
      touchY: touch.clientY,
      cardLeft,
      cardRight,
      cardTop,
      cardBottom,
      isOver
    })

    if (isOver && !isOverInfoCard) {
      setIsOverInfoCard(true)
      startStamping()
    } else if (!isOver && isOverInfoCard) {
      setIsOverInfoCard(false)
      cancelStamping() // 立即停止进度并取消盖章
      console.log('拖离信息卡片 - 取消盖章，重置状态')
    }
  }

  // 拖拽结束
  const handleDragEnd = () => {
    if (!isDragging) return

    console.log('拖拽结束 - isOverInfoCard:', isOverInfoCard, 'isStamping:', isStamping)

    // 无论在哪里结束拖拽，都要清理状态
    cancelStamping()

    setIsDragging(false)
    setDragType(null)
    setIsOverInfoCard(false)
  }

  // 开始盖章进度
  const startStamping = () => {
    // 先清理任何现有的进度
    if ((window as any).stampInterval) {
      clearInterval((window as any).stampInterval)
      ;(window as any).stampInterval = null
    }

    console.log('开始盖章进度 - 重置状态并开始新的进度')
    setIsStamping(true)
    setStampProgress(0)

    const progressInterval = setInterval(() => {
      setStampProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval)
          ;(window as any).stampInterval = null
          completeStamping()
          return 100
        }
        return prev + 5 // 每100ms增加5%，总共2秒（更慢的进度）
      })
    }, 100)

    // 保存interval引用以便取消
    ;(window as any).stampInterval = progressInterval
  }

  // 取消盖章
  const cancelStamping = () => {
    // 清除进度条定时器
    if ((window as any).stampInterval) {
      clearInterval((window as any).stampInterval)
      ;(window as any).stampInterval = null
    }

    // 重置所有相关状态
    setIsStamping(false)
    setStampProgress(0)

    // 确保不会触发盖章完成
    console.log('盖章已取消 - 用户将印章移出信息卡片区域')
  }

  // 完成盖章
  const completeStamping = () => {
    if (!dragType || !currentCharacter) return

    setIsStamping(false)
    setIsDragging(false)
    setShowStampResult(true)

    // 执行决定
    makeDecision(dragType)

    // 2秒后隐藏盖章结果
    setTimeout(() => {
      setShowStampResult(false)
    }, 2000)
  }

  return (
    <View
      className='game-container'
      onTouchMove={handleDragMove}
      onTouchEnd={handleDragEnd}
    >
      {/* 背景 */}
      <View className='background'></View>

      {/* 开始界面 */}
      {gameState === 'start' && (
        <View className='start-screen'>
          <Text className='game-title'>三国打工人</Text>
          {/* <Text className='game-description'>
            在众多冒充者中招到真正的三国打工人！
            仔细查看资料，使用探测仪识别真伪。
          </Text> */}
          <View className='start-button' onClick={startGame}>
            <Text className='start-button-text'>开始游戏</Text>
          </View>
        </View>
      )}

      {/* 游戏界面 */}
      {gameState === 'playing' && (
        <>
          {/* 标题栏 */}
          <View className='title-bar'>
            <Image className='title' src={images['背景上部标题(临时工招聘会).png']} />
            <View className='timer'>{formatTime(timeLeft)}</View>
          </View>

          {/* 椅子 - 放在招聘者身后(下层) */}
          <Image className='chair' src={images['椅子.png']} />

          {/* 游戏主体 */}
          <View className='game-main'>
            {/* 角色 */}
            {currentCharacter && (
              <Image
                className='character'
                src={images[currentCharacter.image] || images['张飞主角色招聘图.png']}
              />
            )}
          </View>

          {/* 桌子 - 放在信息卡片下层和招聘者上层 */}
          <Image className='desk' src={images['桌子.png']} />
          
          {/* 对话气泡 */}
          <View className='speech-bubble'>
            <Text className='speech-text'>{speechText}</Text>
          </View>

          {/* 资料卡 */}
          {currentCharacter && (
            <View
              className={`info-card ${infoCardEnlarged ? 'enlarged' : ''}`}
              onClick={toggleInfoCard}
            >
              <View className='info-content'>
                <Text className='info-name'>{currentCharacter.name}</Text>
                {currentCharacter.traits.map((trait, index) => (
                  <Text key={index} className='info-item'>{trait}</Text>
                ))}
              </View>
            </View>
          )}

          {/* 底部按钮 */}
          <View className='bottom-bar'>
            <View className='buttons'>
              <Image
                className={`reject-button ${isDragging && dragType === 'reject' ? 'dragging' : ''}`}
                src={images['淘汰按钮.png']}
                onTouchStart={(e) => handleDragStart('reject', e)}
                onTouchMove={handleDragMove}
                onTouchEnd={handleDragEnd}
              />
              <Image
                className={`hire-button ${isDragging && dragType === 'hire' ? 'dragging' : ''}`}
                src={images['录用按钮.png']}
                onTouchStart={(e) => handleDragStart('hire', e)}
                onTouchMove={handleDragMove}
                onTouchEnd={handleDragEnd}
              />
            </View>
          </View>

          {/* 目标槽位 */}
          <View className='targets'>
            {Array.from({ length: currentLevel.targetCount }, (_, i) => (
              <View
                key={i}
                className={`target-slot ${i < hiredCount ? 'filled' : ''}`}
              />
            ))}
          </View>

          {/* 工具 */}
          <View className='tools'>
            <Image
              className='time-add'
              src={images['加时图标.png']}
              onClick={addTime}
            />
            <Image
              className='detector'
              src={images['探测图标.png']}
              onClick={useDetector}
            />
          </View>
        </>
      )}

      {/* 拖拽中的印章 */}
      {isDragging && dragType && (
        <View
          className='dragging-stamp'
          style={{
            left: `${dragPosition.x - 40}px`,
            top: `${dragPosition.y - 40}px`,
          }}
        >
          <Image
            className='stamp-image'
            src={dragType === 'hire' ? images['录用按钮.png'] : images['淘汰按钮.png']}
          />

          {/* 进度条跟随印章移动 */}
          {isStamping && isOverInfoCard && (
            <View className='stamp-progress-container'>
              <View className={`stamp-progress-bg ${dragType}`}>
                <View
                  className={`stamp-progress-fill ${dragType}`}
                  style={{
                    transform: `rotate(${(stampProgress / 100) * 360}deg)`
                  }}
                />
              </View>
            </View>
          )}
        </View>
      )}



      {/* 盖章结果 */}
      {showStampResult && dragType && (
        <View className='stamp-result-overlay'>
          <View className='stamp-result-content'>
            <Text className='stamp-result-text'>
              {dragType === 'hire' ? '录用成功！' : '淘汰成功！'}
            </Text>
            <Image
              className='stamp-result-image'
              src={dragType === 'hire' ? images['招聘成功的盖章.png'] : images['招聘失败的淘汰盖章.png']}
            />
          </View>
        </View>
      )}

      {/* 扫描器 */}
      {showScanner && (
        <View className='scanner' onClick={handleScreenClick}>
          <Image className='scan-mask' src={images['扫描时的黑色遮罩.png']} />
          <Image className='scan-frame' src={images['扫描时的框.png']} />
          {/* 扫描条只在动画激活时显示 */}
          {scanAnimationActive && (
            <Image className='scan-bar' src={images['扫描条(由上向下普速).png']} />
          )}
        </View>
      )}

      {/* 扫描结果 */}
      {showScanResult && scanResult && (
        <View className='scan-result' onClick={handleScreenClick}>
          <Image className='result-bg' src={images['扫描后弹窗文字的背景框.png']} />
          <Text className='result-text'>
            {scanResult === 'good' ? '“这家伙看上去不错”' : '“这家伙看上去不靠谱”'}
          </Text>
        </View>
      )}

      {/* 游戏结束界面 */}
      {showGameOver && (
        <View className='game-over'>
          <Text className='game-over-title'>
            {gameResult === 'success' ? '招聘成功！' : '招聘失败！'}
          </Text>
          <Text className='result-text'>
            录用人数: {hiredCount}/{currentLevel.targetCount}
            {'\n'}错误次数: {errorCount}/{currentLevel.maxErrors}
          </Text>
          <View className='restart-button' onClick={restartGame}>
            <Text className='restart-button-text'>重新开始</Text>
          </View>
        </View>
      )}
    </View>
  )
}
