import { View, Text, Image } from '@tarojs/components'
import { useLoad } from '@tarojs/taro'
import { useEffect, useState } from 'react'
import './index.scss'

// 角色数据类型定义
interface Character {
  name: string;
  traits: string[];
  isReal: boolean;
  image: string;
  startDialogues: string[];
  hireDialogues: string[];
  rejectDialogues: string[];
}

// 角色数据
const characters: Character[] = [
  {
    name: "张飞",
    traits: ["黑发", "墨镜", "皮肤黝黑", "易怒", "擅长使矛"],
    isReal: true,
    image: require("../../images/张飞主角色招聘图.png"),
    startDialogues: [
      "老板，我很强的，我抓鹅特别厉害",
      "老板，你看我合适吗",
      "老板，请让我和您一起干番大事业",
      "老板，你看我的宝剑锋利吗"
    ],
    hireDialogues: [
      "老板，您眼光真准",
      "老板，您一看就是做大事的人",
      "谢谢老板，我一定会好好努力",
      "我确实很厉害"
    ],
    rejectDialogues: [
      "我被淘汰辣？？？",
      "哎老板你听我说啊，好歹加个v啊，哎老板……",
      "不要我算了，正好另一家开高薪请我呢",
      "你不相信我？也罢"
    ]
  },
  {
    name: "假张飞",
    traits: ["黑发", "墨镜", "皮肤白皙", "易怒", "擅长使矛"],
    isReal: false,
    image: require("../../images/张飞主角色招聘图.png"),
    startDialogues: [
      "老板，很多人冒充我们三国人士，我才是真的",
      "老板您看，我是大汉三本毕业的",
      "不用看，就我了",
      "老板，可找着你们了"
    ],
    hireDialogues: [
      "老板，您眼光真准",
      "老板，您一看就是做大事的人",
      "谢谢老板，我一定会好好努力",
      "我确实很厉害"
    ],
    rejectDialogues: [
      "呃，我回去准备一下再来",
      "哎呀，老板你是不是没看清楚啊",
      "哎不是，老板，我真能辅佐老板打天下的啊",
      "我就知道白跑一趟"
    ]
  }
];

export default function Index() {
  // 游戏状态
  const [gameRunning, setGameRunning] = useState(false);
  const [timeLeft, setTimeLeft] = useState(120); // 2分钟
  const [hiredCount, setHiredCount] = useState(0);
  const [wrongHires, setWrongHires] = useState(0);
  const [currentCharacter, setCurrentCharacter] = useState<Character | null>(null);
  const [speechText, setSpeechText] = useState("");
  const [showScanner, setShowScanner] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [resultType, setResultType] = useState<'success' | 'fail'>('success');
  const [showGameOver, setShowGameOver] = useState(false);
  const [infoCardEnlarged, setInfoCardEnlarged] = useState(false);

  const targetCount = 5;
  const maxWrongHires = 3;

  useLoad(() => {
    console.log('三国打工人游戏加载完成')
  })

  // 计时器效果
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (gameRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            endGame();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [gameRunning, timeLeft]);

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 开始游戏
  const startGame = () => {
    setGameRunning(true);
    setHiredCount(0);
    setWrongHires(0);
    setTimeLeft(120);
    setShowGameOver(false);
    nextCharacter();
  };

  // 下一个角色
  const nextCharacter = () => {
    const randomIndex = Math.floor(Math.random() * characters.length);
    const character = characters[randomIndex];
    setCurrentCharacter(character);

    // 随机选择开始对话
    const randomDialogueIndex = Math.floor(Math.random() * character.startDialogues.length);
    setSpeechText(character.startDialogues[randomDialogueIndex]);
  };

  // 做出决定（录用/淘汰）
  const makeDecision = (decision: 'hire' | 'reject') => {
    if (!gameRunning || !currentCharacter) return;

    // 显示扫描动画
    setShowScanner(true);

    setTimeout(() => {
      setShowScanner(false);

      let isCorrectDecision = false;

      if (decision === 'hire' && currentCharacter.isReal) {
        // 正确录用
        setResultType('success');
        setHiredCount(prev => prev + 1);
        isCorrectDecision = true;
      } else if (decision === 'reject' && !currentCharacter.isReal) {
        // 正确淘汰
        setResultType('fail');
        isCorrectDecision = true;
      } else if (decision === 'hire' && !currentCharacter.isReal) {
        // 错误录用
        setResultType('fail');
        setWrongHires(prev => prev + 1);
      } else {
        // 错误淘汰
        setResultType('fail');
      }

      // 显示结果
      setShowResult(true);

      // 更新对话
      const dialogues = decision === 'hire' ?
        currentCharacter.hireDialogues :
        currentCharacter.rejectDialogues;
      const randomDialogueIndex = Math.floor(Math.random() * dialogues.length);
      setSpeechText(dialogues[randomDialogueIndex]);

      // 检查游戏是否结束
      const newHiredCount = decision === 'hire' && currentCharacter.isReal ? hiredCount + 1 : hiredCount;
      const newWrongHires = decision === 'hire' && !currentCharacter.isReal ? wrongHires + 1 : wrongHires;

      if (newHiredCount >= targetCount || newWrongHires >= maxWrongHires) {
        setTimeout(() => {
          endGame();
        }, 1500);
        return;
      }

      // 延迟后进入下一个角色
      setTimeout(() => {
        setShowResult(false);
        nextCharacter();
      }, 1500);
    }, 2000); // 扫描动画持续2秒
  };

  // 游戏结束
  const endGame = () => {
    setGameRunning(false);
    setShowGameOver(true);
  };

  // 重新开始游戏
  const restartGame = () => {
    setShowGameOver(false);
    startGame();
  };

  // 使用探测仪
  const useDetector = () => {
    if (!gameRunning) return;
    // 这里可以添加探测仪功能
    console.log('探测到可疑特征！请仔细检查资料信息。');
  };

  // 添加时间
  const addTime = () => {
    if (!gameRunning) return;
    setTimeLeft(prev => prev + 60);
    console.log('已增加60秒时间！');
  };

  // 切换资料卡大小
  const toggleInfoCard = () => {
    setInfoCardEnlarged(!infoCardEnlarged);
  };

  return (
    <View className='game-container'>
      {/* 背景 */}
      <View className='background'></View>

      {/* 标题栏 */}
      <View className='title-bar'></View>
      <Image className='title' src={require('../../images/背景上部标题(临时工招聘会).png')} />

      {/* 计时器 */}
      <View className='timer'>{formatTime(timeLeft)}</View>

      {/* 底部栏 */}
      <View className='bottom-bar'></View>

      {/* 角色 */}
      {currentCharacter && (
        <Image
          className='character'
          src={currentCharacter.image}
        />
      )}

      {/* 对话气泡 */}
      <View className='speech-bubble'>
        <Text className='speech-text'>{speechText}</Text>
      </View>

      {/* 资料卡 */}
      {currentCharacter && (
        <View
          className={`info-card ${infoCardEnlarged ? 'enlarged' : ''}`}
          onClick={toggleInfoCard}
        >
          <View className='info-content'>
            <View className='info-name'>{currentCharacter.name}</View>
            {currentCharacter.traits.map((trait, index) => (
              <View key={index} className='info-item'>• {trait}</View>
            ))}
          </View>
        </View>
      )}

      {/* 按钮 */}
      <View className='buttons'>
        <View
          className='hire-button'
          onClick={() => makeDecision('hire')}
        ></View>
        <View
          className='reject-button'
          onClick={() => makeDecision('reject')}
        ></View>
      </View>

      {/* 目标槽位 */}
      <View className='targets'>
        {Array.from({ length: targetCount }, (_, i) => (
          <View
            key={i}
            className={`target-slot ${i < hiredCount ? 'filled' : ''}`}
          ></View>
        ))}
      </View>

      {/* 工具 */}
      <View className='tools'>
        <View className='detector' onClick={useDetector}></View>
        <View className='time-add' onClick={addTime}></View>
      </View>

      {/* 扫描器 */}
      {showScanner && (
        <View className='scanner'>
          <View className='scan-frame'></View>
          <View className='scan-bar'></View>
        </View>
      )}

      {/* 结果显示 */}
      {showResult && (
        <View className={`result ${resultType}`}></View>
      )}

      {/* 开始界面 */}
      {!gameRunning && !showGameOver && (
        <View className='start-screen'>
          <Text className='game-title'>三国打工人</Text>
          <Text className='game-description'>
            在众多冒充者中招到真正的三国打工人！{'\n'}
            仔细对比资料，做出明智的选择。
          </Text>
          <View className='start-button' onClick={startGame}>
            <Text className='start-button-text'>开始招聘</Text>
          </View>
        </View>
      )}

      {/* 游戏结束界面 */}
      {showGameOver && (
        <View className='game-over'>
          <Text className='game-over-title'>游戏结束</Text>
          <Text className='result-text'>
            {hiredCount >= targetCount
              ? `恭喜！你成功招聘了 ${hiredCount} 名真正的三国打工人！`
              : wrongHires >= maxWrongHires
              ? `你错误录用了 ${wrongHires} 名冒充者，招聘失败！`
              : `时间到！你只招聘到了 ${hiredCount}/${targetCount} 名三国打工人。`
            }
          </Text>
          <View className='restart-button' onClick={restartGame}>
            <Text className='restart-button-text'>再来一次</Text>
          </View>
        </View>
      )}
    </View>
  )
}
