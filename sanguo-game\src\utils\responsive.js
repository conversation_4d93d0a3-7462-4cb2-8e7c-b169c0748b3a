/**
 * 响应式适配工具类
 */

export class ResponsiveUtils {
  constructor() {
    this.screenWidth = 0;
    this.screenHeight = 0;
    this.pixelRatio = 1;
    this.isLandscape = false;
    this.isSmallScreen = false;
    this.init();
  }

  /**
   * 初始化屏幕信息
   */
  init() {
    if (typeof window !== 'undefined') {
      this.updateScreenInfo();
      window.addEventListener('resize', () => this.updateScreenInfo());
      window.addEventListener('orientationchange', () => {
        setTimeout(() => this.updateScreenInfo(), 100);
      });
    }
  }

  /**
   * 更新屏幕信息
   */
  updateScreenInfo() {
    this.screenWidth = window.innerWidth;
    this.screenHeight = window.innerHeight;
    this.pixelRatio = window.devicePixelRatio || 1;
    this.isLandscape = this.screenWidth > this.screenHeight;
    this.isSmallScreen = this.screenWidth <= 375;
    
    // 更新CSS变量
    document.documentElement.style.setProperty('--screen-width', `${this.screenWidth}px`);
    document.documentElement.style.setProperty('--screen-height', `${this.screenHeight}px`);
    document.documentElement.style.setProperty('--pixel-ratio', this.pixelRatio);
    
    // 添加屏幕类型类名
    document.body.classList.toggle('landscape', this.isLandscape);
    document.body.classList.toggle('portrait', !this.isLandscape);
    document.body.classList.toggle('small-screen', this.isSmallScreen);
  }

  /**
   * 获取响应式尺寸
   * @param {number} baseSize 基础尺寸
   * @returns {number} 适配后的尺寸
   */
  getResponsiveSize(baseSize) {
    let scale = 1;
    
    if (this.isSmallScreen) {
      scale = 0.8;
    } else if (this.screenWidth <= 750) {
      scale = 0.9;
    }
    
    if (this.isLandscape && this.screenHeight <= 500) {
      scale *= 0.8;
    }
    
    return Math.round(baseSize * scale);
  }

  /**
   * 获取响应式字体大小
   * @param {number} baseFontSize 基础字体大小
   * @returns {number} 适配后的字体大小
   */
  getResponsiveFontSize(baseFontSize) {
    return this.getResponsiveSize(baseFontSize);
  }

  /**
   * 获取安全区域信息
   * @returns {object} 安全区域信息
   */
  getSafeAreaInsets() {
    const style = getComputedStyle(document.documentElement);
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)')) || 0,
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)')) || 0,
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)')) || 0,
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)')) || 0
    };
  }

  /**
   * 获取游戏元素的适配位置
   * @param {object} position 原始位置信息
   * @returns {object} 适配后的位置信息
   */
  getAdaptivePosition(position) {
    const { top, left, right, bottom, width, height } = position;
    const safeArea = this.getSafeAreaInsets();
    
    let adaptedPosition = { ...position };
    
    // 适配顶部位置（考虑安全区域）
    if (top !== undefined) {
      adaptedPosition.top = top + safeArea.top;
    }
    
    // 适配底部位置（考虑安全区域）
    if (bottom !== undefined) {
      adaptedPosition.bottom = bottom + safeArea.bottom;
    }
    
    // 适配左右位置（考虑安全区域）
    if (left !== undefined) {
      adaptedPosition.left = left + safeArea.left;
    }
    if (right !== undefined) {
      adaptedPosition.right = right + safeArea.right;
    }
    
    // 适配尺寸
    if (width !== undefined) {
      adaptedPosition.width = this.getResponsiveSize(width);
    }
    if (height !== undefined) {
      adaptedPosition.height = this.getResponsiveSize(height);
    }
    
    return adaptedPosition;
  }

  /**
   * 检查是否为小屏设备
   * @returns {boolean}
   */
  isSmallDevice() {
    return this.isSmallScreen;
  }

  /**
   * 检查是否为横屏模式
   * @returns {boolean}
   */
  isLandscapeMode() {
    return this.isLandscape;
  }

  /**
   * 获取屏幕比例
   * @returns {number}
   */
  getScreenRatio() {
    return this.screenWidth / this.screenHeight;
  }

  /**
   * 获取适配的游戏容器尺寸
   * @returns {object}
   */
  getGameContainerSize() {
    const safeArea = this.getSafeAreaInsets();
    return {
      width: this.screenWidth - safeArea.left - safeArea.right,
      height: this.screenHeight - safeArea.top - safeArea.bottom
    };
  }
}

// 创建全局实例
export const responsiveUtils = new ResponsiveUtils();

// 导出默认实例
export default responsiveUtils;
