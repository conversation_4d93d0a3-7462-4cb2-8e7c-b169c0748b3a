(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/index/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./src/pages/index/index.tsx":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./src/pages/index/index.tsx ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Index; }
/* harmony export */ });
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "webpack/container/remote/react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _config_gameConfig__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../config/gameConfig */ "./src/config/gameConfig.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "webpack/container/remote/react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__);






// 游戏状态类型

function Index() {
  // 游戏状态
  const [gameState, setGameState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('start');
  const [currentLevel, setCurrentLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_config_gameConfig__WEBPACK_IMPORTED_MODULE_2__.gameConfig.levels[0]);
  const [currentCharacter, setCurrentCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
  const [speechText, setSpeechText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');

  // 游戏数据
  const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);
  const [hiredCount, setHiredCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);
  const [errorCount, setErrorCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);
  const [gameRunning, setGameRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);

  // UI状态
  const [infoCardEnlarged, setInfoCardEnlarged] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [scanResult, setScanResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
  const [showScanResult, setShowScanResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [showGameOver, setShowGameOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [gameResult, setGameResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('fail');

  // 页面加载
  (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__.useLoad)(() => {
    console.log('三国打工人游戏加载完成');
    initializeGame();
  });

  // 初始化游戏
  const initializeGame = () => {
    setCurrentLevel(_config_gameConfig__WEBPACK_IMPORTED_MODULE_2__.gameConfig.levels[0]);
    setTimeLeft(_config_gameConfig__WEBPACK_IMPORTED_MODULE_2__.gameConfig.levels[0].timeLimit);
  };

  // 获取随机对话
  const getRandomSpeech = speeches => {
    return speeches[Math.floor(Math.random() * speeches.length)];
  };

  // 生成下一个角色
  const generateNextCharacter = () => {
    if (!currentLevel) return;
    const availableCharacters = currentLevel.characters;
    const randomCharacter = availableCharacters[Math.floor(Math.random() * availableCharacters.length)];
    setCurrentCharacter(randomCharacter);
    setSpeechText(getRandomSpeech(randomCharacter.speeches.start));
  };

  // 计时器效果
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    let interval;
    if (gameRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            endGame();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [gameRunning, timeLeft]);

  // 格式化时间显示
  const formatTime = seconds => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 开始游戏
  const startGame = () => {
    setGameState('playing');
    setGameRunning(true);
    setHiredCount(0);
    setErrorCount(0);
    setTimeLeft(currentLevel.timeLimit);
    setShowGameOver(false);
    generateNextCharacter();
  };

  // 结束游戏
  const endGame = () => {
    setGameRunning(false);
    setGameState('gameOver');
    setShowGameOver(true);

    // 判断游戏结果
    if (hiredCount >= currentLevel.targetCount && errorCount <= currentLevel.maxErrors) {
      setGameResult('success');
    } else {
      setGameResult('fail');
    }
  };

  // 做出决定（录用/淘汰）- 不再有扫描动画
  const makeDecision = decision => {
    if (!gameRunning || !currentCharacter) return;
    if (decision === 'hire') {
      // 录用逻辑
      const newHiredCount = hiredCount + 1;
      setHiredCount(newHiredCount);

      // 如果录用了不符合条件的人，增加错误计数
      if (!currentCharacter.isReal) {
        setErrorCount(prev => prev + 1);
      }

      // 显示录用对话
      setSpeechText(getRandomSpeech(currentCharacter.speeches.hired));

      // 检查是否达到目标人数
      if (newHiredCount >= currentLevel.targetCount) {
        setTimeout(() => endGame(), 2000);
        return;
      }
    } else {
      // 淘汰逻辑 - 显示淘汰对话
      setSpeechText(getRandomSpeech(currentCharacter.speeches.rejected));
    }

    // 检查错误次数
    if (errorCount >= currentLevel.maxErrors) {
      setTimeout(() => endGame(), 2000);
      return;
    }

    // 继续下一个角色
    setTimeout(() => {
      generateNextCharacter();
    }, 2000); // 2秒后切换到下一个角色
  };

  // 使用探测仪 - 只有这里才有扫描动画
  const useDetector = () => {
    if (!gameRunning || !currentCharacter) return;
    setShowScanner(true);
    setTimeout(() => {
      setShowScanner(false);

      // 判断扫描结果
      const result = currentCharacter.isReal ? 'good' : 'bad';
      setScanResult(result);
      setShowScanResult(true);

      // 3秒后隐藏扫描结果
      setTimeout(() => {
        setShowScanResult(false);
        setScanResult(null);
      }, 3000);
    }, _config_gameConfig__WEBPACK_IMPORTED_MODULE_2__.gameConfig.globalSettings.scanDuration);
  };

  // 添加时间
  const addTime = () => {
    if (!gameRunning) return;
    setTimeLeft(prev => prev + currentLevel.addTimeAmount);
  };

  // 切换资料卡大小
  const toggleInfoCard = () => {
    setInfoCardEnlarged(!infoCardEnlarged);
  };

  // 重新开始游戏
  const restartGame = () => {
    setShowGameOver(false);
    startGame();
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
    className: "game-container",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "background"
    }), gameState === 'start' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "start-screen",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
        className: "game-title",
        children: "\u4E09\u56FD\u6253\u5DE5\u4EBA"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
        className: "game-description",
        children: "\u5728\u4F17\u591A\u5192\u5145\u8005\u4E2D\u62DB\u5230\u771F\u6B63\u7684\u4E09\u56FD\u6253\u5DE5\u4EBA\uFF01 \u4ED4\u7EC6\u67E5\u770B\u8D44\u6599\uFF0C\u4F7F\u7528\u63A2\u6D4B\u4EEA\u8BC6\u522B\u771F\u4F2A\u3002"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "start-button",
        onClick: startGame,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
          className: "start-button-text",
          children: "\u5F00\u59CB\u6E38\u620F"
        })
      })]
    }), gameState === 'playing' && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "title-bar",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
          className: "title",
          src: __webpack_require__(/*! ../../images/背景上部标题(临时工招聘会).png */ "./src/images/背景上部标题(临时工招聘会).png")
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
          className: "timer",
          children: formatTime(timeLeft)
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "game-main",
        children: currentCharacter && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
          className: "character",
          src: __webpack_require__("./src/images sync recursive ^\\.\\/.*$")(`./${currentCharacter.image}`)
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "speech-bubble",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
          className: "speech-text",
          children: speechText
        })
      }), currentCharacter && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: `info-card ${infoCardEnlarged ? 'enlarged' : ''}`,
        onClick: toggleInfoCard,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
          className: "info-content",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
            className: "info-name",
            children: currentCharacter.name
          }), currentCharacter.traits.map((trait, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
            className: "info-item",
            children: trait
          }, index))]
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "bottom-bar",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
          className: "buttons",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
            className: "hire-button",
            src: __webpack_require__(/*! ../../images/录用按钮.png */ "./src/images/录用按钮.png"),
            onClick: () => makeDecision('hire')
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
            className: "reject-button",
            src: __webpack_require__(/*! ../../images/淘汰按钮.png */ "./src/images/淘汰按钮.png"),
            onClick: () => makeDecision('reject')
          })]
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "targets",
        children: Array.from({
          length: currentLevel.targetCount
        }, (_, i) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
          className: `target-slot ${i < hiredCount ? 'filled' : ''}`
        }, i))
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "tools",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
          className: "detector",
          src: __webpack_require__(/*! ../../images/探测图标.png */ "./src/images/探测图标.png"),
          onClick: useDetector
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
          className: "time-add",
          src: __webpack_require__(/*! ../../images/加时图标.png */ "./src/images/加时图标.png"),
          onClick: addTime
        })]
      })]
    }), showScanner && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "scanner",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
        className: "scan-mask",
        src: __webpack_require__(/*! ../../images/扫描时的黑色遮罩.png */ "./src/images/扫描时的黑色遮罩.png")
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
        className: "scan-frame",
        src: __webpack_require__(/*! ../../images/扫描时的框.png */ "./src/images/扫描时的框.png")
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
        className: "scan-bar",
        src: __webpack_require__(/*! ../../images/扫描条(由上向下普速).png */ "./src/images/扫描条(由上向下普速).png")
      })]
    }), showScanResult && scanResult && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "scan-result",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Image, {
        className: "result-bg",
        src: __webpack_require__(/*! ../../images/扫描后弹窗文字的背景框.png */ "./src/images/扫描后弹窗文字的背景框.png")
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
        className: "result-text",
        children: scanResult === 'good' ? '这家伙看上去很不错' : '这家伙看上去不靠谱'
      })]
    }), showGameOver && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "game-over",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
        className: "game-over-title",
        children: gameResult === 'success' ? '招聘成功！' : '招聘失败！'
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
        className: "result-text",
        children: ["\u5F55\u7528\u4EBA\u6570: ", hiredCount, "/", currentLevel.targetCount, '\n', "\u9519\u8BEF\u6B21\u6570: ", errorCount, "/", currentLevel.maxErrors]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "restart-button",
        onClick: restartGame,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.Text, {
          className: "restart-button-text",
          children: "\u91CD\u65B0\u5F00\u59CB"
        })
      })]
    })]
  });
}

/***/ }),

/***/ "./src/config/gameConfig.ts":
/*!**********************************!*\
  !*** ./src/config/gameConfig.ts ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   gameConfig: function() { return /* binding */ gameConfig; }
/* harmony export */ });
/* unused harmony exports characters, requiredTraits */
// 游戏配置文件

// 角色数据
const characters = [
// 真正的三国人物（符合条件）
{
  id: 'zhangfei_real',
  name: '张飞',
  image: '张飞主角色招聘图.png',
  isReal: true,
  traits: ['姓名：张飞', '字：翼德', '籍贯：涿郡', '职业：武将', '特长：使用丈八蛇矛', '性格：勇猛刚烈', '学历：大汉三本毕业'],
  speeches: {
    start: ['老板，我很强的，我抓鹅特别厉害', '老板，很多人冒充我们三国人士，我才是真的', '老板您看，我是大汉三本毕业的'],
    middle: ['老板，请让我和您一起干番大事业', '老板，你看我的宝剑锋利吗', '我还不错吧，老板'],
    hired: ['老板，您眼光真准', '老板,您一看就是做大事的人', '谢谢老板，我一定会好好努力'],
    rejected: ['哎不是，老板，我真能辅佐老板打天下的啊', '你不相信我？也罢', '哎呀，老板你是不是没看清楚啊']
  }
}, {
  id: 'guanyu_real',
  name: '关羽',
  image: '关羽主角色招聘图.png',
  isReal: true,
  traits: ['姓名：关羽', '字：云长', '籍贯：河东解良', '职业：武将', '特长：使用青龙偃月刀', '性格：忠义仁勇', '学历：大汉二本毕业'],
  speeches: {
    start: ['老板，可找着你们了', '老板，你看我合适吗', '请问你们在招打螺丝的吗'],
    middle: ['其实我来贵公司主要是为了体验生活', '老板你一看就是干大事的人', '好热，咱们能不能现在就签合同呀'],
    hired: ['我确实很厉害', '老板，您眼光真准', '谢谢老板，我一定会好好努力'],
    rejected: ['我被淘汰辣？？？', '哎老板你听我说啊，好歹加个v啊，哎老板……', '有什么了不起，好工作多的是']
  }
},
// 冒充者（不符合条件）
{
  id: 'fake_zhangfei_1',
  name: '张飞',
  image: '张飞主角色招聘图.png',
  isReal: false,
  traits: ['姓名：张飞', '字：翼德', '籍贯：涿郡', '职业：武将', '特长：使用丈八蛇矛', '性格：勇猛刚烈', '学历：蓝翔技校毕业' // 错误信息
  ],
  speeches: {
    start: ['不用看，就我了', '老板，我很强的，我抓鹅特别厉害', '真是的，你们位置好难找呀'],
    middle: ['老板，亮亮在你们公司吗', '我还不错吧，老板', '老板你一看就是干大事的人'],
    hired: ['老板，您眼光真准', '我确实很厉害', '谢谢老板，我一定会好好努力'],
    rejected: ['不要我算了，正好另一家开高薪请我呢', '呃，我回去准备一下再来', '我就知道白跑一趟']
  }
}, {
  id: 'fake_zhangfei_2',
  name: '张飞',
  image: '张飞主角色招聘图.png',
  isReal: false,
  traits: ['姓名：张飞', '字：翼德', '籍贯：涿郡', '职业：武将', '特长：使用AK47',
  // 错误信息
  '性格：勇猛刚烈', '学历：大汉三本毕业'],
  speeches: {
    start: ['老板，我很强的，我抓鹅特别厉害', '老板，很多人冒充我们三国人士，我才是真的', '老板您看，我是大汉三本毕业的'],
    middle: ['老板，请让我和您一起干番大事业', '老板，你看我的宝剑锋利吗', '我还不错吧，老板'],
    hired: ['老板，您眼光真准', '老板,您一看就是做大事的人', '谢谢老板，我一定会好好努力'],
    rejected: ['我就知道你在一声声"老板"中迷失了自我', '哎呀，老板你是不是没看清楚啊', '你不相信我？也罢']
  }
}, {
  id: 'fake_guanyu_1',
  name: '关羽',
  image: '关羽主角色招聘图.png',
  isReal: false,
  traits: ['姓名：关羽', '字：云长', '籍贯：河东解良', '职业：武将', '特长：使用青龙偃月刀', '性格：忠义仁勇', '学历：新东方烹饪学校毕业' // 错误信息
  ],
  speeches: {
    start: ['老板，可找着你们了', '老板，你看我合适吗', '请问你们在招打螺丝的吗'],
    middle: ['其实我来贵公司主要是为了体验生活', '老板你一看就是干大事的人', '好热，咱们能不能现在就签合同呀'],
    hired: ['我确实很厉害', '老板，您眼光真准', '谢谢老板，我一定会好好努力'],
    rejected: ['我被淘汰辣？？？', '哎老板你听我说啊，好歹加个v啊，哎老板……', '有什么了不起，好工作多的是']
  }
}];

// 关卡配置
const gameConfig = {
  levels: [{
    level: 1,
    targetCount: 3,
    // 需要招聘3个人
    maxErrors: 2,
    // 最多容错2个
    timeLimit: 120,
    // 2分钟
    addTimeAmount: 60,
    // 加时1分钟
    characters: [characters[0],
    // 真张飞
    characters[2],
    // 假张飞1
    characters[3],
    // 假张飞2
    characters[1],
    // 真关羽
    characters[4] // 假关羽1
    ]
  }, {
    level: 2,
    targetCount: 5,
    maxErrors: 3,
    timeLimit: 180,
    // 3分钟
    addTimeAmount: 60,
    characters: characters // 所有角色
  }],
  globalSettings: {
    defaultAddTime: 60,
    // 默认加时1分钟
    scanDuration: 2000 // 扫描动画2秒
  }
};

// 招聘需要的资料模板
const requiredTraits = {
  zhangfei: ['姓名：张飞', '字：翼德', '籍贯：涿郡', '职业：武将', '特长：使用丈八蛇矛', '性格：勇猛刚烈', '学历：大汉三本毕业'],
  guanyu: ['姓名：关羽', '字：云长', '籍贯：河东解良', '职业：武将', '特长：使用青龙偃月刀', '性格：忠义仁勇', '学历：大汉二本毕业']
};

/***/ }),

/***/ "./src/pages/index/index.tsx":
/*!***********************************!*\
  !*** ./src/pages/index/index.tsx ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

"use strict";
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "webpack/container/remote/@tarojs/runtime");
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./src/pages/index/index.tsx");


var config = {"navigationBarTitleText":"首页"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"], 'pages/index/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"]);


/***/ }),

/***/ "./src/images sync recursive ^\\.\\/.*$":
/*!***********************************!*\
  !*** ./src/images/ sync ^\.\/.*$ ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

var map = {
	"./scan-bar.png": "./src/images/scan-bar.png",
	"./target-slot.png": "./src/images/target-slot.png",
	"./主角色说话弹窗.png": "./src/images/主角色说话弹窗.png",
	"./倒计时框.png": "./src/images/倒计时框.png",
	"./加时图标.png": "./src/images/加时图标.png",
	"./左下角资料信息图.png": "./src/images/左下角资料信息图.png",
	"./左侧需要招到的人数标(招到一个，填黑一个).png": "./src/images/左侧需要招到的人数标(招到一个，填黑一个).png",
	"./张飞主角色招聘图.png": "./src/images/张飞主角色招聘图.png",
	"./录用按钮.png": "./src/images/录用按钮.png",
	"./扫描后弹窗文字的背景框.png": "./src/images/扫描后弹窗文字的背景框.png",
	"./扫描时的框.png": "./src/images/扫描时的框.png",
	"./扫描时的黑色遮罩.png": "./src/images/扫描时的黑色遮罩.png",
	"./扫描条(由上向下普速).png": "./src/images/扫描条(由上向下普速).png",
	"./招聘失败的淘汰盖章.png": "./src/images/招聘失败的淘汰盖章.png",
	"./招聘成功的盖章.png": "./src/images/招聘成功的盖章.png",
	"./探测图标.png": "./src/images/探测图标.png",
	"./桌子.png": "./src/images/桌子.png",
	"./椅子.png": "./src/images/椅子.png",
	"./淘汰按钮.png": "./src/images/淘汰按钮.png",
	"./游戏背景.png": "./src/images/游戏背景.png",
	"./背景上部标题(临时工招聘会).png": "./src/images/背景上部标题(临时工招聘会).png"
};


function webpackContext(req) {
	var id = webpackContextResolve(req);
	return __webpack_require__(id);
}
function webpackContextResolve(req) {
	if(!__webpack_require__.o(map, req)) {
		var e = new Error("Cannot find module '" + req + "'");
		e.code = 'MODULE_NOT_FOUND';
		throw e;
	}
	return map[req];
}
webpackContext.keys = function webpackContextKeys() {
	return Object.keys(map);
};
webpackContext.resolve = webpackContextResolve;
module.exports = webpackContext;
webpackContext.id = "./src/images sync recursive ^\\.\\/.*$";

/***/ }),

/***/ "./src/images/scan-bar.png":
/*!*********************************!*\
  !*** ./src/images/scan-bar.png ***!
  \*********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/scan-bar.png";

/***/ }),

/***/ "./src/images/target-slot.png":
/*!************************************!*\
  !*** ./src/images/target-slot.png ***!
  \************************************/
/***/ (function(module) {

"use strict";
module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAzCAMAAADivasmAAAAolBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8ELnaCAAAANXRSTlMA6hL17gncjEQGMPzjzq1uPQ758tIqH6KelH55Ft/DvGVgW1YjG7axp5mDSzo2clDXJsjUh2tpK8oAAAKASURBVEjHjdXX1qowEAXgASlSFBQVFAv2Y//bfv9XO0FCKEHxuzEs1kAy2UFqML9tzqcwjMzE39MHvr2DBqHzM7u1FHT7HdQttxa9dF0hpa2m8frx2N7No4tUb0PN1KnDbivRY07CoGt+gTmOqcEtvefGc6rR1730zQ0v2gaAY/KC3fp0PESeTU+Wp7CiM9UkAIxuNt5PAzw5R170nS5yShVrAKs979qi1GSPv2jKLkwq8YdA3+IV1TbfKTNjY4+EPevlgVdMFFRdKHMGhl1R0metH/DxP9R85XdCttp8vGE9zPs+dlC3zvfoS7RAN0qznEFyKK14mD3aA0ZWMUfJQicuYk3KX/Kg3A8kgTgBuyB7zYWtnYQRJJ0d5U5ZCPqVfq8gUdQih89pDpTyYyiCZESVSVypy5JSTickJjFFQ2d0r2Z04KLGsalwSXsWiUiIfS2pB3jSYfP8EY9pTsxIrZy3BRQy0JlQmdWvVFRvUg8aKdDqZ/dcxP+fSlXLrESVvmWmwaocN7wSNZfMSba/XSc6UWOJwXdS0FVmoOuWPniOpLWwMtilJiaHhVLhLmO71jGWtC1x6p+GBk74LbLcYRn2ikTYI7yg5U99sCaSDyx5TBUIr06zCdxJd/npnLt4w/Hz42g/Pxwz6QjLDJUojf2vReSzy+dPizjNX/Zj9YAkvWph6GQPeSDX6elUA7Tp0hE45TuKeIxWCVtJwIPNhp0QrY4Ga1TxrflQzxJBGeEjgU3CWEM7EUbxP9TOo4pLe01MNVcXbw0TkkxWb/fepyb3l01wpio120XNKzpc6bWxKS0pCH16T71Ev4FYs9Hf7OkDqr2J/0zTnCX+nGT/AWmQ+9LiKmNuAAAAAElFTkSuQmCC";

/***/ }),

/***/ "./src/images/主角色说话弹窗.png":
/*!********************************!*\
  !*** ./src/images/主角色说话弹窗.png ***!
  \********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/主角色说话弹窗.png";

/***/ }),

/***/ "./src/images/倒计时框.png":
/*!*****************************!*\
  !*** ./src/images/倒计时框.png ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/倒计时框.png";

/***/ }),

/***/ "./src/images/加时图标.png":
/*!*****************************!*\
  !*** ./src/images/加时图标.png ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/加时图标.png";

/***/ }),

/***/ "./src/images/左下角资料信息图.png":
/*!*********************************!*\
  !*** ./src/images/左下角资料信息图.png ***!
  \*********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/左下角资料信息图.png";

/***/ }),

/***/ "./src/images/左侧需要招到的人数标(招到一个，填黑一个).png":
/*!**********************************************!*\
  !*** ./src/images/左侧需要招到的人数标(招到一个，填黑一个).png ***!
  \**********************************************/
/***/ (function(module) {

"use strict";
module.exports = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAzCAMAAADivasmAAAAolBMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8ELnaCAAAANXRSTlMA6hL17gncjEQGMPzjzq1uPQ758tIqH6KelH55Ft/DvGVgW1YjG7axp5mDSzo2clDXJsjUh2tpK8oAAAKASURBVEjHjdXX1qowEAXgASlSFBQVFAv2Y//bfv9XO0FCKEHxuzEs1kAy2UFqML9tzqcwjMzE39MHvr2DBqHzM7u1FHT7HdQttxa9dF0hpa2m8frx2N7No4tUb0PN1KnDbivRY07CoGt+gTmOqcEtvefGc6rR1730zQ0v2gaAY/KC3fp0PESeTU+Wp7CiM9UkAIxuNt5PAzw5R170nS5yShVrAKs979qi1GSPv2jKLkwq8YdA3+IV1TbfKTNjY4+EPevlgVdMFFRdKHMGhl1R0metH/DxP9R85XdCttp8vGE9zPs+dlC3zvfoS7RAN0qznEFyKK14mD3aA0ZWMUfJQicuYk3KX/Kg3A8kgTgBuyB7zYWtnYQRJJ0d5U5ZCPqVfq8gUdQih89pDpTyYyiCZESVSVypy5JSTickJjFFQ2d0r2Z04KLGsalwSXsWiUiIfS2pB3jSYfP8EY9pTsxIrZy3BRQy0JlQmdWvVFRvUg8aKdDqZ/dcxP+fSlXLrESVvmWmwaocN7wSNZfMSba/XSc6UWOJwXdS0FVmoOuWPniOpLWwMtilJiaHhVLhLmO71jGWtC1x6p+GBk74LbLcYRn2ikTYI7yg5U99sCaSDyx5TBUIr06zCdxJd/npnLt4w/Hz42g/Pxwz6QjLDJUojf2vReSzy+dPizjNX/Zj9YAkvWph6GQPeSDX6elUA7Tp0hE45TuKeIxWCVtJwIPNhp0QrY4Ga1TxrflQzxJBGeEjgU3CWEM7EUbxP9TOo4pLe01MNVcXbw0TkkxWb/fepyb3l01wpio120XNKzpc6bWxKS0pCH16T71Ev4FYs9Hf7OkDqr2J/0zTnCX+nGT/AWmQ+9LiKmNuAAAAAElFTkSuQmCC";

/***/ }),

/***/ "./src/images/张飞主角色招聘图.png":
/*!*********************************!*\
  !*** ./src/images/张飞主角色招聘图.png ***!
  \*********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/张飞主角色招聘图.png";

/***/ }),

/***/ "./src/images/录用按钮.png":
/*!*****************************!*\
  !*** ./src/images/录用按钮.png ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/录用按钮.png";

/***/ }),

/***/ "./src/images/扫描后弹窗文字的背景框.png":
/*!************************************!*\
  !*** ./src/images/扫描后弹窗文字的背景框.png ***!
  \************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/扫描后弹窗文字的背景框.png";

/***/ }),

/***/ "./src/images/扫描时的框.png":
/*!******************************!*\
  !*** ./src/images/扫描时的框.png ***!
  \******************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/扫描时的框.png";

/***/ }),

/***/ "./src/images/扫描时的黑色遮罩.png":
/*!*********************************!*\
  !*** ./src/images/扫描时的黑色遮罩.png ***!
  \*********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/扫描时的黑色遮罩.png";

/***/ }),

/***/ "./src/images/扫描条(由上向下普速).png":
/*!************************************!*\
  !*** ./src/images/扫描条(由上向下普速).png ***!
  \************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/扫描条(由上向下普速).png";

/***/ }),

/***/ "./src/images/招聘失败的淘汰盖章.png":
/*!**********************************!*\
  !*** ./src/images/招聘失败的淘汰盖章.png ***!
  \**********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/招聘失败的淘汰盖章.png";

/***/ }),

/***/ "./src/images/招聘成功的盖章.png":
/*!********************************!*\
  !*** ./src/images/招聘成功的盖章.png ***!
  \********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/招聘成功的盖章.png";

/***/ }),

/***/ "./src/images/探测图标.png":
/*!*****************************!*\
  !*** ./src/images/探测图标.png ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/探测图标.png";

/***/ }),

/***/ "./src/images/桌子.png":
/*!***************************!*\
  !*** ./src/images/桌子.png ***!
  \***************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/桌子.png";

/***/ }),

/***/ "./src/images/椅子.png":
/*!***************************!*\
  !*** ./src/images/椅子.png ***!
  \***************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/椅子.png";

/***/ }),

/***/ "./src/images/淘汰按钮.png":
/*!*****************************!*\
  !*** ./src/images/淘汰按钮.png ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/淘汰按钮.png";

/***/ }),

/***/ "./src/images/游戏背景.png":
/*!*****************************!*\
  !*** ./src/images/游戏背景.png ***!
  \*****************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/游戏背景.png";

/***/ }),

/***/ "./src/images/背景上部标题(临时工招聘会).png":
/*!***************************************!*\
  !*** ./src/images/背景上部标题(临时工招聘会).png ***!
  \***************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
module.exports = __webpack_require__.p + "images/背景上部标题(临时工招聘会).png";

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","common"], function() { return __webpack_exec__("./src/pages/index/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map