"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/index/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./src/pages/index/index.tsx":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./src/pages/index/index.tsx ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ Index; }
/* harmony export */ });
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "webpack/container/remote/@tarojs/taro");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "webpack/container/remote/react");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ "webpack/container/remote/react/jsx-runtime");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__);





// 角色数据类型定义

// 角色数据
const characters = [{
  name: "张飞",
  traits: ["黑发", "墨镜", "皮肤黝黑", "易怒", "擅长使矛"],
  isReal: true,
  image: __webpack_require__(/*! ../../images/张飞主角色招聘图.png */ "./src/images/张飞主角色招聘图.png"),
  startDialogues: ["老板，我很强的，我抓鹅特别厉害", "老板，你看我合适吗", "老板，请让我和您一起干番大事业", "老板，你看我的宝剑锋利吗"],
  hireDialogues: ["老板，您眼光真准", "老板，您一看就是做大事的人", "谢谢老板，我一定会好好努力", "我确实很厉害"],
  rejectDialogues: ["我被淘汰辣？？？", "哎老板你听我说啊，好歹加个v啊，哎老板……", "不要我算了，正好另一家开高薪请我呢", "你不相信我？也罢"]
}, {
  name: "假张飞",
  traits: ["黑发", "墨镜", "皮肤白皙", "易怒", "擅长使矛"],
  isReal: false,
  image: __webpack_require__(/*! ../../images/张飞主角色招聘图.png */ "./src/images/张飞主角色招聘图.png"),
  startDialogues: ["老板，很多人冒充我们三国人士，我才是真的", "老板您看，我是大汉三本毕业的", "不用看，就我了", "老板，可找着你们了"],
  hireDialogues: ["老板，您眼光真准", "老板，您一看就是做大事的人", "谢谢老板，我一定会好好努力", "我确实很厉害"],
  rejectDialogues: ["呃，我回去准备一下再来", "哎呀，老板你是不是没看清楚啊", "哎不是，老板，我真能辅佐老板打天下的啊", "我就知道白跑一趟"]
}];
function Index() {
  // 游戏状态
  const [gameRunning, setGameRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [timeLeft, setTimeLeft] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(120); // 2分钟
  const [hiredCount, setHiredCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);
  const [wrongHires, setWrongHires] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);
  const [currentCharacter, setCurrentCharacter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
  const [speechText, setSpeechText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)("");
  const [showScanner, setShowScanner] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [showResult, setShowResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [resultType, setResultType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('success');
  const [showGameOver, setShowGameOver] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [infoCardEnlarged, setInfoCardEnlarged] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const targetCount = 5;
  const maxWrongHires = 3;
  (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__.useLoad)(() => {
    console.log('三国打工人游戏加载完成');
  });

  // 计时器效果
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    let interval;
    if (gameRunning && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            endGame();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [gameRunning, timeLeft]);

  // 格式化时间显示
  const formatTime = seconds => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // 开始游戏
  const startGame = () => {
    setGameRunning(true);
    setHiredCount(0);
    setWrongHires(0);
    setTimeLeft(120);
    setShowGameOver(false);
    nextCharacter();
  };

  // 下一个角色
  const nextCharacter = () => {
    const randomIndex = Math.floor(Math.random() * characters.length);
    const character = characters[randomIndex];
    setCurrentCharacter(character);

    // 随机选择开始对话
    const randomDialogueIndex = Math.floor(Math.random() * character.startDialogues.length);
    setSpeechText(character.startDialogues[randomDialogueIndex]);
  };

  // 做出决定（录用/淘汰）
  const makeDecision = decision => {
    if (!gameRunning || !currentCharacter) return;

    // 显示扫描动画
    setShowScanner(true);
    setTimeout(() => {
      setShowScanner(false);
      let isCorrectDecision = false;
      if (decision === 'hire' && currentCharacter.isReal) {
        // 正确录用
        setResultType('success');
        setHiredCount(prev => prev + 1);
        isCorrectDecision = true;
      } else if (decision === 'reject' && !currentCharacter.isReal) {
        // 正确淘汰
        setResultType('fail');
        isCorrectDecision = true;
      } else if (decision === 'hire' && !currentCharacter.isReal) {
        // 错误录用
        setResultType('fail');
        setWrongHires(prev => prev + 1);
      } else {
        // 错误淘汰
        setResultType('fail');
      }

      // 显示结果
      setShowResult(true);

      // 更新对话
      const dialogues = decision === 'hire' ? currentCharacter.hireDialogues : currentCharacter.rejectDialogues;
      const randomDialogueIndex = Math.floor(Math.random() * dialogues.length);
      setSpeechText(dialogues[randomDialogueIndex]);

      // 检查游戏是否结束
      const newHiredCount = decision === 'hire' && currentCharacter.isReal ? hiredCount + 1 : hiredCount;
      const newWrongHires = decision === 'hire' && !currentCharacter.isReal ? wrongHires + 1 : wrongHires;
      if (newHiredCount >= targetCount || newWrongHires >= maxWrongHires) {
        setTimeout(() => {
          endGame();
        }, 1500);
        return;
      }

      // 延迟后进入下一个角色
      setTimeout(() => {
        setShowResult(false);
        nextCharacter();
      }, 1500);
    }, 2000); // 扫描动画持续2秒
  };

  // 游戏结束
  const endGame = () => {
    setGameRunning(false);
    setShowGameOver(true);
  };

  // 重新开始游戏
  const restartGame = () => {
    setShowGameOver(false);
    startGame();
  };

  // 使用探测仪
  const useDetector = () => {
    if (!gameRunning) return;
    // 这里可以添加探测仪功能
    console.log('探测到可疑特征！请仔细检查资料信息。');
  };

  // 添加时间
  const addTime = () => {
    if (!gameRunning) return;
    setTimeLeft(prev => prev + 60);
    console.log('已增加60秒时间！');
  };

  // 切换资料卡大小
  const toggleInfoCard = () => {
    setInfoCardEnlarged(!infoCardEnlarged);
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
    className: "game-container",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "background"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "title-bar",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
        className: "title",
        children: "\u4E09\u56FD\u6253\u5DE5\u4EBA"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "timer",
        children: formatTime(timeLeft)
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "game-main",
      children: currentCharacter && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "character",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
          style: {
            fontSize: '60rpx',
            color: 'white'
          },
          children: "\uD83D\uDC64"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
          style: {
            fontSize: '24rpx',
            color: 'white',
            marginTop: '10rpx'
          },
          children: currentCharacter.name
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "speech-bubble",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
        className: "speech-text",
        children: speechText
      })
    }), currentCharacter && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: `info-card ${infoCardEnlarged ? 'enlarged' : ''}`,
      onClick: toggleInfoCard,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "info-content",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
          className: "info-name",
          children: currentCharacter.name
        }), currentCharacter.traits.map((trait, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
          className: "info-item",
          children: ["\u2022 ", trait]
        }, index))]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "bottom-bar",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "buttons",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
          className: "hire-button",
          onClick: () => makeDecision('hire'),
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
            style: {
              color: 'white',
              fontSize: '28rpx',
              fontWeight: 'bold'
            },
            children: "\u5F55\u7528"
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
          className: "reject-button",
          onClick: () => makeDecision('reject'),
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
            style: {
              color: 'white',
              fontSize: '28rpx',
              fontWeight: 'bold'
            },
            children: "\u6DD8\u6C70"
          })
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "targets",
      children: Array.from({
        length: targetCount
      }, (_, i) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: `target-slot ${i < hiredCount ? 'filled' : ''}`
      }, i))
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "tools",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "detector",
        onClick: useDetector
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "time-add",
        onClick: addTime
      })]
    }), showScanner && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "scanner",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "scan-frame"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "scan-bar"
      })]
    }), showResult && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: `result ${resultType}`
    }), !gameRunning && !showGameOver && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "start-screen",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
        className: "game-title",
        children: "\u4E09\u56FD\u6253\u5DE5\u4EBA"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
        className: "game-description",
        children: ["\u5728\u4F17\u591A\u5192\u5145\u8005\u4E2D\u62DB\u5230\u771F\u6B63\u7684\u4E09\u56FD\u6253\u5DE5\u4EBA\uFF01", '\n', "\u4ED4\u7EC6\u5BF9\u6BD4\u8D44\u6599\uFF0C\u505A\u51FA\u660E\u667A\u7684\u9009\u62E9\u3002"]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "start-button",
        onClick: startGame,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
          className: "start-button-text",
          children: "\u5F00\u59CB\u62DB\u8058"
        })
      })]
    }), showGameOver && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
      className: "game-over",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
        className: "game-over-title",
        children: "\u6E38\u620F\u7ED3\u675F"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
        className: "result-text",
        children: hiredCount >= targetCount ? `恭喜！你成功招聘了 ${hiredCount} 名真正的三国打工人！` : wrongHires >= maxWrongHires ? `你错误录用了 ${wrongHires} 名冒充者，招聘失败！` : `时间到！你只招聘到了 ${hiredCount}/${targetCount} 名三国打工人。`
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.View, {
        className: "restart-button",
        onClick: restartGame,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_3__.Text, {
          className: "restart-button-text",
          children: "\u518D\u6765\u4E00\u6B21"
        })
      })]
    })]
  });
}

/***/ }),

/***/ "./src/pages/index/index.tsx":
/*!***********************************!*\
  !*** ./src/pages/index/index.tsx ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/runtime */ "webpack/container/remote/@tarojs/runtime");
/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./src/pages/index/index.tsx");


var config = {"navigationBarTitleText":"首页"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_0__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"], 'pages/index/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_index_index_index_tsx__WEBPACK_IMPORTED_MODULE_1__["default"]);


/***/ }),

/***/ "./src/images/张飞主角色招聘图.png":
/*!*********************************!*\
  !*** ./src/images/张飞主角色招聘图.png ***!
  \*********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

module.exports = __webpack_require__.p + "images/张飞主角色招聘图.png";

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","common"], function() { return __webpack_exec__("./src/pages/index/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map