{"version": 3, "file": "app.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACZA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://sanguo-game/._src_app.ts", "webpack://sanguo-game/./src/app.ts?7071"], "sourcesContent": ["import { useLaunch } from '@tarojs/taro';\nimport './app.scss';\nfunction App({\n  children\n}) {\n  useLaunch(() => {\n    console.log('App launched.');\n  });\n\n  // children 是将要会渲染的页面\n  return children;\n}\nexport default App;", "import '@tarojs/plugin-platform-weapp/dist/runtime'\n\nimport { window } from '@tarojs/runtime'\nimport { createReactApp } from '@tarojs/plugin-framework-react/dist/runtime'\nimport { initPxTransform } from '@tarojs/taro'\n\nimport component from \"!!../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=app!./app.ts\"\n\nimport * as React from 'react'\nimport ReactDOM from 'react-dom'\n\nvar config = {\"pages\":[\"pages/index/index\"],\"window\":{\"backgroundTextStyle\":\"light\",\"navigationBarBackgroundColor\":\"#fff\",\"navigationBarTitleText\":\"WeChat\",\"navigationBarTextStyle\":\"black\"}};\nwindow.__taroAppConfig = config\nvar inst = App(createReactApp(component, React, ReactDOM, config))\n\ninitPxTransform({\n  designWidth: 750,\n  deviceRatio: {\"375\":2,\"640\":1.17,\"750\":1,\"828\":0.905},\n  baseFontSize: 20,\n  unitPrecision: undefined,\n  targetUnit: undefined\n})\n"], "names": [], "sourceRoot": ""}