import { require_react } from "./chunk-XDFXK7K5.js";
import { __commonJS } from "./chunk-QRPWKJ4C.js";
// node_modules/react/cjs/react-jsx-runtime.production.min.js
var require_react_jsx_runtime_production_min = __commonJS({
    "node_modules/react/cjs/react-jsx-runtime.production.min.js" (exports) {
        "use strict";
        var f = require_react();
        var k = Symbol.for("react.element");
        var l = Symbol.for("react.fragment");
        var m = Object.prototype.hasOwnProperty;
        var n = f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner;
        var p = {
            key: true,
            ref: true,
            __self: true,
            __source: true
        };
        function q(c, a, g) {
            var b, d = {}, e = null, h = null;
            void 0 !== g && (e = "" + g);
            void 0 !== a.key && (e = "" + a.key);
            void 0 !== a.ref && (h = a.ref);
            for(b in a)m.call(a, b) && !p.hasOwnProperty(b) && (d[b] = a[b]);
            if (c && c.defaultProps) for(b in a = c.defaultProps, a)void 0 === d[b] && (d[b] = a[b]);
            return {
                $$typeof: k,
                type: c,
                key: e,
                ref: h,
                props: d,
                _owner: n.current
            };
        }
        exports.Fragment = l;
        exports.jsx = q;
        exports.jsxs = q;
    }
});
// entry:react_jsx-runtime
var require_react_jsx_runtime = __commonJS({
    "entry:react_jsx-runtime" (exports, module) {
        module.exports = require_react_jsx_runtime_production_min();
    }
});
export default require_react_jsx_runtime(); /*! Bundled license information:

react/cjs/react-jsx-runtime.production.min.js:
  (**
   * @license React
   * react-jsx-runtime.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/ 
