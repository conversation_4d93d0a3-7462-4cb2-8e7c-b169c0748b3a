{"version": 3, "file": "pages/index/index.js", "mappings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cA;;AAgCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AAKA;AAKA;AAKA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AASA;AACA;AAKA;AAKA;AAKA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAEA;AACA;AAKA;AAKA;AAKA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AAKA;AACA;AAGA;AACA;AAKA;AAKA;AAKA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AAOA;AAEA;AACA;AAKA;AAKA;AAKA;AAKA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AASA;AASA;;;;;;;;;;;;;;ACnRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;ACdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AC1CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://sanguo-game/./src/pages/index/index.tsx?acc1", "webpack://sanguo-game/._src_config_gameConfig.ts", "webpack://sanguo-game/._src_pages_index_index.tsx", "webpack://sanguo-game/._src_images_ sync ^_.__.*$", "webpack://sanguo-game/._src_images_ sync ^_.__.*_.png$"], "sourcesContent": ["import { View, Text, Image } from '@tarojs/components';\nimport { useLoad } from '@tarojs/taro';\nimport { useEffect, useState } from 'react';\nimport './index.scss';\nimport { gameConfig } from '../../config/gameConfig';\n\n// 游戏状态类型\nimport { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from \"react/jsx-runtime\";\nexport default function Index() {\n  // 游戏状态\n  const [gameState, setGameState] = useState('start');\n  const [currentLevel, setCurrentLevel] = useState(gameConfig.levels[0]);\n  const [currentCharacter, setCurrentCharacter] = useState(null);\n  const [speechText, setSpeechText] = useState('');\n\n  // 游戏数据\n  const [timeLeft, setTimeLeft] = useState(0);\n  const [hiredCount, setHiredCount] = useState(0);\n  const [errorCount, setErrorCount] = useState(0);\n  const [gameRunning, setGameRunning] = useState(false);\n\n  // UI状态\n  const [infoCardEnlarged, setInfoCardEnlarged] = useState(false);\n  const [showScanner, setShowScanner] = useState(false);\n  const [scanResult, setScanResult] = useState(null);\n  const [showScanResult, setShowScanResult] = useState(false);\n  const [showGameOver, setShowGameOver] = useState(false);\n  const [gameResult, setGameResult] = useState('fail');\n\n  // 拖拽状态\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragType, setDragType] = useState(null);\n  const [dragPosition, setDragPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const [isOverInfoCard, setIsOverInfoCard] = useState(false);\n  const [stampProgress, setStampProgress] = useState(0);\n  const [isStamping, setIsStamping] = useState(false);\n  const [showStampResult, setShowStampResult] = useState(false);\n\n  // 页面加载\n  useLoad(() => {\n    console.log('三国打工人游戏加载完成');\n    initializeGame();\n  });\n\n  // 初始化游戏\n  const initializeGame = () => {\n    setCurrentLevel(gameConfig.levels[0]);\n    setTimeLeft(gameConfig.levels[0].timeLimit);\n  };\n\n  // 获取随机对话\n  const getRandomSpeech = speeches => {\n    return speeches[Math.floor(Math.random() * speeches.length)];\n  };\n\n  // 生成下一个角色\n  const generateNextCharacter = () => {\n    if (!currentLevel) return;\n    const availableCharacters = currentLevel.characters;\n    const randomCharacter = availableCharacters[Math.floor(Math.random() * availableCharacters.length)];\n    setCurrentCharacter(randomCharacter);\n    setSpeechText(getRandomSpeech(randomCharacter.speeches.start));\n  };\n\n  // 计时器效果\n  useEffect(() => {\n    let interval;\n    if (gameRunning && timeLeft > 0) {\n      interval = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev <= 1) {\n            endGame();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [gameRunning, timeLeft]);\n\n  // 格式化时间显示\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 开始游戏\n  const startGame = () => {\n    setGameState('playing');\n    setGameRunning(true);\n    setHiredCount(0);\n    setErrorCount(0);\n    setTimeLeft(currentLevel.timeLimit);\n    setShowGameOver(false);\n    generateNextCharacter();\n  };\n\n  // 结束游戏\n  const endGame = () => {\n    setGameRunning(false);\n    setGameState('gameOver');\n    setShowGameOver(true);\n\n    // 判断游戏结果\n    if (hiredCount >= currentLevel.targetCount && errorCount <= currentLevel.maxErrors) {\n      setGameResult('success');\n    } else {\n      setGameResult('fail');\n    }\n  };\n\n  // 做出决定（录用/淘汰）- 不再有扫描动画\n  const makeDecision = decision => {\n    if (!gameRunning || !currentCharacter) return;\n    if (decision === 'hire') {\n      // 录用逻辑\n      const newHiredCount = hiredCount + 1;\n      setHiredCount(newHiredCount);\n\n      // 如果录用了不符合条件的人，增加错误计数\n      if (!currentCharacter.isReal) {\n        setErrorCount(prev => prev + 1);\n      }\n\n      // 显示录用对话\n      setSpeechText(getRandomSpeech(currentCharacter.speeches.hired));\n\n      // 检查是否达到目标人数\n      if (newHiredCount >= currentLevel.targetCount) {\n        setTimeout(() => endGame(), 2000);\n        return;\n      }\n    } else {\n      // 淘汰逻辑 - 显示淘汰对话\n      setSpeechText(getRandomSpeech(currentCharacter.speeches.rejected));\n    }\n\n    // 检查错误次数\n    if (errorCount >= currentLevel.maxErrors) {\n      setTimeout(() => endGame(), 2000);\n      return;\n    }\n\n    // 继续下一个角色\n    setTimeout(() => {\n      generateNextCharacter();\n    }, 2000); // 2秒后切换到下一个角色\n  };\n\n  // 使用探测仪 - 只有这里才有扫描动画\n  const useDetector = () => {\n    if (!gameRunning || !currentCharacter) return;\n    setShowScanner(true);\n    setTimeout(() => {\n      setShowScanner(false);\n\n      // 判断扫描结果\n      const result = currentCharacter.isReal ? 'good' : 'bad';\n      setScanResult(result);\n      setShowScanResult(true);\n\n      // 3秒后隐藏扫描结果\n      setTimeout(() => {\n        setShowScanResult(false);\n        setScanResult(null);\n      }, 3000);\n    }, gameConfig.globalSettings.scanDuration);\n  };\n\n  // 添加时间\n  const addTime = () => {\n    if (!gameRunning) return;\n    setTimeLeft(prev => prev + currentLevel.addTimeAmount);\n  };\n\n  // 切换资料卡大小\n  const toggleInfoCard = () => {\n    setInfoCardEnlarged(!infoCardEnlarged);\n  };\n\n  // 重新开始游戏\n  const restartGame = () => {\n    setShowGameOver(false);\n    startGame();\n  };\n\n  // 拖拽开始\n  const handleDragStart = (type, event) => {\n    if (!gameRunning || !currentCharacter) return;\n    setIsDragging(true);\n    setDragType(type);\n\n    // 获取触摸位置\n    const touch = event.touches[0];\n    setDragPosition({\n      x: touch.clientX,\n      y: touch.clientY\n    });\n  };\n\n  // 拖拽移动\n  const handleDragMove = event => {\n    if (!isDragging) return;\n    const touch = event.touches[0];\n    setDragPosition({\n      x: touch.clientX,\n      y: touch.clientY\n    });\n\n    // 简化的区域检测 - 使用屏幕坐标\n    const screenWidth = window.innerWidth || 375;\n    const screenHeight = window.innerHeight || 667;\n\n    // 信息卡大致位置 (左下角区域)\n    const cardLeft = screenWidth * 0.05;\n    const cardRight = screenWidth * 0.4;\n    const cardTop = screenHeight * 0.4;\n    const cardBottom = screenHeight * 0.85;\n    const isOver = touch.clientX >= cardLeft && touch.clientX <= cardRight && touch.clientY >= cardTop && touch.clientY <= cardBottom;\n    if (isOver && !isOverInfoCard) {\n      setIsOverInfoCard(true);\n      startStamping();\n    } else if (!isOver && isOverInfoCard) {\n      setIsOverInfoCard(false);\n      cancelStamping();\n    }\n  };\n\n  // 拖拽结束\n  const handleDragEnd = () => {\n    if (!isDragging) return;\n    setIsDragging(false);\n    setDragType(null);\n    setIsOverInfoCard(false);\n    cancelStamping();\n  };\n\n  // 开始盖章进度\n  const startStamping = () => {\n    if (isStamping) return;\n    setIsStamping(true);\n    setStampProgress(0);\n    const progressInterval = setInterval(() => {\n      setStampProgress(prev => {\n        if (prev >= 100) {\n          clearInterval(progressInterval);\n          completeStamping();\n          return 100;\n        }\n        return prev + 10; // 每100ms增加10%，总共1秒\n      });\n    }, 100)\n\n    // 保存interval引用以便取消\n    ;\n    window.stampInterval = progressInterval;\n  };\n\n  // 取消盖章\n  const cancelStamping = () => {\n    setIsStamping(false);\n    setStampProgress(0);\n    if (window.stampInterval) {\n      clearInterval(window.stampInterval);\n      window.stampInterval = null;\n    }\n  };\n\n  // 完成盖章\n  const completeStamping = () => {\n    if (!dragType || !currentCharacter) return;\n    setIsStamping(false);\n    setIsDragging(false);\n    setShowStampResult(true);\n\n    // 执行决定\n    makeDecision(dragType);\n\n    // 2秒后隐藏盖章结果\n    setTimeout(() => {\n      setShowStampResult(false);\n    }, 2000);\n  };\n  return /*#__PURE__*/_jsxs(View, {\n    className: \"game-container\",\n    onTouchMove: handleDragMove,\n    onTouchEnd: handleDragEnd,\n    children: [/*#__PURE__*/_jsx(View, {\n      className: \"background\"\n    }), gameState === 'start' && /*#__PURE__*/_jsxs(View, {\n      className: \"start-screen\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"game-title\",\n        children: \"\\u4E09\\u56FD\\u6253\\u5DE5\\u4EBA\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"game-description\",\n        children: \"\\u5728\\u4F17\\u591A\\u5192\\u5145\\u8005\\u4E2D\\u62DB\\u5230\\u771F\\u6B63\\u7684\\u4E09\\u56FD\\u6253\\u5DE5\\u4EBA\\uFF01 \\u4ED4\\u7EC6\\u67E5\\u770B\\u8D44\\u6599\\uFF0C\\u4F7F\\u7528\\u63A2\\u6D4B\\u4EEA\\u8BC6\\u522B\\u771F\\u4F2A\\u3002\"\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"start-button\",\n        onClick: startGame,\n        children: /*#__PURE__*/_jsx(Text, {\n          className: \"start-button-text\",\n          children: \"\\u5F00\\u59CB\\u6E38\\u620F\"\n        })\n      })]\n    }), gameState === 'playing' && /*#__PURE__*/_jsxs(_Fragment, {\n      children: [/*#__PURE__*/_jsxs(View, {\n        className: \"title-bar\",\n        children: [/*#__PURE__*/_jsx(Image, {\n          className: \"title\",\n          src: require('../../images/背景上部标题(临时工招聘会).png')\n        }), /*#__PURE__*/_jsx(View, {\n          className: \"timer\",\n          children: formatTime(timeLeft)\n        })]\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"game-main\",\n        children: currentCharacter && /*#__PURE__*/_jsx(Image, {\n          className: \"character\",\n          src: require(`../../images/${currentCharacter.image}`)\n        })\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"speech-bubble\",\n        children: /*#__PURE__*/_jsx(Text, {\n          className: \"speech-text\",\n          children: speechText\n        })\n      }), currentCharacter && /*#__PURE__*/_jsxs(View, {\n        className: `info-card ${infoCardEnlarged ? 'enlarged' : ''} ${isOverInfoCard ? 'drag-target' : ''}`,\n        onClick: toggleInfoCard,\n        children: [/*#__PURE__*/_jsxs(View, {\n          className: \"info-content\",\n          children: [/*#__PURE__*/_jsx(Text, {\n            className: \"info-name\",\n            children: currentCharacter.name\n          }), currentCharacter.traits.map((trait, index) => /*#__PURE__*/_jsx(Text, {\n            className: \"info-item\",\n            children: trait\n          }, index))]\n        }), isStamping && isOverInfoCard && /*#__PURE__*/_jsxs(View, {\n          className: \"stamp-progress-container\",\n          children: [/*#__PURE__*/_jsx(View, {\n            className: \"stamp-progress-bg\",\n            children: /*#__PURE__*/_jsx(View, {\n              className: \"stamp-progress-fill\",\n              style: {\n                transform: `rotate(${stampProgress / 100 * 360}deg)`\n              }\n            })\n          }), /*#__PURE__*/_jsxs(Text, {\n            className: \"stamp-progress-text\",\n            children: [Math.round(stampProgress), \"%\"]\n          })]\n        })]\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"bottom-bar\",\n        children: /*#__PURE__*/_jsxs(View, {\n          className: \"buttons\",\n          children: [/*#__PURE__*/_jsx(Image, {\n            className: \"hire-button\",\n            src: require('../../images/录用按钮.png'),\n            onTouchStart: e => handleDragStart('hire', e),\n            onTouchMove: handleDragMove,\n            onTouchEnd: handleDragEnd\n          }), /*#__PURE__*/_jsx(Image, {\n            className: \"reject-button\",\n            src: require('../../images/淘汰按钮.png'),\n            onTouchStart: e => handleDragStart('reject', e),\n            onTouchMove: handleDragMove,\n            onTouchEnd: handleDragEnd\n          })]\n        })\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"targets\",\n        children: Array.from({\n          length: currentLevel.targetCount\n        }, (_, i) => /*#__PURE__*/_jsx(View, {\n          className: `target-slot ${i < hiredCount ? 'filled' : ''}`\n        }, i))\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \"tools\",\n        children: [/*#__PURE__*/_jsx(Image, {\n          className: \"detector\",\n          src: require('../../images/探测图标.png'),\n          onClick: useDetector\n        }), /*#__PURE__*/_jsx(Image, {\n          className: \"time-add\",\n          src: require('../../images/加时图标.png'),\n          onClick: addTime\n        })]\n      })]\n    }), isDragging && dragType && /*#__PURE__*/_jsx(View, {\n      className: \"dragging-stamp\",\n      style: {\n        left: `${dragPosition.x - 40}px`,\n        top: `${dragPosition.y - 40}px`\n      },\n      children: /*#__PURE__*/_jsx(Image, {\n        className: \"stamp-image\",\n        src: require(`../../images/${dragType === 'hire' ? '录用按钮' : '淘汰按钮'}.png`)\n      })\n    }), showStampResult && dragType && /*#__PURE__*/_jsx(View, {\n      className: \"stamp-result-overlay\",\n      children: /*#__PURE__*/_jsxs(View, {\n        className: \"stamp-result-content\",\n        children: [/*#__PURE__*/_jsx(Text, {\n          className: \"stamp-result-text\",\n          children: dragType === 'hire' ? '录用成功！' : '淘汰成功！'\n        }), /*#__PURE__*/_jsx(View, {\n          className: `stamp-result-icon ${dragType}`,\n          children: /*#__PURE__*/_jsx(Text, {\n            className: \"stamp-icon-text\",\n            children: dragType === 'hire' ? '✓' : '✗'\n          })\n        })]\n      })\n    }), showScanner && /*#__PURE__*/_jsxs(View, {\n      className: \"scanner\",\n      children: [/*#__PURE__*/_jsx(Image, {\n        className: \"scan-mask\",\n        src: require('../../images/扫描时的黑色遮罩.png')\n      }), /*#__PURE__*/_jsx(Image, {\n        className: \"scan-frame\",\n        src: require('../../images/扫描时的框.png')\n      }), /*#__PURE__*/_jsx(Image, {\n        className: \"scan-bar\",\n        src: require('../../images/扫描条(由上向下普速).png')\n      })]\n    }), showScanResult && scanResult && /*#__PURE__*/_jsxs(View, {\n      className: \"scan-result\",\n      children: [/*#__PURE__*/_jsx(Image, {\n        className: \"result-bg\",\n        src: require('../../images/扫描后弹窗文字的背景框.png')\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"result-text\",\n        children: scanResult === 'good' ? '这家伙看上去很不错' : '这家伙看上去不靠谱'\n      })]\n    }), showGameOver && /*#__PURE__*/_jsxs(View, {\n      className: \"game-over\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"game-over-title\",\n        children: gameResult === 'success' ? '招聘成功！' : '招聘失败！'\n      }), /*#__PURE__*/_jsxs(Text, {\n        className: \"result-text\",\n        children: [\"\\u5F55\\u7528\\u4EBA\\u6570: \", hiredCount, \"/\", currentLevel.targetCount, '\\n', \"\\u9519\\u8BEF\\u6B21\\u6570: \", errorCount, \"/\", currentLevel.maxErrors]\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"restart-button\",\n        onClick: restartGame,\n        children: /*#__PURE__*/_jsx(Text, {\n          className: \"restart-button-text\",\n          children: \"\\u91CD\\u65B0\\u5F00\\u59CB\"\n        })\n      })]\n    })]\n  });\n}", "// 游戏配置文件\nexport interface Character {\n  id: string;\n  name: string;\n  image: string;\n  isReal: boolean; // 是否符合条件\n  traits: string[]; // 资料信息\n  speeches: {\n    start: string[];\n    middle: string[];\n    hired: string[];\n    rejected: string[];\n  };\n}\n\nexport interface LevelConfig {\n  level: number;\n  targetCount: number; // 需要招聘的人数\n  maxErrors: number; // 最大容错数量\n  timeLimit: number; // 时间限制（秒）\n  addTimeAmount: number; // 加时数量（秒）\n  characters: Character[]; // 本关卡的角色\n}\n\nexport interface GameConfig {\n  levels: LevelConfig[];\n  globalSettings: {\n    defaultAddTime: number; // 默认加时时间\n    scanDuration: number; // 扫描动画时长\n  };\n}\n\n// 角色数据\nexport const characters: Character[] = [\n  // 真正的三国人物（符合条件）\n  {\n    id: 'zhangfei_real',\n    name: '张飞',\n    image: '张飞主角色招聘图.png',\n    isReal: true,\n    traits: [\n      '姓名：张飞',\n      '字：翼德',\n      '籍贯：涿郡',\n      '职业：武将',\n      '特长：使用丈八蛇矛',\n      '性格：勇猛刚烈',\n      '学历：大汉三本毕业'\n    ],\n    speeches: {\n      start: [\n        '老板，我很强的，我抓鹅特别厉害',\n        '老板，很多人冒充我们三国人士，我才是真的',\n        '老板您看，我是大汉三本毕业的'\n      ],\n      middle: [\n        '老板，请让我和您一起干番大事业',\n        '老板，你看我的宝剑锋利吗',\n        '我还不错吧，老板'\n      ],\n      hired: [\n        '老板，您眼光真准',\n        '老板,您一看就是做大事的人',\n        '谢谢老板，我一定会好好努力'\n      ],\n      rejected: [\n        '哎不是，老板，我真能辅佐老板打天下的啊',\n        '你不相信我？也罢',\n        '哎呀，老板你是不是没看清楚啊'\n      ]\n    }\n  },\n  {\n    id: 'guanyu_real',\n    name: '关羽',\n    image: '关羽主角色招聘图.png',\n    isReal: true,\n    traits: [\n      '姓名：关羽',\n      '字：云长',\n      '籍贯：河东解良',\n      '职业：武将',\n      '特长：使用青龙偃月刀',\n      '性格：忠义仁勇',\n      '学历：大汉二本毕业'\n    ],\n    speeches: {\n      start: [\n        '老板，可找着你们了',\n        '老板，你看我合适吗',\n        '请问你们在招打螺丝的吗'\n      ],\n      middle: [\n        '其实我来贵公司主要是为了体验生活',\n        '老板你一看就是干大事的人',\n        '好热，咱们能不能现在就签合同呀'\n      ],\n      hired: [\n        '我确实很厉害',\n        '老板，您眼光真准',\n        '谢谢老板，我一定会好好努力'\n      ],\n      rejected: [\n        '我被淘汰辣？？？',\n        '哎老板你听我说啊，好歹加个v啊，哎老板……',\n        '有什么了不起，好工作多的是'\n      ]\n    }\n  },\n  // 冒充者（不符合条件）\n  {\n    id: 'fake_zhangfei_1',\n    name: '张飞',\n    image: '张飞主角色招聘图.png',\n    isReal: false,\n    traits: [\n      '姓名：张飞',\n      '字：翼德',\n      '籍贯：涿郡',\n      '职业：武将',\n      '特长：使用丈八蛇矛',\n      '性格：勇猛刚烈',\n      '学历：蓝翔技校毕业' // 错误信息\n    ],\n    speeches: {\n      start: [\n        '不用看，就我了',\n        '老板，我很强的，我抓鹅特别厉害',\n        '真是的，你们位置好难找呀'\n      ],\n      middle: [\n        '老板，亮亮在你们公司吗',\n        '我还不错吧，老板',\n        '老板你一看就是干大事的人'\n      ],\n      hired: [\n        '老板，您眼光真准',\n        '我确实很厉害',\n        '谢谢老板，我一定会好好努力'\n      ],\n      rejected: [\n        '不要我算了，正好另一家开高薪请我呢',\n        '呃，我回去准备一下再来',\n        '我就知道白跑一趟'\n      ]\n    }\n  },\n  {\n    id: 'fake_zhangfei_2',\n    name: '张飞',\n    image: '张飞主角色招聘图.png',\n    isReal: false,\n    traits: [\n      '姓名：张飞',\n      '字：翼德',\n      '籍贯：涿郡',\n      '职业：武将',\n      '特长：使用AK47', // 错误信息\n      '性格：勇猛刚烈',\n      '学历：大汉三本毕业'\n    ],\n    speeches: {\n      start: [\n        '老板，我很强的，我抓鹅特别厉害',\n        '老板，很多人冒充我们三国人士，我才是真的',\n        '老板您看，我是大汉三本毕业的'\n      ],\n      middle: [\n        '老板，请让我和您一起干番大事业',\n        '老板，你看我的宝剑锋利吗',\n        '我还不错吧，老板'\n      ],\n      hired: [\n        '老板，您眼光真准',\n        '老板,您一看就是做大事的人',\n        '谢谢老板，我一定会好好努力'\n      ],\n      rejected: [\n        '我就知道你在一声声\"老板\"中迷失了自我',\n        '哎呀，老板你是不是没看清楚啊',\n        '你不相信我？也罢'\n      ]\n    }\n  },\n  {\n    id: 'fake_guanyu_1',\n    name: '关羽',\n    image: '关羽主角色招聘图.png',\n    isReal: false,\n    traits: [\n      '姓名：关羽',\n      '字：云长',\n      '籍贯：河东解良',\n      '职业：武将',\n      '特长：使用青龙偃月刀',\n      '性格：忠义仁勇',\n      '学历：新东方烹饪学校毕业' // 错误信息\n    ],\n    speeches: {\n      start: [\n        '老板，可找着你们了',\n        '老板，你看我合适吗',\n        '请问你们在招打螺丝的吗'\n      ],\n      middle: [\n        '其实我来贵公司主要是为了体验生活',\n        '老板你一看就是干大事的人',\n        '好热，咱们能不能现在就签合同呀'\n      ],\n      hired: [\n        '我确实很厉害',\n        '老板，您眼光真准',\n        '谢谢老板，我一定会好好努力'\n      ],\n      rejected: [\n        '我被淘汰辣？？？',\n        '哎老板你听我说啊，好歹加个v啊，哎老板……',\n        '有什么了不起，好工作多的是'\n      ]\n    }\n  }\n];\n\n// 关卡配置\nexport const gameConfig: GameConfig = {\n  levels: [\n    {\n      level: 1,\n      targetCount: 3, // 需要招聘3个人\n      maxErrors: 2, // 最多容错2个\n      timeLimit: 120, // 2分钟\n      addTimeAmount: 60, // 加时1分钟\n      characters: [\n        characters[0], // 真张飞\n        characters[2], // 假张飞1\n        characters[3], // 假张飞2\n        characters[1], // 真关羽\n        characters[4], // 假关羽1\n      ]\n    },\n    {\n      level: 2,\n      targetCount: 5,\n      maxErrors: 3,\n      timeLimit: 180, // 3分钟\n      addTimeAmount: 60,\n      characters: characters // 所有角色\n    }\n  ],\n  globalSettings: {\n    defaultAddTime: 60, // 默认加时1分钟\n    scanDuration: 2000 // 扫描动画2秒\n  }\n};\n\n// 招聘需要的资料模板\nexport const requiredTraits = {\n  zhangfei: [\n    '姓名：张飞',\n    '字：翼德',\n    '籍贯：涿郡',\n    '职业：武将',\n    '特长：使用丈八蛇矛',\n    '性格：勇猛刚烈',\n    '学历：大汉三本毕业'\n  ],\n  guanyu: [\n    '姓名：关羽',\n    '字：云长',\n    '籍贯：河东解良',\n    '职业：武将',\n    '特长：使用青龙偃月刀',\n    '性格：忠义仁勇',\n    '学历：大汉二本毕业'\n  ]\n};\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"首页\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/index/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n", "var map = {\n\t\"./scan-bar.png\": \"./src/images/scan-bar.png\",\n\t\"./target-slot.png\": \"./src/images/target-slot.png\",\n\t\"./主角色说话弹窗.png\": \"./src/images/主角色说话弹窗.png\",\n\t\"./倒计时框.png\": \"./src/images/倒计时框.png\",\n\t\"./加时图标.png\": \"./src/images/加时图标.png\",\n\t\"./左下角资料信息图.png\": \"./src/images/左下角资料信息图.png\",\n\t\"./左侧需要招到的人数标(招到一个，填黑一个).png\": \"./src/images/左侧需要招到的人数标(招到一个，填黑一个).png\",\n\t\"./张飞主角色招聘图.png\": \"./src/images/张飞主角色招聘图.png\",\n\t\"./录用按钮.png\": \"./src/images/录用按钮.png\",\n\t\"./扫描后弹窗文字的背景框.png\": \"./src/images/扫描后弹窗文字的背景框.png\",\n\t\"./扫描时的框.png\": \"./src/images/扫描时的框.png\",\n\t\"./扫描时的黑色遮罩.png\": \"./src/images/扫描时的黑色遮罩.png\",\n\t\"./扫描条(由上向下普速).png\": \"./src/images/扫描条(由上向下普速).png\",\n\t\"./招聘失败的淘汰盖章.png\": \"./src/images/招聘失败的淘汰盖章.png\",\n\t\"./招聘成功的盖章.png\": \"./src/images/招聘成功的盖章.png\",\n\t\"./探测图标.png\": \"./src/images/探测图标.png\",\n\t\"./桌子.png\": \"./src/images/桌子.png\",\n\t\"./椅子.png\": \"./src/images/椅子.png\",\n\t\"./淘汰按钮.png\": \"./src/images/淘汰按钮.png\",\n\t\"./游戏背景.png\": \"./src/images/游戏背景.png\",\n\t\"./背景上部标题(临时工招聘会).png\": \"./src/images/背景上部标题(临时工招聘会).png\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"./src/images sync recursive ^\\\\.\\\\/.*$\";", "var map = {\n\t\"./scan-bar.png\": \"./src/images/scan-bar.png\",\n\t\"./target-slot.png\": \"./src/images/target-slot.png\",\n\t\"./主角色说话弹窗.png\": \"./src/images/主角色说话弹窗.png\",\n\t\"./倒计时框.png\": \"./src/images/倒计时框.png\",\n\t\"./加时图标.png\": \"./src/images/加时图标.png\",\n\t\"./左下角资料信息图.png\": \"./src/images/左下角资料信息图.png\",\n\t\"./左侧需要招到的人数标(招到一个，填黑一个).png\": \"./src/images/左侧需要招到的人数标(招到一个，填黑一个).png\",\n\t\"./张飞主角色招聘图.png\": \"./src/images/张飞主角色招聘图.png\",\n\t\"./录用按钮.png\": \"./src/images/录用按钮.png\",\n\t\"./扫描后弹窗文字的背景框.png\": \"./src/images/扫描后弹窗文字的背景框.png\",\n\t\"./扫描时的框.png\": \"./src/images/扫描时的框.png\",\n\t\"./扫描时的黑色遮罩.png\": \"./src/images/扫描时的黑色遮罩.png\",\n\t\"./扫描条(由上向下普速).png\": \"./src/images/扫描条(由上向下普速).png\",\n\t\"./招聘失败的淘汰盖章.png\": \"./src/images/招聘失败的淘汰盖章.png\",\n\t\"./招聘成功的盖章.png\": \"./src/images/招聘成功的盖章.png\",\n\t\"./探测图标.png\": \"./src/images/探测图标.png\",\n\t\"./桌子.png\": \"./src/images/桌子.png\",\n\t\"./椅子.png\": \"./src/images/椅子.png\",\n\t\"./淘汰按钮.png\": \"./src/images/淘汰按钮.png\",\n\t\"./游戏背景.png\": \"./src/images/游戏背景.png\",\n\t\"./背景上部标题(临时工招聘会).png\": \"./src/images/背景上部标题(临时工招聘会).png\"\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = \"./src/images sync recursive ^\\\\.\\\\/.*\\\\.png$\";"], "names": [], "sourceRoot": ""}