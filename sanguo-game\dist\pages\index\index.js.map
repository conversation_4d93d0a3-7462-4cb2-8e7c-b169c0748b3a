{"version": 3, "file": "pages/index/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;ACtTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://sanguo-game/./src/pages/index/index.tsx?acc1", "webpack://sanguo-game/._src_pages_index_index.tsx"], "sourcesContent": ["import { View, Text } from '@tarojs/components';\nimport { useLoad } from '@tarojs/taro';\nimport { useEffect, useState } from 'react';\nimport './index.scss';\n\n// 角色数据类型定义\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\n// 角色数据\nconst characters = [{\n  name: \"张飞\",\n  traits: [\"黑发\", \"墨镜\", \"皮肤黝黑\", \"易怒\", \"擅长使矛\"],\n  isReal: true,\n  image: require(\"../../images/张飞主角色招聘图.png\"),\n  startDialogues: [\"老板，我很强的，我抓鹅特别厉害\", \"老板，你看我合适吗\", \"老板，请让我和您一起干番大事业\", \"老板，你看我的宝剑锋利吗\"],\n  hireDialogues: [\"老板，您眼光真准\", \"老板，您一看就是做大事的人\", \"谢谢老板，我一定会好好努力\", \"我确实很厉害\"],\n  rejectDialogues: [\"我被淘汰辣？？？\", \"哎老板你听我说啊，好歹加个v啊，哎老板……\", \"不要我算了，正好另一家开高薪请我呢\", \"你不相信我？也罢\"]\n}, {\n  name: \"假张飞\",\n  traits: [\"黑发\", \"墨镜\", \"皮肤白皙\", \"易怒\", \"擅长使矛\"],\n  isReal: false,\n  image: require(\"../../images/张飞主角色招聘图.png\"),\n  startDialogues: [\"老板，很多人冒充我们三国人士，我才是真的\", \"老板您看，我是大汉三本毕业的\", \"不用看，就我了\", \"老板，可找着你们了\"],\n  hireDialogues: [\"老板，您眼光真准\", \"老板，您一看就是做大事的人\", \"谢谢老板，我一定会好好努力\", \"我确实很厉害\"],\n  rejectDialogues: [\"呃，我回去准备一下再来\", \"哎呀，老板你是不是没看清楚啊\", \"哎不是，老板，我真能辅佐老板打天下的啊\", \"我就知道白跑一趟\"]\n}];\nexport default function Index() {\n  // 游戏状态\n  const [gameRunning, setGameRunning] = useState(false);\n  const [timeLeft, setTimeLeft] = useState(120); // 2分钟\n  const [hiredCount, setHiredCount] = useState(0);\n  const [wrongHires, setWrongHires] = useState(0);\n  const [currentCharacter, setCurrentCharacter] = useState(null);\n  const [speechText, setSpeechText] = useState(\"\");\n  const [showScanner, setShowScanner] = useState(false);\n  const [showResult, setShowResult] = useState(false);\n  const [resultType, setResultType] = useState('success');\n  const [showGameOver, setShowGameOver] = useState(false);\n  const [infoCardEnlarged, setInfoCardEnlarged] = useState(false);\n  const targetCount = 5;\n  const maxWrongHires = 3;\n  useLoad(() => {\n    console.log('三国打工人游戏加载完成');\n  });\n\n  // 计时器效果\n  useEffect(() => {\n    let interval;\n    if (gameRunning && timeLeft > 0) {\n      interval = setInterval(() => {\n        setTimeLeft(prev => {\n          if (prev <= 1) {\n            endGame();\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    }\n    return () => clearInterval(interval);\n  }, [gameRunning, timeLeft]);\n\n  // 格式化时间显示\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 开始游戏\n  const startGame = () => {\n    setGameRunning(true);\n    setHiredCount(0);\n    setWrongHires(0);\n    setTimeLeft(120);\n    setShowGameOver(false);\n    nextCharacter();\n  };\n\n  // 下一个角色\n  const nextCharacter = () => {\n    const randomIndex = Math.floor(Math.random() * characters.length);\n    const character = characters[randomIndex];\n    setCurrentCharacter(character);\n\n    // 随机选择开始对话\n    const randomDialogueIndex = Math.floor(Math.random() * character.startDialogues.length);\n    setSpeechText(character.startDialogues[randomDialogueIndex]);\n  };\n\n  // 做出决定（录用/淘汰）\n  const makeDecision = decision => {\n    if (!gameRunning || !currentCharacter) return;\n\n    // 显示扫描动画\n    setShowScanner(true);\n    setTimeout(() => {\n      setShowScanner(false);\n      let isCorrectDecision = false;\n      if (decision === 'hire' && currentCharacter.isReal) {\n        // 正确录用\n        setResultType('success');\n        setHiredCount(prev => prev + 1);\n        isCorrectDecision = true;\n      } else if (decision === 'reject' && !currentCharacter.isReal) {\n        // 正确淘汰\n        setResultType('fail');\n        isCorrectDecision = true;\n      } else if (decision === 'hire' && !currentCharacter.isReal) {\n        // 错误录用\n        setResultType('fail');\n        setWrongHires(prev => prev + 1);\n      } else {\n        // 错误淘汰\n        setResultType('fail');\n      }\n\n      // 显示结果\n      setShowResult(true);\n\n      // 更新对话\n      const dialogues = decision === 'hire' ? currentCharacter.hireDialogues : currentCharacter.rejectDialogues;\n      const randomDialogueIndex = Math.floor(Math.random() * dialogues.length);\n      setSpeechText(dialogues[randomDialogueIndex]);\n\n      // 检查游戏是否结束\n      const newHiredCount = decision === 'hire' && currentCharacter.isReal ? hiredCount + 1 : hiredCount;\n      const newWrongHires = decision === 'hire' && !currentCharacter.isReal ? wrongHires + 1 : wrongHires;\n      if (newHiredCount >= targetCount || newWrongHires >= maxWrongHires) {\n        setTimeout(() => {\n          endGame();\n        }, 1500);\n        return;\n      }\n\n      // 延迟后进入下一个角色\n      setTimeout(() => {\n        setShowResult(false);\n        nextCharacter();\n      }, 1500);\n    }, 2000); // 扫描动画持续2秒\n  };\n\n  // 游戏结束\n  const endGame = () => {\n    setGameRunning(false);\n    setShowGameOver(true);\n  };\n\n  // 重新开始游戏\n  const restartGame = () => {\n    setShowGameOver(false);\n    startGame();\n  };\n\n  // 使用探测仪\n  const useDetector = () => {\n    if (!gameRunning) return;\n    // 这里可以添加探测仪功能\n    console.log('探测到可疑特征！请仔细检查资料信息。');\n  };\n\n  // 添加时间\n  const addTime = () => {\n    if (!gameRunning) return;\n    setTimeLeft(prev => prev + 60);\n    console.log('已增加60秒时间！');\n  };\n\n  // 切换资料卡大小\n  const toggleInfoCard = () => {\n    setInfoCardEnlarged(!infoCardEnlarged);\n  };\n  return /*#__PURE__*/_jsxs(View, {\n    className: \"game-container\",\n    children: [/*#__PURE__*/_jsx(View, {\n      className: \"background\"\n    }), /*#__PURE__*/_jsxs(View, {\n      className: \"title-bar\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"title\",\n        children: \"\\u4E09\\u56FD\\u6253\\u5DE5\\u4EBA\"\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"timer\",\n        children: formatTime(timeLeft)\n      })]\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"game-main\",\n      children: currentCharacter && /*#__PURE__*/_jsxs(View, {\n        className: \"character\",\n        children: [/*#__PURE__*/_jsx(Text, {\n          style: {\n            fontSize: '60rpx',\n            color: 'white'\n          },\n          children: \"\\uD83D\\uDC64\"\n        }), /*#__PURE__*/_jsx(Text, {\n          style: {\n            fontSize: '24rpx',\n            color: 'white',\n            marginTop: '10rpx'\n          },\n          children: currentCharacter.name\n        })]\n      })\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"speech-bubble\",\n      children: /*#__PURE__*/_jsx(Text, {\n        className: \"speech-text\",\n        children: speechText\n      })\n    }), currentCharacter && /*#__PURE__*/_jsx(View, {\n      className: `info-card ${infoCardEnlarged ? 'enlarged' : ''}`,\n      onClick: toggleInfoCard,\n      children: /*#__PURE__*/_jsxs(View, {\n        className: \"info-content\",\n        children: [/*#__PURE__*/_jsx(View, {\n          className: \"info-name\",\n          children: currentCharacter.name\n        }), currentCharacter.traits.map((trait, index) => /*#__PURE__*/_jsxs(View, {\n          className: \"info-item\",\n          children: [\"\\u2022 \", trait]\n        }, index))]\n      })\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"bottom-bar\",\n      children: /*#__PURE__*/_jsxs(View, {\n        className: \"buttons\",\n        children: [/*#__PURE__*/_jsx(View, {\n          className: \"hire-button\",\n          onClick: () => makeDecision('hire'),\n          children: /*#__PURE__*/_jsx(Text, {\n            style: {\n              color: 'white',\n              fontSize: '28rpx',\n              fontWeight: 'bold'\n            },\n            children: \"\\u5F55\\u7528\"\n          })\n        }), /*#__PURE__*/_jsx(View, {\n          className: \"reject-button\",\n          onClick: () => makeDecision('reject'),\n          children: /*#__PURE__*/_jsx(Text, {\n            style: {\n              color: 'white',\n              fontSize: '28rpx',\n              fontWeight: 'bold'\n            },\n            children: \"\\u6DD8\\u6C70\"\n          })\n        })]\n      })\n    }), /*#__PURE__*/_jsx(View, {\n      className: \"targets\",\n      children: Array.from({\n        length: targetCount\n      }, (_, i) => /*#__PURE__*/_jsx(View, {\n        className: `target-slot ${i < hiredCount ? 'filled' : ''}`\n      }, i))\n    }), /*#__PURE__*/_jsxs(View, {\n      className: \"tools\",\n      children: [/*#__PURE__*/_jsx(View, {\n        className: \"detector\",\n        onClick: useDetector\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"time-add\",\n        onClick: addTime\n      })]\n    }), showScanner && /*#__PURE__*/_jsxs(View, {\n      className: \"scanner\",\n      children: [/*#__PURE__*/_jsx(View, {\n        className: \"scan-frame\"\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"scan-bar\"\n      })]\n    }), showResult && /*#__PURE__*/_jsx(View, {\n      className: `result ${resultType}`\n    }), !gameRunning && !showGameOver && /*#__PURE__*/_jsxs(View, {\n      className: \"start-screen\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"game-title\",\n        children: \"\\u4E09\\u56FD\\u6253\\u5DE5\\u4EBA\"\n      }), /*#__PURE__*/_jsxs(Text, {\n        className: \"game-description\",\n        children: [\"\\u5728\\u4F17\\u591A\\u5192\\u5145\\u8005\\u4E2D\\u62DB\\u5230\\u771F\\u6B63\\u7684\\u4E09\\u56FD\\u6253\\u5DE5\\u4EBA\\uFF01\", '\\n', \"\\u4ED4\\u7EC6\\u5BF9\\u6BD4\\u8D44\\u6599\\uFF0C\\u505A\\u51FA\\u660E\\u667A\\u7684\\u9009\\u62E9\\u3002\"]\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"start-button\",\n        onClick: startGame,\n        children: /*#__PURE__*/_jsx(Text, {\n          className: \"start-button-text\",\n          children: \"\\u5F00\\u59CB\\u62DB\\u8058\"\n        })\n      })]\n    }), showGameOver && /*#__PURE__*/_jsxs(View, {\n      className: \"game-over\",\n      children: [/*#__PURE__*/_jsx(Text, {\n        className: \"game-over-title\",\n        children: \"\\u6E38\\u620F\\u7ED3\\u675F\"\n      }), /*#__PURE__*/_jsx(Text, {\n        className: \"result-text\",\n        children: hiredCount >= targetCount ? `恭喜！你成功招聘了 ${hiredCount} 名真正的三国打工人！` : wrongHires >= maxWrongHires ? `你错误录用了 ${wrongHires} 名冒充者，招聘失败！` : `时间到！你只招聘到了 ${hiredCount}/${targetCount} 名三国打工人。`\n      }), /*#__PURE__*/_jsx(View, {\n        className: \"restart-button\",\n        onClick: restartGame,\n        children: /*#__PURE__*/_jsx(Text, {\n          className: \"restart-button-text\",\n          children: \"\\u518D\\u6765\\u4E00\\u6B21\"\n        })\n      })]\n    })]\n  });\n}", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/index/index!./index.tsx\"\nvar config = {\"navigationBarTitleText\":\"首页\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/index/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}