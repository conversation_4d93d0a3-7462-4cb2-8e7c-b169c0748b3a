/* 三国打工人游戏样式 - 使用原始素材 */
@use '../../styles/responsive.scss';
@use '../../styles/adaptive.scss';

/* 全局响应式设置 */
* {
  box-sizing: border-box;
}

/* 防止横屏时界面变形 */
@media (orientation: landscape) and (max-height: 500px) {
  .game-container {
    font-size: 0.8em;
  }

  .timer {
    top: calc(env(safe-area-inset-top, 0) + 60rpx);
    font-size: 2.5rem;
  }

  .character {
    top: 50%;
    width: 20%;
    height: 40%;
  }

  .speech-bubble {
    top: 15%;
    width: 28%;
    height: 15%;
  }

  .info-card {
    width: 28%;
    height: 45%;
  }
}

.game-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
  z-index: 0;
}

/* 顶部标题栏 */
.title-bar {
  position: absolute;
  top: env(safe-area-inset-top, 0);
  left: 0;
  width: 100%;
  height: 200rpx;
  z-index: 10;
}

.title {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  max-width: 800rpx;
  height: auto;
  z-index: 2;
}

/* 计时器 */
.timer {
  position: absolute;
  top: calc(env(safe-area-inset-top, 0) + 120rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 280rpx;
  height: 100rpx;
  background-image: url('../../images/倒计时框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3.5rem;
  font-weight: bold;
  color: white;
  z-index: 3;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 3rem;
    width: 240rpx;
    height: 80rpx;
  }
}

/* 角色 */
.character {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 25%;
  max-width: 300rpx;
  height: 50%;
  max-height: 600rpx;
  object-fit: contain;
  z-index: 3;
  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 30%;
    height: 45%;
  }
}

/* 对话气泡 */
.speech-bubble {
  position: absolute;
  top: 25%;
  right: 8%;
  width: 32%;
  max-width: 400rpx;
  height: 18%;
  min-height: 120rpx;
  background-image: url('../../images/主角色说话弹窗.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  z-index: 5;
  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 38%;
    right: 5%;
    top: 22%;
  }
}

.speech-text {
  font-size: 1.8rem;
  text-align: center;
  color: #333;
  width: 80%;
  line-height: 1.3;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 1.6rem;
  }
}

/* 信息卡片 */
.info-card {
  position: absolute;
  bottom: calc(env(safe-area-inset-bottom, 0) + 18%);
  left: 3%;
  width: 32%;
  max-width: 350rpx;
  height: 55%;
  max-height: 650rpx;
  background-image: url('../../images/左下角资料信息图.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;
  transform-origin: bottom left;
  z-index: 4;

  &.enlarged {
    transform: scale(1.4);
    z-index: 100;
  }

  /* 拖拽目标区域高亮 */
  &.drag-target {
    box-shadow: 0 0 20rpx #ff4444;
    transform: scale(1.05);
  }

  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 35%;
    bottom: calc(env(safe-area-inset-bottom, 0) + 20%);
    left: 2%;

    &.enlarged {
      transform: scale(1.2);
    }
  }
}

.info-content {
  position: absolute;
  top: 25%;
  left: 15%;
  width: 70%;
  height: 60%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 1.4rem;
  color: #333;
  line-height: 1.2;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 1.2rem;
    top: 28%;
    left: 12%;
    width: 75%;
  }
}

.info-name {
  font-size: 2.2rem;
  margin-bottom: 8rpx;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 2rem;
  }
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 10rpx;
}

/* 底部按钮区域 */
.bottom-bar {
  position: absolute;
  bottom: env(safe-area-inset-bottom, 0);
  left: 0;
  width: 100%;
  height: 200rpx;
  z-index: 2;
}

.buttons {
  position: absolute;
  bottom: calc(env(safe-area-inset-bottom, 0) + 8%);
  right: 3%;
  display: flex;
  gap: 30rpx;
  z-index: 5;
  /* 响应式调整 */
  @media (max-width: 750px) {
    gap: 20rpx;
    right: 2%;
    bottom: calc(env(safe-area-inset-bottom, 0) + 10%);
  }
}

.hire-button, .reject-button {
  width: 140rpx;
  height: 140rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  cursor: grab;

  &:active {
    cursor: grabbing;
    transform: scale(1.1);
  }

  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 120rpx;
    height: 120rpx;
  }
}

.targets {
  position: absolute;
  top: 22%;
  left: 1%;
  width: 100rpx;
  height: 500rpx;
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  z-index: 4;
  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 80rpx;
    gap: 12rpx;
    top: 25%;
  }
}

.target-slot {
  width: 80rpx;
  height: 80rpx;
  background-image: url('../../images/target-slot.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;

  &.filled {
    opacity: 1;
  }

  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 60rpx;
    height: 60rpx;
  }
}

.tools {
  position: absolute;
  top: 45%;
  right: 1%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  z-index: 4;
  /* 响应式调整 */
  @media (max-width: 750px) {
    gap: 20rpx;
    top: 48%;
  }
}

.detector, .time-add {
  width: 100rpx;
  height: 100rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 80rpx;
    height: 80rpx;
  }
}

/* 扫描器 */
.scanner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.scan-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 70%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.scan-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 20rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: scanMove 2s linear infinite;
}

/* 扫描结果 */
.scan-result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 350rpx;
  height: 180rpx;
  z-index: 15;
  /* 响应式调整 */
  @media (max-width: 750px) {
    width: 300rpx;
    height: 150rpx;
  }
}

.result-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.2rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  line-height: 1.2;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 2rem;
  }
}

@keyframes scanMove {
  0% { top: 15%; }
  100% { top: 85%; }
}

@keyframes bubbleFloat {
  0% { transform: translateY(0rpx); }
  100% { transform: translateY(-10rpx); }
}

@keyframes characterPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 拖拽印章 */
.dragging-stamp {
  position: fixed;
  width: 80rpx;
  height: 80rpx;
  z-index: 1000;
  pointer-events: none;
  opacity: 0.8;

  .stamp-image {
    width: 100%;
    height: 100%;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
}

/* 进度条容器 */
.stamp-progress-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;
  height: 120rpx;
  z-index: 50;
}

/* 进度条背景 */
.stamp-progress-bg {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

/* 进度条填充 */
.stamp-progress-fill {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  width: calc(100% + 8rpx);
  height: calc(100% + 8rpx);
  border-radius: 50%;
  border: 4rpx solid transparent;
  border-top-color: #ff4444;
  transition: transform 0.1s ease;
  transform-origin: center;
}

/* 进度条文字 */
.stamp-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.8);
}

/* 盖章结果覆盖层 */
.stamp-result-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
}

/* 盖章结果内容 */
.stamp-result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: stampAppear 0.5s ease-out;
}

/* 盖章结果文字 */
.stamp-result-text {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 40rpx;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.8);
}

/* 盖章结果图标 */
.stamp-result-icon {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 8rpx solid white;

  &.hire {
    background: linear-gradient(45deg, #4CAF50, #66BB6A);
  }

  &.reject {
    background: linear-gradient(45deg, #F44336, #EF5350);
  }
}

/* 盖章图标文字 */
.stamp-icon-text {
  font-size: 80rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.8);
}

@keyframes stampAppear {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: 300rpx;
  height: 300rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 20;

  &.success {
    background-image: url('../../images/招聘成功的盖章.png');
  }

  &.fail {
    background-image: url('../../images/招聘失败的淘汰盖章.png');
  }
}

.start-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  box-sizing: border-box;
}

.game-title {
  font-size: 4.5rem;
  color: white;
  margin-bottom: 50rpx;
  font-weight: bold;
  text-align: center;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 3.8rem;
    margin-bottom: 40rpx;
  }
}

.game-description {
  font-size: 2.2rem;
  color: white;
  margin-bottom: 80rpx;
  text-align: center;
  max-width: 85%;
  line-height: 1.4;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 2rem;
    margin-bottom: 60rpx;
    max-width: 90%;
  }
}

.start-button, .restart-button {
  padding: 25rpx 70rpx;
  border-radius: 50rpx;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 200rpx;
  /* 响应式调整 */
  @media (max-width: 750px) {
    padding: 20rpx 60rpx;
    min-width: 180rpx;
  }
}

.start-button {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.5);

  &:active {
    transform: translateY(-8rpx);
    box-shadow: 0 12rpx 35rpx rgba(255, 107, 107, 0.7);
  }
}

.restart-button {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  box-shadow: 0 8rpx 30rpx rgba(79, 172, 254, 0.5);

  &:active {
    transform: translateY(-8rpx);
    box-shadow: 0 12rpx 35rpx rgba(79, 172, 254, 0.7);
  }
}

.start-button-text, .restart-button-text {
  font-size: 2.8rem;
  color: white;
  font-weight: bold;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 2.4rem;
  }
}

.game-over {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
  padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
  box-sizing: border-box;
}

.game-over-title {
  font-size: 5.5rem;
  color: white;
  margin-bottom: 35rpx;
  font-weight: bold;
  text-align: center;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 4.5rem;
    margin-bottom: 30rpx;
  }
}

.result-text {
  font-size: 2.8rem;
  color: white;
  margin-bottom: 50rpx;
  text-align: center;
  max-width: 85%;
  line-height: 1.4;
  /* 响应式字体 */
  @media (max-width: 750px) {
    font-size: 2.4rem;
    margin-bottom: 40rpx;
    max-width: 90%;
  }
}