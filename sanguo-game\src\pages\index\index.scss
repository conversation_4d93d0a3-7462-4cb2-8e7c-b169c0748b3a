/* 三国打工人游戏样式 - 使用原始素材尺寸版本 */

.game-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
  z-index: 0;
}

/* ==================== 顶部标题 - 使用原始尺寸 ==================== */
.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: auto; /* 自适应高度 */
  z-index: 10;
}

.title {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: auto;    /* 使用图片原始宽度 */
  height: auto;   /* 使用图片原始高度 */
  background-image: url('../../images/背景上部标题(临时工招聘会).png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 2;
  /* 如果图片原始尺寸太大，可以设置最大宽度 */
  max-width: 90%;
}

/* ==================== 计时器 - 使用原始尺寸 ==================== */
.timer {
  position: absolute;
  top: 120rpx;
  left: 50%;
  transform: translateX(-50%);
  width: auto;    /* 使用倒计时框原始宽度 */
  height: auto;   /* 使用倒计时框原始高度 */
  background-image: url('../../images/倒计时框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 3rem;
  font-weight: bold;
  color: white;
  z-index: 3;
  /* 设置最小尺寸以确保内容可见 */
  min-width: 200rpx;
  min-height: 80rpx;
}

/* ==================== 角色 - 使用原始尺寸 ==================== */
.character {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;    /* 使用角色图片原始宽度 */
  height: auto;   /* 使用角色图片原始高度 */
  background-image: url('../../images/张飞主角色招聘图.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 3;
  /* 设置最大尺寸以防止图片过大 */
  max-width: 40%;
  max-height: 60%;
}

/* ==================== 对话气泡 - 使用原始尺寸 ==================== */
.speech-bubble {
  position: absolute;
  top: 20%;
  right: 5%;
  width: auto;    /* 使用对话框原始宽度 */
  height: auto;   /* 使用对话框原始高度 */
  background-image: url('../../images/主角色说话弹窗.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx;
  z-index: 5;
  /* 设置最大尺寸 */
  max-width: 35%;
  max-height: 25%;
  min-width: 200rpx;
  min-height: 100rpx;
}

.speech-text {
  font-size: 1.6rem;
  text-align: center;
  color: #333;
  width: 70%;
  line-height: 1.3;
}

/* ==================== 信息卡片 - 使用原始尺寸 ==================== */
.info-card {
  position: absolute;
  bottom: 15%;
  left: 2%;
  width: auto;    /* 使用信息图原始宽度 */
  height: auto;   /* 使用信息图原始高度 */
  background-image: url('../../images/左下角资料信息图.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: all 0.3s ease;
  transform-origin: bottom left;
  z-index: 4;
  /* 设置最大尺寸 */
  max-width: 35%;
  max-height: 65%;
  min-width: 250rpx;
  min-height: 400rpx;
}

.info-card.enlarged {
  transform: scale(1.2);
  z-index: 100;
}

.info-card.drag-target {
  box-shadow: 0 0 20rpx #ff4444;
  transform: scale(1.05);
}

.info-content {
  position: absolute;
  top: 25%;
  left: 15%;
  width: 70%;
  height: 60%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 1.2rem;
  color: #333;
  line-height: 1.2;
}

.info-name {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.info-item {
  margin-bottom: 6rpx;
  font-size: 1rem;
}

/* ==================== 底部按钮 - 使用原始尺寸 ==================== */
.bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: auto;
  z-index: 2;
}

.buttons {
  position: absolute;
  bottom: 5%;
  right: 2%;
  display: flex;
  gap: 20rpx;
  z-index: 5;
}

.hire-button {
  width: auto;    /* 使用录用按钮原始宽度 */
  height: auto;   /* 使用录用按钮原始高度 */
  background-image: url('../../images/录用按钮.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  cursor: grab;
  /* 设置最小尺寸确保可点击 */
  min-width: 100rpx;
  min-height: 100rpx;
}

.reject-button {
  width: auto;    /* 使用淘汰按钮原始宽度 */
  height: auto;   /* 使用淘汰按钮原始高度 */
  background-image: url('../../images/淘汰按钮.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  cursor: grab;
  /* 设置最小尺寸确保可点击 */
  min-width: 100rpx;
  min-height: 100rpx;
}

.hire-button:active, .reject-button:active {
  cursor: grabbing;
  transform: scale(1.1);
}

/* ==================== 左侧目标 - 使用原始尺寸 ==================== */
.targets {
  position: absolute;
  top: 20%;
  left: 1%;
  width: auto;
  height: auto;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
  z-index: 4;
}

.target-slot {
  width: auto;    /* 使用目标槽原始宽度 */
  height: auto;   /* 使用目标槽原始高度 */
  background-image: url('../../images/左侧需要招到的人数标(招到一个，填黑一个).png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;
  /* 设置最小尺寸 */
  min-width: 60rpx;
  min-height: 60rpx;
}

.target-slot.filled {
  opacity: 1;
}

/* ==================== 右侧工具 - 使用原始尺寸 ==================== */
.tools {
  position: absolute;
  top: 40%;
  right: 1%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 4;
}

.detector {
  width: auto;    /* 使用探测图标原始宽度 */
  height: auto;   /* 使用探测图标原始高度 */
  background-image: url('../../images/探测图标.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  /* 设置最小尺寸确保可点击 */
  min-width: 80rpx;
  min-height: 80rpx;
}

.time-add {
  width: auto;    /* 使用加时图标原始宽度 */
  height: auto;   /* 使用加时图标原始高度 */
  background-image: url('../../images/加时图标.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  /* 设置最小尺寸确保可点击 */
  min-width: 80rpx;
  min-height: 80rpx;
}

.detector:active, .time-add:active {
  transform: scale(0.95);
}

/* ==================== 扫描器 - 使用原始尺寸 ==================== */
.scanner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.scan-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../../images/扫描时的黑色遮罩.png');
  background-size: cover;
  background-position: center;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;    /* 使用扫描框原始宽度 */
  height: auto;   /* 使用扫描框原始高度 */
  background-image: url('../../images/扫描时的框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  /* 设置最大尺寸 */
  max-width: 85%;
  max-height: 75%;
}

.scan-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: auto;    /* 使用扫描条原始宽度 */
  height: auto;   /* 使用扫描条原始高度 */
  background-image: url('../../images/扫描条(由上向下普速).png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: scanMove 2s linear infinite;
  /* 设置最小尺寸 */
  min-width: 200rpx;
  min-height: 10rpx;
}

/* ==================== 扫描结果 - 使用原始尺寸 ==================== */
.scan-result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: auto;    /* 使用结果框原始宽度 */
  height: auto;   /* 使用结果框原始高度 */
  z-index: 15;
  min-width: 300rpx;
  min-height: 150rpx;
}

.result-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../../images/扫描后弹窗文字的背景框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  text-align: center;
  line-height: 1.2;
  width: 80%;
}

/* 扫描动画 */
@keyframes scanMove {
  0% { top: 15%; }
  100% { top: 85%; }
}

/* ==================== 拖拽印章 - 使用原始尺寸 ==================== */
.dragging-stamp {
  position: fixed;
  width: auto;    /* 使用印章原始宽度 */
  height: auto;   /* 使用印章原始高度 */
  z-index: 1000;
  pointer-events: none;
  opacity: 0.8;
  min-width: 60rpx;
  min-height: 60rpx;
}

.stamp-image {
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* ==================== 进度条 ==================== */
.stamp-progress-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100rpx;
  height: 100rpx;
  z-index: 50;
}

.stamp-progress-bg {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.stamp-progress-fill {
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  width: calc(100% + 6rpx);
  height: calc(100% + 6rpx);
  border-radius: 50%;
  border: 3rpx solid transparent;
  border-top-color: #ff4444;
  transition: transform 0.1s ease;
  transform-origin: center;
}

.stamp-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 20rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.8);
}

/* ==================== 盖章结果 - 使用原始尺寸 ==================== */
.result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: auto;    /* 使用盖章图片原始宽度 */
  height: auto;   /* 使用盖章图片原始高度 */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 20;
  /* 设置最大尺寸 */
  max-width: 40%;
  max-height: 40%;
  min-width: 200rpx;
  min-height: 200rpx;
}

.result.success {
  background-image: url('../../images/招聘成功的盖章.png');
}

.result.fail {
  background-image: url('../../images/招聘失败的淘汰盖章.png');
}

/* ==================== 盖章结果覆盖层 ==================== */
.stamp-result-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5);
}

.stamp-result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: stampAppear 0.5s ease-out;
}

.stamp-result-text {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 30rpx;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.8);
}

.stamp-result-icon {
  width: 150rpx;
  height: 150rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 6rpx solid white;
}

.stamp-result-icon.hire {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
}

.stamp-result-icon.reject {
  background: linear-gradient(45deg, #F44336, #EF5350);
}

.stamp-icon-text {
  font-size: 60rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.8);
}

@keyframes stampAppear {
  0% {
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg);
    opacity: 0.8;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* ==================== 游戏开始界面 ==================== */
.start-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-title {
  font-size: 4rem;
  color: white;
  margin-bottom: 40rpx;
  font-weight: bold;
  text-align: center;
}

.game-description {
  font-size: 2rem;
  color: white;
  margin-bottom: 60rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.4;
}

.start-button, .restart-button {
  padding: 20rpx 60rpx;
  border-radius: 40rpx;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 180rpx;
}

.start-button {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  box-shadow: 0 6rpx 25rpx rgba(255, 107, 107, 0.5);
}

.start-button:active {
  transform: translateY(-6rpx);
  box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.7);
}

.restart-button {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  box-shadow: 0 6rpx 25rpx rgba(79, 172, 254, 0.5);
}

.restart-button:active {
  transform: translateY(-6rpx);
  box-shadow: 0 10rpx 30rpx rgba(79, 172, 254, 0.7);
}

.start-button-text, .restart-button-text {
  font-size: 2.4rem;
  color: white;
  font-weight: bold;
}

/* ==================== 游戏结束界面 ==================== */
.game-over {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-over-title {
  font-size: 5rem;
  color: white;
  margin-bottom: 30rpx;
  font-weight: bold;
  text-align: center;
}

.result-text {
  font-size: 2.4rem;
  color: white;
  margin-bottom: 40rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.4;
}

/*
 * 📝 原始尺寸使用说明：
 *
 * 这个样式文件将所有素材设置为使用原始尺寸（width: auto, height: auto），
 * 同时设置了合理的最大/最小尺寸限制，确保在不同设备上的显示效果。
 *
 * 主要特点：
 * 1. 所有图片素材使用原始尺寸
 * 2. 设置了max-width/max-height防止图片过大
 * 3. 设置了min-width/min-height确保可见性和可点击性
 * 4. 保持了响应式布局的基本原则
 *
 * 如果发现某些素材显示过大或过小，可以调整对应的max-width/max-height值。
 */
