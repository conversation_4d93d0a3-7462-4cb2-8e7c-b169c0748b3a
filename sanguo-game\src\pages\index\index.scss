* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.game-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: #f0f0e8;
  font-family: 'Microsoft YaHei', sans-serif;
}

.background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
}

.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200rpx;
  background-color: rgba(100, 100, 180, 0.5);
  z-index: 1;
}

.title {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  max-width: 1000rpx;
  height: auto;
  z-index: 2;
}

.timer {
  position: absolute;
  top: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 300rpx;
  height: 120rpx;
  background-image: url('../../images/倒计时框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 4rem;
  font-weight: bold;
  color: white;
  z-index: 3;
}

.character {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30%;
  height: 60%;
  object-fit: contain;
  z-index: 3;
}

.speech-bubble {
  position: absolute;
  top: 20%;
  right: 10%;
  width: 35%;
  height: 20%;
  background-image: url('../../images/主角色说话弹窗.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  z-index: 5;
}

.speech-text {
  font-size: 2rem;
  text-align: center;
  color: #333;
  width: 80%;
}

.info-card {
  position: absolute;
  bottom: 15%;
  left: 5%;
  width: 35%;
  height: 60%;
  background-image: url('../../images/左下角资料信息图.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s ease;
  transform-origin: bottom left;
  z-index: 4;

  &.enlarged {
    transform: scale(1.5);
    z-index: 100;
  }
}

.info-content {
  position: absolute;
  top: 25%;
  left: 15%;
  width: 70%;
  height: 60%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 1.6rem;
  color: #333;
}

.info-name {
  font-size: 2.4rem;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.info-item {
  margin-bottom: 10rpx;
}

.bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200rpx;
  background-color: rgba(50, 50, 100, 0.7);
  z-index: 2;
}

.buttons {
  position: absolute;
  bottom: 10%;
  right: 5%;
  display: flex;
  gap: 40rpx;
  z-index: 5;
}

.hire-button, .reject-button {
  width: 160rpx;
  height: 160rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.hire-button {
  background-image: url('../../images/录用按钮.png');
}

.reject-button {
  background-image: url('../../images/淘汰按钮.png');
}

.targets {
  position: absolute;
  top: 20%;
  left: 2%;
  width: 120rpx;
  height: 600rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 4;
}

.target-slot {
  width: 100rpx;
  height: 100rpx;
  background-image: url('../../images/target-slot.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;

  &.filled {
    opacity: 1;
  }
}

.tools {
  position: absolute;
  top: 50%;
  right: 2%;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  z-index: 4;
}

.detector, .time-add {
  width: 120rpx;
  height: 120rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.detector {
  background-image: url('../../images/探测图标.png');
}

.time-add {
  background-image: url('../../images/加时图标.png');
}

.scanner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 70%;
  background-image: url('../../images/扫描时的框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.scan-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 20rpx;
  background-image: url('../../images/scan-bar.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: scanMove 2s linear infinite;
}

@keyframes scanMove {
  0% { top: 15%; }
  100% { top: 85%; }
}

.result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: 300rpx;
  height: 300rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 20;

  &.success {
    background-image: url('../../images/招聘成功的盖章.png');
  }

  &.fail {
    background-image: url('../../images/招聘失败的淘汰盖章.png');
  }
}

.start-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-title {
  font-size: 5rem;
  color: white;
  margin-bottom: 60rpx;
  font-weight: bold;
}

.game-description {
  font-size: 2.4rem;
  color: white;
  margin-bottom: 100rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

.start-button, .restart-button {
  padding: 30rpx 80rpx;
  border-radius: 60rpx;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.start-button {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.5);

  &:active {
    transform: translateY(-10rpx);
    box-shadow: 0 14rpx 40rpx rgba(255, 107, 107, 0.7);
  }
}

.restart-button {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  box-shadow: 0 8rpx 30rpx rgba(79, 172, 254, 0.5);

  &:active {
    transform: translateY(-10rpx);
    box-shadow: 0 14rpx 40rpx rgba(79, 172, 254, 0.7);
  }
}

.start-button-text, .restart-button-text {
  font-size: 3rem;
  color: white;
  font-weight: bold;
}

.game-over {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-over-title {
  font-size: 6rem;
  color: white;
  margin-bottom: 40rpx;
  font-weight: bold;
}

.result-text {
  font-size: 3rem;
  color: white;
  margin-bottom: 60rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}