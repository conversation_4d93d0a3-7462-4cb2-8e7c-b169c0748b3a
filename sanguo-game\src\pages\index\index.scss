/* 微信小程序样式优化 */

.game-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
  z-index: 0;
}

/* 顶部区域 */
.title-bar {
  position: relative;
  width: 100%;
  height: 120rpx;
  background: rgba(139, 69, 19, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.title {
  width: 400rpx;
  height: 80rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #FFD700;
  text-align: center;
  line-height: 80rpx;
}

/* 计时器 */
.timer {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 160rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #FFD700;
  z-index: 15;
}

/* 游戏主体区域 */
.game-main {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
}

/* 角色 */
.character {
  width: 240rpx;
  height: 320rpx;
  background: linear-gradient(45deg, #4169E1, #1E90FF);
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.3);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  z-index: 5;
  animation: characterPulse 3s ease-in-out infinite;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}

/* 对话气泡 */
.speech-bubble {
  position: absolute;
  top: 200rpx;
  right: 40rpx;
  width: 300rpx;
  min-height: 120rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  z-index: 6;
  animation: bubbleFloat 2s ease-in-out infinite alternate;

  &::before {
    content: '';
    position: absolute;
    left: -20rpx;
    top: 30rpx;
    width: 0;
    height: 0;
    border-top: 20rpx solid transparent;
    border-bottom: 20rpx solid transparent;
    border-right: 20rpx solid rgba(255, 255, 255, 0.95);
  }
}

.speech-text {
  font-size: 28rpx;
  color: #333;
  text-align: left;
  line-height: 1.4;
}

/* 信息卡片 */
.info-card {
  position: absolute;
  bottom: 200rpx;
  left: 20rpx;
  width: 280rpx;
  min-height: 300rpx;
  background: rgba(139, 69, 19, 0.9);
  border-radius: 15rpx;
  padding: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.3);
  z-index: 7;
  transition: transform 0.3s ease;

  &.enlarged {
    transform: scale(1.2);
    z-index: 100;
  }
}

.info-content {
  color: #FFD700;
  font-size: 24rpx;
  line-height: 1.5;
}

.info-name {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  text-align: center;
  color: #FFF;
}

.info-item {
  margin-bottom: 12rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;

  &::before {
    content: '•';
    color: #FFD700;
    margin-right: 8rpx;
  }
}

/* 底部按钮区域 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 160rpx;
  background: rgba(139, 69, 19, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.buttons {
  display: flex;
  gap: 80rpx;
  align-items: center;
  justify-content: center;
}

.hire-button, .reject-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);

  &:active {
    transform: scale(0.95);
  }
}

.hire-button {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);

  &:active {
    background: linear-gradient(45deg, #388E3C, #4CAF50);
  }
}

.reject-button {
  background: linear-gradient(45deg, #F44336, #EF5350);

  &:active {
    background: linear-gradient(45deg, #D32F2F, #F44336);
  }
}

.targets {
  position: absolute;
  top: 20%;
  left: 2%;
  width: 120rpx;
  height: 600rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  z-index: 4;
}

.target-slot {
  width: 100rpx;
  height: 100rpx;
  background-image: url('../../images/target-slot.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;

  &.filled {
    opacity: 1;
  }
}

.tools {
  position: absolute;
  top: 50%;
  right: 2%;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  z-index: 4;
}

.detector, .time-add {
  width: 120rpx;
  height: 120rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.detector {
  background-image: url('../../images/探测图标.png');
}

.time-add {
  background-image: url('../../images/加时图标.png');
}

.scanner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 10;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 70%;
  background-image: url('../../images/扫描时的框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.scan-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;
  height: 20rpx;
  background-image: url('../../images/scan-bar.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: scanMove 2s linear infinite;
}

@keyframes scanMove {
  0% { top: 15%; }
  100% { top: 85%; }
}

@keyframes bubbleFloat {
  0% { transform: translateY(0rpx); }
  100% { transform: translateY(-10rpx); }
}

@keyframes characterPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: 300rpx;
  height: 300rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 20;

  &.success {
    background-image: url('../../images/招聘成功的盖章.png');
  }

  &.fail {
    background-image: url('../../images/招聘失败的淘汰盖章.png');
  }
}

.start-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-title {
  font-size: 5rem;
  color: white;
  margin-bottom: 60rpx;
  font-weight: bold;
}

.game-description {
  font-size: 2.4rem;
  color: white;
  margin-bottom: 100rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}

.start-button, .restart-button {
  padding: 30rpx 80rpx;
  border-radius: 60rpx;
  border: none;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.start-button {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.5);

  &:active {
    transform: translateY(-10rpx);
    box-shadow: 0 14rpx 40rpx rgba(255, 107, 107, 0.7);
  }
}

.restart-button {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  box-shadow: 0 8rpx 30rpx rgba(79, 172, 254, 0.5);

  &:active {
    transform: translateY(-10rpx);
    box-shadow: 0 14rpx 40rpx rgba(79, 172, 254, 0.7);
  }
}

.start-button-text, .restart-button-text {
  font-size: 3rem;
  color: white;
  font-weight: bold;
}

.game-over {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-over-title {
  font-size: 6rem;
  color: white;
  margin-bottom: 40rpx;
  font-weight: bold;
}

.result-text {
  font-size: 3rem;
  color: white;
  margin-bottom: 60rpx;
  text-align: center;
  max-width: 80%;
  line-height: 1.5;
}