/* 三国打工人游戏样式 - 微信小程序版本（带详细尺寸注释） */

/* 
 * 🎯 尺寸调整说明：
 * - rpx: 微信小程序响应式单位，750rpx = 屏幕宽度
 * - %: 相对于父元素的百分比
 * - vw/vh: 相对于视窗的百分比
 * - rem: 相对于根元素字体大小的单位
 * 
 * 🔧 标记说明：
 * - 🔧 表示可以调整的尺寸参数
 * - 📱 表示影响移动端显示的重要参数
 * - 🎨 表示影响视觉效果的参数
 */

.game-container {
  position: relative;
  width: 100vw;  /* 🔧 游戏容器宽度：占满整个屏幕宽度 */
  height: 100vh; /* 🔧 游戏容器高度：占满整个屏幕高度 */
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  margin: 0;     /* 🔧 确保没有外边距 */
  padding: 0;    /* 🔧 确保没有内边距 */
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;  /* 🎨 背景图片宽度：占满容器 */
  height: 100%; /* 🎨 背景图片高度：占满容器 */
  background-image: url('../../images/游戏背景.png');
  background-size: cover;
  background-position: center;
  z-index: 0;
}

/* ==================== 顶部区域 ==================== */
.title-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200rpx; /* 🔧 标题栏高度：200rpx，可调整 */
  z-index: 10;
}

.title {
  position: absolute;
  top: 20rpx;     /* 🔧 标题距离顶部：20rpx，可调整 */
  left: 50%;
  transform: translateX(-50%);
  width: 70%;     /* 🔧 标题宽度：占屏幕70%，可调整为60%-80% */
  max-width: 800rpx; /* 🔧 标题最大宽度：800rpx，可调整 */
  height: auto;
  z-index: 2;
}

/* ==================== 计时器 ==================== */
.timer {
  position: absolute;
  top: 20rpx;    /* 🔧 计时器距离顶部：120rpx，可调整 */
  left: 50%;
  transform: translateX(-50%);
  width: 280rpx;  /* 🔧 计时器宽度：280rpx，可调整 */
  height: 100rpx; /* 🔧 计时器高度：100rpx，可调整 */
  background-image: url('../../images/倒计时框.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 1.5rem; /* 🔧 计时器文字大小：1.5rem*/
  font-weight: bold;
  color: white;     /* 🎨 计时器文字颜色：白色，可调整 */
  z-index: 3;
}

/* ==================== 角色区域 ==================== */
.character {
  position: absolute;
  top: 40%;       /* 🔧 角色垂直位置：45%，可调整为40%-50% */
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70%;     /* 🔧 角色宽度：25%，可调整为20%-30% */
  max-width: 500rpx; /* 🔧 角色最大宽度：300rpx，可调整 */
  height: 50%;    /* 🔧 角色高度：50%，可调整为40%-60% */
  max-height: 800rpx; /* 🔧 角色最大高度：600rpx，可调整 */
  object-fit: contain;
  z-index: 3;
}

/* ==================== 对话气泡 ==================== */
.speech-bubble {
  position: absolute;
  top: 15%;       /* 🔧 对话气泡垂直位置：25%，可调整为20%-30% */
  right: 2%;      /* 🔧 对话气泡距离右边：8%，可调整为5%-15% */
  width: 30%;     /* 🔧 对话气泡宽度：32%，可调整为25%-40% */
  max-width: 400rpx; /* 🔧 对话气泡最大宽度：400rpx，可调整 */
  height: 12%;    /* 🔧 对话气泡高度：18%，可调整为15%-25% */
  min-height: 100rpx; /* 🔧 对话气泡最小高度：120rpx，可调整 */
  background-image: url('../../images/主角色说话弹窗.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20rpx; /* 🔧 对话气泡内边距：30rpx，可调整为20rpx-40rpx */
  z-index: 5;
}

.speech-text {
  font-size: 0.9em; /* 🔧 对话文字大小：1.8rem，可调整为1.4rem-2.2rem */
  text-align: center;
  color: #333;      /* 🎨 对话文字颜色：深灰色，可调整 */
  width: 80%;       /* 🔧 对话文字宽度：80%，可调整为70%-90% */
  line-height: 1.3; /* 🔧 对话文字行高：1.3，可调整为1.2-1.5 */
  font-weight: bold; /* 🔧 对话文字加粗 */
}

/* ==================== 信息卡片 ==================== */
.info-card {
  position: absolute;  /* 🔧 使用absolute定位 */
  bottom: -10rpx;      /* 🔧 信息卡片距离底部：负值强制贴底 */
  left: -10rpx;        /* 🔧 信息卡片距离左边：负值强制贴左 */
  width: 50%;          /* 🔧 信息卡片宽度：50%，可调整为40%-60% */
  max-width: 400rpx;   /* 🔧 信息卡片最大宽度：400rpx，可调整 */
  height: 65%;         /* 🔧 信息卡片高度：65%，可调整为50%-70% */
  max-height: 800rpx;  /* 🔧 信息卡片最大高度：800rpx，可调整 */
  background-image: url('../../images/左下角资料信息图.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom left; /* 🔧 背景图片对齐到左下角 */
  transition: all 0.3s ease;
  transform-origin: bottom left;
  z-index: 4;
  margin: 0;           /* 🔧 确保没有外边距 */
  padding: 0;          /* 🔧 确保没有内边距 */
}

.info-card.enlarged {
  transform: scale(1.4); /* 🔧 信息卡片放大倍数：1.4，可调整为1.2-1.6 */
  z-index: 100;
}

.info-card.drag-target {
  box-shadow: 0 0 20rpx #ff4444; /* 🎨 拖拽高亮效果：红色阴影，可调整颜色和大小 */
  transform: scale(1.05); /* 🔧 拖拽时缩放：1.05，可调整 */
}

.info-content {
  position: absolute;
  top: 25%;       /* 🔧 信息内容垂直位置：25%，可调整为20%-30% */
  left: 15%;      /* 🔧 信息内容距离左边：15%，可调整为10%-20% */
  width: 70%;     /* 🔧 信息内容宽度：70%，可调整为60%-80% */
  height: 60%;    /* 🔧 信息内容高度：60%，可调整为50%-70% */
  display: none;  /* 隐藏信息内容，因为背景图片已包含文字 */
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 1.4rem; /* 🔧 信息文字大小：1.4rem，可调整为1.2rem-1.8rem */
  color: #333;      /* 🎨 信息文字颜色：深灰色，可调整 */
  line-height: 1.2; /* 🔧 信息文字行高：1.2，可调整为1.1-1.4 */
}

.info-name {
  font-size: 2.2rem; /* 🔧 姓名文字大小：2.2rem，可调整为1.8rem-2.6rem */
  font-weight: bold;
  margin-bottom: 8rpx; /* 🔧 姓名下边距：8rpx，可调整为5rpx-15rpx */
  display: none; /* 隐藏姓名文字，因为背景图片已包含 */
}

.info-item {
  margin-bottom: 8rpx; /* 🔧 信息项下边距：8rpx，可调整为5rpx-15rpx */
  font-size: 1.2rem;   /* 🔧 信息项文字大小：1.2rem，可调整为1rem-1.6rem */
  display: none; /* 隐藏信息项文字，因为背景图片已包含 */
}

/* ==================== 底部按钮区域 ==================== */
.bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 200rpx; /* 🔧 底部栏高度：200rpx，可调整 */
  z-index: 2;
}

.buttons {
  position: absolute;
  bottom: 8%;     /* 🔧 按钮距离底部：8%，可调整为5%-15% */
  right: 3%;      /* 🔧 按钮距离右边：3%，可调整为2%-8% */
  display: flex;
  gap: 30rpx;     /* 🔧 按钮间距：30rpx，可调整为20rpx-50rpx */
  z-index: 5;
}

.hire-button, .reject-button {
  width: 140rpx;  /* 🔧 按钮宽度：140rpx，可调整为120rpx-180rpx */
  height: 140rpx; /* 🔧 按钮高度：140rpx，可调整为120rpx-180rpx */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
  cursor: grab;
}

.hire-button:active, .reject-button:active {
  cursor: grabbing;
  transform: scale(1.1); /* 🔧 按钮点击缩放：1.1，可调整为1.05-1.2 */
}

/* ==================== 左侧目标区域 ==================== */
.targets {
  position: absolute;
  top: 22%;       /* 🔧 目标区域垂直位置：22%，可调整为18%-28% */
  left: 1%;       /* 🔧 目标区域距离左边：1%，可调整为0%-5% */
  width: 100rpx;  /* 🔧 目标区域宽度：100rpx，可调整为80rpx-120rpx */
  height: 500rpx; /* 🔧 目标区域高度：500rpx，可调整为400rpx-600rpx */
  display: flex;
  flex-direction: column;
  gap: 15rpx;     /* 🔧 目标槽间距：15rpx，可调整为10rpx-25rpx */
  z-index: 4;
}

.target-slot {
  width: 80rpx;   /* 🔧 目标槽宽度：80rpx，可调整为60rpx-100rpx */
  height: 80rpx;  /* 🔧 目标槽高度：80rpx，可调整为60rpx-100rpx */
  background-image: url('../../images/target-slot.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.5;   /* 🎨 未填充时透明度：0.5，可调整为0.3-0.7 */
}

.target-slot.filled {
  opacity: 1;     /* 🎨 填充后透明度：1（完全不透明） */
}

/* ==================== 右侧工具区域 ==================== */
.tools {
  position: absolute;
  top: 45%;       /* 🔧 工具区域垂直位置：45%，可调整为40%-50% */
  right: 1%;      /* 🔧 工具区域距离右边：1%，可调整为0%-5% */
  display: flex;
  flex-direction: column;
  gap: 30rpx;     /* 🔧 工具按钮间距：30rpx，可调整为20rpx-50rpx */
  z-index: 4;
}

.detector, .time-add {
  width: 100rpx;  /* 🔧 工具按钮宽度：100rpx，可调整为80rpx-120rpx */
  height: 100rpx; /* 🔧 工具按钮高度：100rpx，可调整为80rpx-120rpx */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.2s ease;
}

.detector:active, .time-add:active {
  transform: scale(0.95); /* 🔧 工具按钮点击缩放：0.95，可调整为0.9-0.98 */
}

/* ==================== 扫描器界面 ==================== */
.scanner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.scan-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}

.scan-frame {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;     /* 🔧 扫描框宽度：80%，可调整为70%-90% */
  height: 70%;    /* 🔧 扫描框高度：70%，可调整为60%-80% */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.scan-bar {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 70%;     /* 🔧 扫描线宽度：70%，可调整为60%-80% */
  height: 20rpx;  /* 🔧 扫描线高度：20rpx，可调整为15rpx-30rpx */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  animation: scanMove 2s linear infinite; /* 🔧 扫描动画时长：2s，可调整为1s-3s */
}

/* ==================== 扫描结果显示 ==================== */
.scan-result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 350rpx;  /* 🔧 扫描结果宽度：350rpx，可调整为300rpx-400rpx */
  height: 180rpx; /* 🔧 扫描结果高度：180rpx，可调整为150rpx-220rpx */
  z-index: 15;
}

.result-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.result-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 2.2rem; /* 🔧 扫描结果文字大小：2.2rem，可调整为1.8rem-2.6rem */
  font-weight: bold;
  color: #333;      /* 🎨 扫描结果文字颜色：深灰色，可调整 */
  text-align: center;
  line-height: 1.2; /* 🔧 扫描结果文字行高：1.2，可调整为1.1-1.4 */
}

/* 扫描动画关键帧 */
@keyframes scanMove {
  0% { top: 15%; }   /* 🔧 扫描线起始位置：15%，可调整为10%-20% */
  100% { top: 85%; } /* 🔧 扫描线结束位置：85%，可调整为80%-90% */
}

/* ==================== 拖拽相关 ==================== */
.dragging-stamp {
  position: fixed;
  width: 80rpx;   /* 🔧 拖拽印章宽度：80rpx，可调整为60rpx-120rpx */
  height: 80rpx;  /* 🔧 拖拽印章高度：80rpx，可调整为60rpx-120rpx */
  z-index: 1000;
  pointer-events: none;
  opacity: 0.8;   /* 🎨 拖拽印章透明度：0.8，可调整为0.6-1.0 */
}

.stamp-image {
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* ==================== 进度条 ==================== */
.stamp-progress-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120rpx;  /* 🔧 进度条容器宽度：120rpx，可调整为100rpx-150rpx */
  height: 120rpx; /* 🔧 进度条容器高度：120rpx，可调整为100rpx-150rpx */
  z-index: 50;
}

.stamp-progress-bg {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3); /* 🎨 进度条背景：半透明黑色，可调整透明度 */
  border: 4rpx solid rgba(255, 255, 255, 0.3); /* 🔧 进度条边框：4rpx，可调整为2rpx-8rpx */
}

.stamp-progress-fill {
  position: absolute;
  top: -4rpx;     /* 🔧 进度条填充偏移：-4rpx，需与边框宽度匹配 */
  left: -4rpx;    /* 🔧 进度条填充偏移：-4rpx，需与边框宽度匹配 */
  width: calc(100% + 8rpx);  /* 🔧 进度条填充宽度：边框宽度的2倍 */
  height: calc(100% + 8rpx); /* 🔧 进度条填充高度：边框宽度的2倍 */
  border-radius: 50%;
  border: 4rpx solid transparent; /* 🔧 进度条填充边框：4rpx，需与背景边框匹配 */
  border-top-color: #ff4444;     /* 🎨 进度条颜色：红色，可调整 */
  transition: transform 0.1s ease;
  transform-origin: center;
}

.stamp-progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24rpx; /* 🔧 进度条文字大小：24rpx，可调整为20rpx-30rpx */
  font-weight: bold;
  color: white;     /* 🎨 进度条文字颜色：白色，可调整 */
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.8); /* 🎨 进度条文字阴影：可调整 */
}

/* ==================== 盖章结果覆盖层 ==================== */
.stamp-result-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
  background: rgba(0, 0, 0, 0.5); /* 🎨 结果覆盖层背景：半透明黑色，可调整透明度 */
}

.stamp-result-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: stampAppear 0.5s ease-out; /* 🔧 盖章出现动画时长：0.5s，可调整为0.3s-1s */
}

.stamp-result-text {
  font-size: 48rpx; /* 🔧 盖章结果文字大小：48rpx，可调整为40rpx-60rpx */
  font-weight: bold;
  color: white;     /* 🎨 盖章结果文字颜色：白色，可调整 */
  margin-bottom: 40rpx; /* 🔧 盖章结果文字下边距：40rpx，可调整为30rpx-60rpx */
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.8); /* 🎨 盖章结果文字阴影：可调整 */
}

.stamp-result-icon {
  width: 200rpx;  /* 🔧 盖章结果图标宽度：200rpx，可调整为150rpx-250rpx */
  height: 200rpx; /* 🔧 盖章结果图标高度：200rpx，可调整为150rpx-250rpx */
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 8rpx solid white; /* 🔧 盖章结果图标边框：8rpx，可调整为5rpx-12rpx */
}

.stamp-result-icon.hire {
  background: linear-gradient(45deg, #4CAF50, #66BB6A); /* 🎨 录用图标背景：绿色渐变，可调整 */
}

.stamp-result-icon.reject {
  background: linear-gradient(45deg, #F44336, #EF5350); /* 🎨 淘汰图标背景：红色渐变，可调整 */
}

.stamp-icon-text {
  font-size: 80rpx; /* 🔧 盖章图标文字大小：80rpx，可调整为60rpx-100rpx */
  font-weight: bold;
  color: white;     /* 🎨 盖章图标文字颜色：白色，可调整 */
  text-shadow: 0 0 10rpx rgba(0, 0, 0, 0.8); /* 🎨 盖章图标文字阴影：可调整 */
}

/* 盖章出现动画关键帧 */
@keyframes stampAppear {
  0% {
    transform: scale(0) rotate(-180deg); /* 🔧 动画起始：缩放0，旋转-180度 */
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(-90deg); /* 🔧 动画中间：缩放1.2，旋转-90度 */
    opacity: 0.8; /* 🎨 动画中间透明度：0.8，可调整 */
  }
  100% {
    transform: scale(1) rotate(0deg); /* 🔧 动画结束：缩放1，旋转0度 */
    opacity: 1;
  }
}

/* ==================== 盖章结果图片 ==================== */
.result {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(1);
  width: 300rpx;  /* 🔧 盖章结果图片宽度：300rpx，可调整为250rpx-400rpx */
  height: 300rpx; /* 🔧 盖章结果图片高度：300rpx，可调整为250rpx-400rpx */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 20;
}

.result.success {
  background-image: url('../../images/招聘成功的盖章.png');
}

.result.fail {
  background-image: url('../../images/招聘失败的淘汰盖章.png');
}

/* ==================== 游戏开始界面 ==================== */
.start-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8); /* 🎨 开始界面背景：半透明黑色，可调整透明度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-title {
  font-size: 4.5rem; /* 🔧 游戏标题文字大小：4.5rem，可调整为3.5rem-5.5rem */
  color: white;      /* 🎨 游戏标题文字颜色：白色，可调整 */
  margin-bottom: 50rpx; /* 🔧 游戏标题下边距：50rpx，可调整为30rpx-80rpx */
  font-weight: bold;
  text-align: center;
}

.game-description {
  font-size: 2.2rem; /* 🔧 游戏描述文字大小：2.2rem，可调整为1.8rem-2.6rem */
  color: white;      /* 🎨 游戏描述文字颜色：白色，可调整 */
  margin-bottom: 80rpx; /* 🔧 游戏描述下边距：80rpx，可调整为60rpx-120rpx */
  text-align: center;
  max-width: 85%;    /* 🔧 游戏描述最大宽度：85%，可调整为75%-95% */
  line-height: 1.4;  /* 🔧 游戏描述行高：1.4，可调整为1.2-1.6 */
}

/* ==================== 按钮样式 ==================== */
.start-button, .restart-button {
  padding: 25rpx 70rpx; /* 🔧 按钮内边距：25rpx 70rpx，可调整 */
  border-radius: 50rpx; /* 🔧 按钮圆角：50rpx，可调整为30rpx-80rpx */
  border: none;
  transition: all 0.3s ease; /* 🔧 按钮动画时长：0.3s，可调整为0.2s-0.5s */
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 200rpx; /* 🔧 按钮最小宽度：200rpx，可调整为150rpx-250rpx */
}

.start-button {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e); /* 🎨 开始按钮背景：红色渐变，可调整 */
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.5);   /* 🎨 开始按钮阴影：可调整 */
}

.start-button:active {
  transform: translateY(-8rpx); /* 🔧 开始按钮点击位移：-8rpx，可调整为-5rpx到-12rpx */
  box-shadow: 0 12rpx 35rpx rgba(255, 107, 107, 0.7); /* 🎨 开始按钮点击阴影：可调整 */
}

.restart-button {
  background: linear-gradient(45deg, #4facfe, #00f2fe); /* 🎨 重新开始按钮背景：蓝色渐变，可调整 */
  box-shadow: 0 8rpx 30rpx rgba(79, 172, 254, 0.5);    /* 🎨 重新开始按钮阴影：可调整 */
}

.restart-button:active {
  transform: translateY(-8rpx); /* 🔧 重新开始按钮点击位移：-8rpx，可调整为-5rpx到-12rpx */
  box-shadow: 0 12rpx 35rpx rgba(79, 172, 254, 0.7); /* 🎨 重新开始按钮点击阴影：可调整 */
}

.start-button-text, .restart-button-text {
  font-size: 2.8rem; /* 🔧 按钮文字大小：2.8rem，可调整为2.2rem-3.4rem */
  color: white;      /* 🎨 按钮文字颜色：白色，可调整 */
  font-weight: bold;
}

/* ==================== 游戏结束界面 ==================== */
.game-over {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8); /* 🎨 结束界面背景：半透明黑色，可调整透明度 */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.game-over-title {
  font-size: 5.5rem; /* 🔧 游戏结束标题文字大小：5.5rem，可调整为4.5rem-6.5rem */
  color: white;      /* 🎨 游戏结束标题文字颜色：白色，可调整 */
  margin-bottom: 35rpx; /* 🔧 游戏结束标题下边距：35rpx，可调整为25rpx-50rpx */
  font-weight: bold;
  text-align: center;
}

.result-text {
  font-size: 2.8rem; /* 🔧 结果文字大小：2.8rem，可调整为2.2rem-3.4rem */
  color: white;      /* 🎨 结果文字颜色：白色，可调整 */
  margin-bottom: 50rpx; /* 🔧 结果文字下边距：50rpx，可调整为30rpx-80rpx */
  text-align: center;
  max-width: 85%;    /* 🔧 结果文字最大宽度：85%，可调整为75%-95% */
  line-height: 1.4;  /* 🔧 结果文字行高：1.4，可调整为1.2-1.6 */
}

/*
 * 📋 尺寸调整总结：
 *
 * 🎯 主要可调整的尺寸参数：
 * 1. 标题区域：top: 20rpx, width: 70%, max-width: 800rpx
 * 2. 计时器：top: 120rpx, width: 280rpx, height: 100rpx, font-size: 3.5rem
 * 3. 角色：top: 45%, width: 25%, height: 50%, max-width: 300rpx, max-height: 600rpx
 * 4. 对话气泡：top: 25%, right: 8%, width: 32%, height: 18%, font-size: 1.8rem
 * 5. 信息卡片：bottom: 18%, left: 3%, width: 32%, height: 55%
 * 6. 信息文字：font-size: 1.4rem (内容), 2.2rem (姓名), 1.2rem (详情)
 * 7. 底部按钮：bottom: 8%, right: 3%, width/height: 140rpx, gap: 30rpx
 * 8. 左侧目标：top: 22%, left: 1%, width/height: 80rpx, gap: 15rpx
 * 9. 右侧工具：top: 45%, right: 1%, width/height: 100rpx, gap: 30rpx
 * 10. 扫描相关：width: 80%/70%, height: 70%, font-size: 2.2rem
 * 11. 进度条：width/height: 120rpx, font-size: 24rpx
 * 12. 盖章结果：width/height: 200rpx, font-size: 48rpx/80rpx
 * 13. 游戏界面：font-size: 4.5rem (标题), 2.2rem (描述), 2.8rem (按钮)
 *
 * 📱 移动端适配建议：
 * - 小屏设备：将所有尺寸乘以 0.8-0.9
 * - 大屏设备：将所有尺寸乘以 1.1-1.2
 * - 字体大小：根据屏幕密度调整，保持可读性
 * - 触摸区域：确保按钮最小44rpx×44rpx
 */
