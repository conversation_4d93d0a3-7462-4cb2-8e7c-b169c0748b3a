(wx["webpackJsonp"]=wx["webpackJsonp"]||[]).push([[907],{3114:function(t,e,n){"use strict";n.d(e,{A:function(){return I}});var r={};n.r(r),n.d(r,{logInterceptor:function(){return b},timeoutInterceptor:function(){return m}});var i=n(5518),o=n(9489),a=n(8322),u=n(3896),s=n(7819),c={ASCF:"ASCF",WEAPP:"WEAPP",SWAN:"SWAN",ALIPAY:"ALIPAY",TT:"TT",QQ:"QQ",JD:"JD",WEB:"WEB",RN:"RN",HARMONY:"HARMONY",QUICKAPP:"QUICKAPP",HARMONYHYBRID:"HARMONYHYBRID"};function l(){return c.WEAPP}var d=n(3029),f=n(2901),h=n(7766),v=function(){function t(e,n,r){(0,d.A)(this,t),this.index=r||0,this.requestParams=e||{},this.interceptors=n||[]}return(0,f.A)(t,[{key:"proceed",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.requestParams=t,this.index>=this.interceptors.length)throw new Error("chain \u53c2\u6570\u9519\u8bef, \u8bf7\u52ff\u76f4\u63a5\u4fee\u6539 request.chain");var e=this._getNextInterceptor(),n=this._getNextChain(),r=e(n),i=r.catch(function(t){return Promise.reject(t)});return Object.keys(r).forEach(function(t){return(0,h.Tn)(r[t])&&(i[t]=r[t])}),i}},{key:"_getNextInterceptor",value:function(){return this.interceptors[this.index]}},{key:"_getNextChain",value:function(){return new t(this.requestParams,this.interceptors,this.index+1)}}])}(),p=function(){function t(e){(0,d.A)(this,t),this.taroInterceptor=e,this.chain=new v}return(0,f.A)(t,[{key:"request",value:function(t){var e=this.chain,n=this.taroInterceptor;return e.interceptors=e.interceptors.filter(function(t){return t!==n}).concat(n),e.proceed(Object.assign({},t))}},{key:"addInterceptor",value:function(t){this.chain.interceptors.push(t)}},{key:"cleanInterceptors",value:function(){this.chain=new v}}])}();function g(t){return new p(function(e){return t(e.requestParams)})}function m(t){var e,n=t.requestParams,r=new Promise(function(r,i){var o=setTimeout(function(){clearTimeout(o),i(new Error("\u7f51\u7edc\u94fe\u63a5\u8d85\u65f6,\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01"))},n&&n.timeout||6e4);e=t.proceed(n),e.then(function(t){o&&(clearTimeout(o),r(t))}).catch(function(t){o&&clearTimeout(o),i(t)})});return!(0,h.b0)(e)&&(0,h.Tn)(e.abort)&&(r.abort=e.abort),r}function b(t){var e=t.requestParams,n=e.method,r=e.data,i=e.url;console.log("http ".concat(n||"GET"," --\x3e ").concat(i," data: "),r);var o=t.proceed(e),a=o.then(function(t){return console.log("http <-- ".concat(i," result:"),t),t});return(0,h.Tn)(o.abort)&&(a.abort=o.abort),a}var y=n(4467);function k(t){return t}function A(t){return function(e,n){t.preloadData=(0,h.Gv)(e)?e:(0,y.A)({},e,n)}}var w=750,E={640:1.17,750:1,828:.905},S=20,C=5,T="rpx";function x(t){return function(e){var n=e.designWidth,r=void 0===n?w:n,i=e.deviceRatio,o=void 0===i?E:i,a=e.baseFontSize,u=void 0===a?S:a,s=e.targetUnit,c=void 0===s?T:s,l=e.unitPrecision,d=void 0===l?C:l;t.config=t.config||{},t.config.designWidth=r,t.config.deviceRatio=o,t.config.baseFontSize=u,t.config.targetUnit=c,t.config.unitPrecision=d}}function L(t){return function(e){var n=t.config||{},r=n.baseFontSize,i=n.deviceRatio||E,o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return(0,h.Tn)(n.designWidth)?n.designWidth(t):n.designWidth||w}(e);if(!(o in i))throw new Error("deviceRatio \u914d\u7f6e\u4e2d\u4e0d\u5b58\u5728 ".concat(o," \u7684\u8bbe\u7f6e\uff01"));var a=n.targetUnit||T,u=n.unitPrecision||C,s=~~e,c=1/i[o];switch(a){case"rem":c*=2*r;break;case"px":c*=2;break}var l=s/c;return u>=0&&u<=100&&(l=Number(l.toFixed(u))),l+a}}var I={Behavior:k,getEnv:l,ENV_TYPE:c,Link:p,interceptors:r,Current:i.E,getCurrentInstance:i.n,options:o.f,nextTick:a.d,eventCenter:u.k,Events:s.s,getInitPxTransform:x,interceptorify:g};I.initPxTransform=x(I),I.preload=A(i.E),I.pxTransform=L(I)},1291:function(t,e,n){"use strict";n.d(e,{ND:function(){return et}});var r=n(4467),i=n(5544),o=n(3029),a=n(2901),u=n(6919),s=n(5501),c=n(436),l=n(7225),d=n(7766),f=n(6021),h=n(5518),v=n(8870),p=n(4733),g=n(6541),m=n(2709),b=n(7768),y={PageContext:l.MZ,R:l.MZ},k="taro-app";function A(t,e){var n,r=e.prototype;return!(null===(n=e.displayName)||void 0===n?void 0:n.includes("Connect"))&&((0,d.Tn)(e.render)||!!(null===r||void 0===r?void 0:r.isReactComponent)||r instanceof t.Component)}function w(t){return(0,d.cy)(t)?t:t?[t]:[]}function E(t){return t.writable=!0,t.enumerable=!0,t}function S(t){h.E.router=Object.assign({params:null===t||void 0===t?void 0:t.query},t)}var C,T,x=function(t){return function(e){var n=y.R,r=y.PageContext,i=n.useContext(r)||k,o=n.useRef(),a=n.useRef(e);a.current!==e&&(a.current=e),n.useLayoutEffect(function(){var e=o.current=(0,v.zk)(i),n=!1;e||(n=!0,o.current=Object.create(null),e=o.current);var r=function(){return a.current.apply(a,arguments)};return(0,d.Tn)(e[t])?e[t]=[e[t],r]:e[t]=[].concat((0,c.A)(e[t]||[]),[r]),n&&(0,v.wT)(e,i),function(){var e=o.current;if(e){var n=e[t];n===r?e[t]=void 0:(0,d.cy)(n)&&(e[t]=n.filter(function(t){return t!==r})),o.current=void 0}}},[])}},L=x("componentDidHide"),I=x("componentDidShow"),P=x("onError"),N=x("onUnhandledRejection"),O=x("onLaunch"),_=x("onPageNotFound"),R=x("onLoad"),M=x("onPageScroll"),B=x("onPullDownRefresh"),D=x("onPullIntercept"),G=x("onReachBottom"),F=x("onResize"),j=x("onUnload"),U=x("onAddToFavorites"),V=x("onOptionMenuClick"),H=x("onSaveExitState"),W=x("onShareAppMessage"),$=x("onShareTimeline"),K=x("onTitleClick"),z=x("onReady"),Y=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=y.R;return t?h.E.router:e.useMemo(function(){return h.E.router},[])},q=x("onTabItemTap"),J=function(){},Z=Object.freeze({__proto__:null,useAddToFavorites:U,useDidHide:L,useDidShow:I,useError:P,useLaunch:O,useLoad:R,useOptionMenuClick:V,usePageNotFound:_,usePageScroll:M,usePullDownRefresh:B,usePullIntercept:D,useReachBottom:G,useReady:z,useResize:F,useRouter:Y,useSaveExitState:H,useScope:J,useShareAppMessage:W,useShareTimeline:$,useTabItemTap:q,useTitleClick:K,useUnhandledRejection:N,useUnload:j}),Q=(0,p.F$)();function X(t){f.JL.tap("getLifecycle",function(t,e){return e=e.replace(/^on(Show|Hide)$/,"componentDid$1"),t[e]}),f.JL.tap("modifyMpEvent",function(t){Object.defineProperty(t,"type",{value:t.type.replace(/-/g,"")})}),f.JL.tap("batchedEventUpdates",function(e){null===t||void 0===t||t.unstable_batchedUpdates(e)}),f.JL.tap("mergePageInstance",function(t,e){t&&e&&("constructor"in t||Object.keys(t).forEach(function(n){var r=t[n],i=w(e[n]);e[n]=i.concat(r)}))})}function tt(t,e){return function(n){var r=A(t,n),i=function(t){return t&&(0,v.wT)(t,e)},c=r?{ref:i}:{forwardedRef:i,reactReduxForwardedRef:i};return y.PageContext===l.MZ&&(y.PageContext=t.createContext("")),function(t){function r(){var t;return(0,o.A)(this,r),t=(0,u.A)(this,r,arguments),t.state={hasError:!1},t}return(0,s.A)(r,t),(0,a.A)(r,[{key:"componentDidCatch",value:function(t,e){0}},{key:"render",value:function(){var t=this.state.hasError?[]:C(y.PageContext.Provider,{value:e},C(n,Object.assign(Object.assign({},this.props),c)));return C("root",{id:e},t)}}],[{key:"getDerivedStateFromError",value:function(t){var e,n;return null===(n=null===(e=h.E.app)||void 0===e?void 0:e.onError)||void 0===n||n.call(e,t.message+t.stack),{hasError:!0}}}])}(t.Component)}}function et(t,e,n,c){y.R=e,C=e.createElement,T=n,e.Fragment;var l,d,p=e.createRef(),w=A(e,t),x=new Promise(function(t){return d=t});function L(){return p.current}function I(t){x.then(function(){return t()})}function P(){var t,n,r=(null===c||void 0===c?void 0:c.appId)||"app",i=g.F.getElementById(r);if(null==i){var o=g.F.getElementById(m.MR);i=g.F.createElement(r),i.id=r,null===o||void 0===o||o.appendChild(i)}if((e.version||"").startsWith("18")){var a=T.createRoot(i);null===(t=a.render)||void 0===t||t.call(a,C(N))}else null===(n=T.render)||void 0===n||n.call(T,C(N),i)}X(T);var N=function(n){function r(t){var e;return(0,o.A)(this,r),e=(0,u.A)(this,r,[t]),e.pages=[],e.elements=[],l=e,d(e),e}return(0,s.A)(r,n),(0,a.A)(r,[{key:"mount",value:function(t,n,r){var i=tt(e,n)(t),o=n+Q(),a=function(){return C(i,{key:o,tid:n})};this.pages.push(a),this.forceUpdate(function(){return b.k.stop(m.zP),r.apply(void 0,arguments)})}},{key:"unmount",value:function(t,e){var n=this.elements,r=n.findIndex(function(e){return e.props.tid===t});n.splice(r,1),this.forceUpdate(e)}},{key:"render",value:function(){var e=this.pages,n=this.elements;while(e.length>0){var r=e.pop();n.push(r())}var i=null;return w&&(i={ref:p}),C(t,i,n.slice())}}])}(e.Component);P();var O=(0,i.A)(f.JL.call("getMiniLifecycleImpl").app,3),_=O[0],R=O[1],M=O[2],B=Object.create({render:function(t){l.forceUpdate(t)},mount:function(t,e,n){l?l.mount(t,e,n):x.then(function(r){return r.mount(t,e,n)})},unmount:function(t,e){l?l.unmount(t,e):x.then(function(n){return n.unmount(t,e)})}},(0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)((0,r.A)({config:E({configurable:!0,value:c})},_,E({value:function(t){var e=this;S(t);var n=function(){var n,r=L();if(e.$app=r,r){if(r.taroGlobalData){var i=r.taroGlobalData,o=Object.keys(i),a=Object.getOwnPropertyDescriptors(i);o.forEach(function(t){Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return i[t]},set:function(e){i[t]=e}})}),Object.defineProperties(e,a)}null===(n=r.onLaunch)||void 0===n||n.call(r,t)}D("onLaunch",t)};I(n)}})),R,E({value:function(t){S(t);var e=function(){var e,n=L();null===(e=null===n||void 0===n?void 0:n.componentDidShow)||void 0===e||e.call(n,t),D("onShow",t)};I(e)}})),M,E({value:function(){var t=function(){var t,e=L();null===(t=null===e||void 0===e?void 0:e.componentDidHide)||void 0===t||t.call(e),D("onHide")};I(t)}})),"onError",E({value:function(t){var e=function(){var e,n=L();null===(e=null===n||void 0===n?void 0:n.onError)||void 0===e||e.call(n,t),D("onError",t)};I(e)}})),"onUnhandledRejection",E({value:function(t){var e=function(){var e,n=L();null===(e=null===n||void 0===n?void 0:n.onUnhandledRejection)||void 0===e||e.call(n,t),D("onUnhandledRejection",t)};I(e)}})),"onPageNotFound",E({value:function(t){var e=function(){var e,n=L();null===(e=null===n||void 0===n?void 0:n.onPageNotFound)||void 0===e||e.call(n,t),D("onPageNotFound",t)};I(e)}})));function D(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];var i=(0,v.zk)(k);if(i){var o=L(),a=f.JL.call("getLifecycle",i,t);Array.isArray(a)&&a.forEach(function(t){return t.apply(o,n)})}}return h.E.app=B,B}(0,p.F$)();f.JL.tap("initNativeApi",function(t){for(var e in Z)t[e]=Z[e]})},118:function(t,e,n){"use strict";n.d(e,{EY:function(){return i},Ss:function(){return r},_V:function(){return o}});var r="view",i="text",o="image"},2457:function(t,e,n){"use strict";var r=n(4467),i=n(436),o=n(7766),a=n(7225),u=new Set(["addPhoneContact","authorize","canvasGetImageData","canvasPutImageData","canvasToTempFilePath","checkSession","chooseAddress","chooseImage","chooseInvoiceTitle","chooseLocation","chooseVideo","clearStorage","closeBLEConnection","closeBluetoothAdapter","closeSocket","compressImage","connectSocket","createBLEConnection","downloadFile","exitMiniProgram","getAvailableAudioSources","getBLEDeviceCharacteristics","getBLEDeviceServices","getBatteryInfo","getBeacons","getBluetoothAdapterState","getBluetoothDevices","getClipboardData","getConnectedBluetoothDevices","getConnectedWifi","getExtConfig","getFileInfo","getImageInfo","getLocation","getNetworkType","getSavedFileInfo","getSavedFileList","getScreenBrightness","getSetting","getStorage","getStorageInfo","getSystemInfo","getUserInfo","getWifiList","hideHomeButton","hideShareMenu","hideTabBar","hideTabBarRedDot","loadFontFace","login","makePhoneCall","navigateBack","navigateBackMiniProgram","navigateTo","navigateToBookshelf","navigateToMiniProgram","notifyBLECharacteristicValueChange","hideKeyboard","hideLoading","hideNavigationBarLoading","hideToast","openBluetoothAdapter","openDocument","openLocation","openSetting","pageScrollTo","previewImage","queryBookshelf","reLaunch","readBLECharacteristicValue","redirectTo","removeSavedFile","removeStorage","removeTabBarBadge","requestSubscribeMessage","saveFile","saveImageToPhotosAlbum","saveVideoToPhotosAlbum","scanCode","sendSocketMessage","setBackgroundColor","setBackgroundTextStyle","setClipboardData","setEnableDebug","setInnerAudioOption","setKeepScreenOn","setNavigationBarColor","setNavigationBarTitle","setScreenBrightness","setStorage","setTabBarBadge","setTabBarItem","setTabBarStyle","showActionSheet","showFavoriteGuide","showLoading","showModal","showShareMenu","showTabBar","showTabBarRedDot","showToast","startBeaconDiscovery","startBluetoothDevicesDiscovery","startDeviceMotionListening","startPullDownRefresh","stopBeaconDiscovery","stopBluetoothDevicesDiscovery","stopCompass","startCompass","startAccelerometer","stopAccelerometer","showNavigationBarLoading","stopDeviceMotionListening","stopPullDownRefresh","switchTab","uploadFile","vibrateLong","vibrateShort","writeBLECharacteristicValue"]);function s(t){return function(){var e,n=null===(e=t.getSystemInfoSync)||void 0===e?void 0:e.call(t);if(!n)return!1;var r=n.platform,i=r.toLowerCase();return"android"===i||"devtools"===i}}function c(t){return function(e){e=e?(0,o.Kg)(e)?{url:e}:e:{};var n,r=e.success,i=e.fail,a=e.complete,u=new Promise(function(o,u){e.success=function(t){r&&r(t),o(t)},e.fail=function(t){i&&i(t),u(t)},e.complete=function(t){a&&a(t)},n=t.request(e)});return f(n,u),u.abort=function(t){return t&&t(),n&&n.abort(),u},u}}function l(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.needPromiseApis||[],s=new Set([].concat((0,i.A)(r),(0,i.A)(u))),c=["getEnv","interceptors","Current","getCurrentInstance","options","nextTick","eventCenter","Events","preload","webpackJsonp"],l=new Set(n.isOnlyPromisify?r:Object.keys(e).filter(function(t){return-1===c.indexOf(t)}));n.modifyApis&&n.modifyApis(l),l.forEach(function(r){if(s.has(r)){var i=r;t[i]=function(){for(var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length,o=new Array(r>1?r-1:0),u=1;u<r;u++)o[u-1]=arguments[u];var s=i;if("string"===typeof t)return o.length?e[s].apply(e,[t].concat(o)):e[s](t);if(n.transformMeta){var c=n.transformMeta(s,t);if(s=c.key,t=c.options,!e.hasOwnProperty(s))return(0,a.Zb)(s)()}var l=null,d=Object.assign({},t);(0,a.vk)(s,t);var h=new Promise(function(r,i){d.success=function(e){var i,o;null===(i=n.modifyAsyncResult)||void 0===i||i.call(n,s,e),null===(o=t.success)||void 0===o||o.call(t,e),r("connectSocket"===s?Promise.resolve().then(function(){return l?Object.assign(l,e):e}):e)},d.fail=function(e){var n;null===(n=t.fail)||void 0===n||n.call(t,e),i(e)},d.complete=function(e){var n;null===(n=t.complete)||void 0===n||n.call(t,e)},l=o.length?e[s].apply(e,[d].concat(o)):e[s](d)});return["uploadFile","downloadFile"].includes(s)&&(f(l,h),h.progress=function(t){return null===l||void 0===l||l.onProgressUpdate(t),h},h.abort=function(t){return null===t||void 0===t||t(),null===l||void 0===l||l.abort(),h}),h}}else{var u=r;if(n.transformMeta&&(u=n.transformMeta(r,{}).key),!e.hasOwnProperty(u))return void(t[r]=(0,a.Zb)(r));(0,o.Tn)(e[r])?t[r]=function(){for(var t=arguments.length,i=new Array(t),o=0;o<t;o++)i[o]=arguments[o];return n.handleSyncApis?n.handleSyncApis(r,e,i):e[u].apply(e,i)}:t[r]=e[u]}}),!n.isOnlyPromisify&&d(t,e,n)}function d(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};t.canIUseWebp=s(t),t.getCurrentPages=getCurrentPages||(0,a.Zb)("getCurrentPages"),t.getApp=getApp||(0,a.Zb)("getApp"),t.env=e.env||{};try{t.requirePlugin=requirePlugin||(0,a.Zb)("requirePlugin")}catch(e){t.requirePlugin=(0,a.Zb)("requirePlugin")}var r=n.request||c(e);function i(t){return r(t.requestParams)}var o=new t.Link(i);t.request=o.request.bind(o),t.addInterceptor=o.addInterceptor.bind(o),t.cleanInterceptors=o.cleanInterceptors.bind(o),t.miniGlobal=t.options.miniGlobal=e,t.getAppInfo=function(){return{platform:"mini",taroVersion:"4.1.3",designWidth:t.config.designWidth}},t.createSelectorQuery=h(t,e,"createSelectorQuery","exec"),t.createIntersectionObserver=h(t,e,"createIntersectionObserver","observe")}function f(t,e){if(t&&e){var n=["abort","onHeadersReceived","offHeadersReceived","onProgressUpdate","offProgressUpdate","onChunkReceived","offChunkReceived"];t&&n.forEach(function(n){n in t&&(e[n]=t[n].bind(t))})}}function h(t,e,n,r){return function(){var i=e[n].apply(e,arguments),o=i[r].bind(i);return i[r]=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];t.nextTick(function(){return o.apply(void 0,n)})},i}}var v=new Set(["addFileToFavorites","addVideoToFavorites","authPrivateMessage","checkIsAddedToMyMiniProgram","chooseContact","cropImage","disableAlertBeforeUnload","editImage","enableAlertBeforeUnload","getBackgroundFetchData","getChannelsLiveInfo","getChannelsLiveNoticeInfo","getFuzzyLocation","getGroupEnterInfo","getLocalIPAddress","getShareInfo","getUserProfile","getWeRunData","join1v1Chat","openChannelsActivity","openChannelsEvent","openChannelsLive","openChannelsUserProfile","openCustomerServiceChat","openVideoEditor","saveFileToDisk","scanItem","setEnable1v1Chat","setWindowSize","sendBizRedPacket","startFacialRecognitionVerify"]);function p(t){l(t,wx,{needPromiseApis:v,modifyApis:function(t){t.delete("lanDebug")},transformMeta:function(t,e){var n;return"showShareMenu"===t&&(e.menus=null===(n=e.showShareItems)||void 0===n?void 0:n.map(function(t){return"wechatFriends"===t?"shareAppMessage":"wechatMoment"===t?"shareTimeline":t})),{key:t,options:e}}}),t.cloud=wx.cloud,t.getTabBar=function(t){var e;if("function"===typeof(null===t||void 0===t?void 0:t.getTabBar))return null===(e=t.getTabBar())||void 0===e?void 0:e.$taroInstances},t.getRenderer=function(){var e,n,r;return null!==(r=null===(n=null===(e=t.getCurrentInstance())||void 0===e?void 0:e.page)||void 0===n?void 0:n.renderer)&&void 0!==r?r:"webview"}}var g="true",m="false",b="",y="0",k="{}",A={Progress:{"border-radius":y,"font-size":"16",duration:"30",bindActiveEnd:b},RichText:{space:b,"user-select":m,mode:"'default'"},Text:{"user-select":m,overflow:"visible","max-lines":""},Map:{polygons:"[]",subkey:b,rotate:y,skew:y,"max-scale":"20","min-scale":"3","enable-3D":m,"show-compass":m,"show-scale":m,"enable-overlooking":m,"enable-auto-max-overlooking":m,"enable-zoom":g,"enable-scroll":g,"enable-rotate":m,"enable-satellite":m,"enable-traffic":m,"enable-poi":g,"enable-building":g,setting:k,bindLabelTap:b,bindRegionChange:b,bindPoiTap:b,bindPolylineTap:b,bindAbilitySuccess:b,bindAbilityFailed:b,bindAuthSuccess:b,bindInterpolatePoint:b,bindError:b,bindAnchorPointTap:b},Button:{lang:"en","session-from":b,"send-message-title":b,"send-message-path":b,"send-message-img":b,"app-parameter":b,"show-message-card":m,"business-id":b,bindGetUserInfo:b,bindContact:b,bindGetPhoneNumber:b,bindGetRealTimePhoneNumber:b,bindChooseAvatar:b,bindError:b,bindOpenSetting:b,bindLaunchApp:b,bindAgreePrivacyAuthorization:b},Form:{"report-submit-timeout":y},Input:{"always-embed":m,"adjust-position":g,"hold-keyboard":m,"safe-password-cert-path":"","safe-password-length":"","safe-password-time-stamp":"","safe-password-nonce":"","safe-password-salt":"","safe-password-custom-hash":"","auto-fill":b,"cursor-color":"",bindKeyboardHeightChange:b,bindNicknameReview:b,bindSelectionChange:b,bindKeyboardCompositionStart:b,bindKeyboardCompositionUpdate:b,bindKeyboardCompositionEnd:b},Picker:{"header-text":b,level:"region"},PickerView:{"immediate-change":m,bindPickStart:b,bindPickEnd:b},Slider:{color:"'#e9e9e9'","selected-color":"'#1aad19'"},Textarea:{"show-confirm-bar":g,"adjust-position":g,"hold-keyboard":m,"disable-default-padding":m,"confirm-type":"'return'","confirm-hold":m,"adjust-keyboard-to":"'cursor'",bindKeyboardHeightChange:b,bindSelectionChange:b,bindKeyboardCompositionStart:b,bindKeyboardCompositionUpdate:b,bindKeyboardCompositionEnd:b},ScrollView:{"enable-flex":m,"scroll-anchoring":m,enhanced:m,"using-sticky":m,"paging-enabled":m,"enable-passive":m,"refresher-enabled":m,"refresher-threshold":"45","refresher-default-style":"'black'","refresher-background":"'#FFF'","refresher-triggered":m,bounces:g,"show-scrollbar":g,"fast-deceleration":m,type:"'list'","associative-container":"''",reverse:m,clip:g,"enable-back-to-top":m,"cache-extent":b,"min-drag-distance":"18","scroll-into-view-within-extent":m,"scroll-into-view-alignment":"'start'",padding:"[0,0,0,0]","refresher-two-level-enabled":m,"refresher-two-level-triggered":m,"refresher-two-level-threshold":"150","refresher-two-level-close-threshold":"80","refresher-two-level-scroll-enabled":m,"refresher-ballistic-refresh-enabled":m,"refresher-two-level-pinned":m,bindDragStart:b,bindDragging:b,bindDragEnd:b,bindRefresherPulling:b,bindRefresherRefresh:b,bindRefresherRestore:b,bindRefresherAbort:b,bindScrollStart:b,bindScrollEnd:b,bindRefresherWillRefresh:b,bindRefresherStatusChange:b},StickySection:{"push-pinned-header":g,padding:"[0, 0, 0, 0]"},GridView:{type:"'aligned'","cross-axis-count":"2","max-cross-axis-extent":y,"main-axis-gap":y,"cross-axis-gap":y,padding:"[0, 0, 0, 0]"},GridBuilder:{type:"'aligned'",list:"[]","cross-axis-count":"2","max-cross-axis-extent":y,"main-axis-gap":y,"cross-axis-gap":y,padding:"[0, 0, 0, 0]",bindItemBuild:b,bindItemDispose:b},ListView:{padding:"[0, 0, 0, 0]"},ListBuilder:{list:"[]",type:"static",padding:"[0, 0, 0, 0]","child-count":b,"child-height":b,bindItemBuild:b,bindItemDispose:b},StickyHeader:{"offset-top":"0",padding:"[0, 0, 0, 0]"},Swiper:{"snap-to-edge":m,"easing-function":"'default'","layout-type":"'normal'","transformer-type":"'scaleAndFade'","indicator-type":"'normal'","indicator-margin":"10","indicator-spacing":"4","indicator-radius":"4","indicator-width":"8","indicator-height":"8","indicator-alignment":"'auto'","indicator-offset":"[0, 0]","scroll-with-animation":g,"cache-extent":"0"},SwiperItem:{"skip-hidden-item-layout":m},Navigator:{target:"'self'","app-id":b,path:b,"extra-data":b,version:"'version'"},Camera:{mode:"'normal'",resolution:"'medium'","frame-size":"'medium'",bindInitDone:b,bindScanCode:b},Image:{webp:m,"show-menu-by-longpress":m,"fade-in":m},LivePlayer:{mode:"'live'","sound-mode":"'speaker'","auto-pause-if-navigate":g,"auto-pause-if-open-native":g,"picture-in-picture-mode":"[]","enable-auto-rotation":m,"referrer-policy":"'no-referrer'","enable-casting":m,bindstatechange:b,bindfullscreenchange:b,bindnetstatus:b,bindAudioVolumeNotify:b,bindEnterPictureInPicture:b,bindLeavePictureInPicture:b,bindCastingUserSelect:b,bindCastingStateChange:b,bindCastingInterrupt:b},Video:{title:b,"play-btn-position":"'bottom'","enable-play-gesture":m,"auto-pause-if-navigate":g,"auto-pause-if-open-native":g,"vslide-gesture":m,"vslide-gesture-in-fullscreen":g,"show-bottom-progress":g,"ad-unit-id":b,"poster-for-crawler":b,"show-casting-button":m,"picture-in-picture-mode":"[]","enable-auto-rotation":m,"show-screen-lock-button":m,"show-snapshot-button":m,"show-background-playback-button":m,"background-poster":b,"referrer-policy":"'no-referrer'","is-drm":m,"is-live":m,"provision-url":b,"certificate-url":b,"license-url":b,"preferred-peak-bit-rate":b,bindProgress:b,bindLoadedMetadata:b,bindControlsToggle:b,bindEnterPictureInPicture:b,bindLeavePictureInPicture:b,bindSeekComplete:b,bindCastingUserSelect:b,bindCastingStateChange:b,bindCastingInterrupt:b,bindAdLoad:b,bindAdError:b,bindAdClose:b,bindAdPlay:b},Canvas:{type:b},Ad:{"ad-type":"'banner'","ad-theme":"'white'"},CoverView:{"marker-id":b,slot:b},Editor:{"read-only":m,placeholder:b,"show-img-size":m,"show-img-toolbar":m,"show-img-resize":m,focus:m,bindReady:b,bindFocus:b,bindBlur:b,bindInput:b,bindStatusChange:b,name:b},MatchMedia:{"min-width":b,"max-width":b,width:b,"min-height":b,"max-height":b,height:b,orientation:b},FunctionalPageNavigator:{version:"'release'",name:b,args:b,bindSuccess:b,bindFail:b,bindCancel:b},LivePusher:{url:b,mode:"'RTC'",autopush:m,muted:m,"enable-camera":g,"auto-focus":g,orientation:"'vertical'",beauty:y,whiteness:y,aspect:"'9:16'","min-bitrate":"200","max-bitrate":"1000","audio-quality":"'high'","waiting-image":b,"waiting-image-hash":b,zoom:m,"device-position":"'front'","background-mute":m,mirror:m,"remote-mirror":m,"local-mirror":m,"audio-reverb-type":y,"enable-mic":g,"enable-agc":m,"enable-ans":m,"audio-volume-type":"'voicecall'","video-width":"360","video-height":"640","beauty-style":"'smooth'",filter:"'standard'","picture-in-picture-mode":"[]",animation:b,bindStateChange:b,bindNetStatus:b,bindBgmStart:b,bindBgmProgress:b,bindBgmComplete:b,bindAudioVolumeNotify:b},OfficialAccount:{bindLoad:b,bindError:b},OpenData:{type:b,"open-gid":b,lang:"'en'","default-text":b,"default-avatar":b,bindError:b},NavigationBar:{title:b,loading:m,"front-color":"'#000000'","background-color":b,"color-animation-duration":y,"color-animation-timing-func":"'linear'"},PageMeta:{"background-text-style":b,"background-color":b,"background-color-top":b,"background-color-bottom":b,"root-background-color":b,"scroll-top":"''","scroll-duration":"300","page-style":"''","root-font-size":"''","page-orientation":"''",bindResize:b,bindScroll:b,bindScrollDone:b},VoipRoom:{openid:b,mode:"'camera'","device-position":"'front'",bindError:b},AdCustom:{"unit-id":b,"ad-intervals":b,bindLoad:b,bindError:b},PageContainer:{show:m,duration:"300","z-index":"100",overlay:g,position:"'bottom'",round:m,"close-on-slide-down":m,"overlay-style":b,"custom-style":b,bindBeforeEnter:b,bindEnter:b,bindAfterEnter:b,bindBeforeLeave:b,bindLeave:b,bindAfterLeave:b,bindClickOverlay:b},ShareElement:{mapkey:b,transform:m,duration:"300","easing-function":"'ease-out'","transition-on-gesture":m,"shuttle-on-push":"'to'","shuttle-on-pop":"'to'","rect-tween-type":"'materialRectArc'"},KeyboardAccessory:{},RootPortal:{enable:g},ChannelLive:{"feed-id":b,"finder-user-name":b},ChannelVideo:{"feed-id":b,"finder-user-name":b,"feed-token":b,autoplay:m,loop:m,muted:m,"object-fit":"'contain'",bindError:b},Snapshot:{mode:"'view'"},Span:{},OpenContainer:{transitionType:"'fade'",transitionDuration:"300",closedColor:"'white'",closedElevation:y,closeBorderRadius:y,middleColor:b,openColor:"'white'",openElevation:y,openBorderRadius:y},DraggableSheet:{initialChildSize:"0.5",minChildSize:"0.25",maxChildSize:"1.0",snap:m,snapSizes:"[]"},NestedScrollHeader:{},NestedScrollBody:{},DoubleTapGestureHandler:{},ForcePressGestureHandler:{},HorizontalDragGestureHandler:{},LongPressGestureHandler:{},PanGestureHandler:{},ScaleGestureHandler:{},TapGestureHandler:{},VerticalDragGestureHandler:{}},w={initNativeApi:p,getMiniLifecycle:function(t){var e=t.page[5];return-1===e.indexOf("onSaveExitState")&&e.push("onSaveExitState"),t},transferHydrateData:function(t,e,n){var i;if(e.isTransferElement){var o=getCurrentPages(),u=o[o.length-1];return t["nn"]=e.dataName,u.setData((0,r.A)({},(0,a.Cb)(t.nn),t)),(0,r.A)((0,r.A)({sid:e.sid},"v",""),"nn",(null===(i=n["#text"])||void 0===i?void 0:i._num)||"8")}}};(0,a.ZG)(w),(0,a.IQ)(A)},7260:function(t,e,n){"use strict";n.d(e,{Ay:function(){return Mt}});var r=n(3029),i=n(2901),o=n(4467),a=n(5544),u=n(2284),s=n(8885),c=n(7766),l=n(7225),d=n(6570),f=n(6021),h=n(8480),v=n(4733),p=n(6541),g=n(4845),m=n.n(g),b=n(772),y={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0},k=1,A=4,w=16,E=k,S=A,C=w;function T(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"dragend":case"dragstart":case"drop":case"input":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"reset":case"resize":case"submit":case"touchcancel":case"touchend":case"touchstart":case"change":case"blur":case"focus":case"select":case"selectstart":return E;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"pointerenter":case"pointerleave":return S;default:return C}}var x=Math.random().toString(36).slice(2),L="__reactProps$"+x,I="__reactFiber$"+x,P="__reactContainer$"+x,N=3,O=5,_=6,R=13;function M(t,e){e[I]=t}function B(t,e){e[P]=t}function D(t){var e=t[I]||t[P];return e&&(e.tag===O||e.tag===_||e.tag===R||e.tag===N)?e:null}function G(t){if(t.tag===O||t.tag===_)return t.stateNode}function F(t){return t[L]||null}function j(t,e){t[L]=e}function U(t,e,n){var r=t,i=n.checked;null==i?($(t,e,n),V(t,n)):console.warn("updateCheck \u672a\u5b9e\u73b0",r)}function V(t,e){var n=e.name;"radio"===e.type&&null!=n&&console.warn("radio updateNamedCousins \u672a\u5b9e\u73b0",t,e)}function H(t){var e="function"===typeof t||"symbol"===(0,u.A)(t);return e?"":t}function W(t){return""+t}function $(t,e,n){var r=t,i=H(n.value),o=n.type;K(r,e,i,o)}function K(t,e,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"string";null!=n?"number"===r?(0===n&&""===t.value||e!=n)&&(t.value=W(n)):e!==W(n)&&(t.value=W(n)):"submit"!==r&&"reset"!==r||t.removeAttribute("value")}function z(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();if("input"===e){var n=t.type;return!n||!!y[n]}return"textarea"===e}var Y=$,q=U;function J(t){var e=t.type,n=t.nodeName;return n&&"input"===n.toLowerCase()&&("checkbox"===e||"radio"===e)}function Z(t){return t._valueTracker}function Q(t){t._valueTracker=null}function X(t){var e=J(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,o=n.set;Object.defineProperty(t,e,{configurable:!0,enumerable:n.enumerable,get:function(){return i.call(this)},set:function(t){r=""+t,o.call(this,t)}});var a={getValue:function(){return r},setValue:function(t){r=""+t},stopTracking:function(){Q(t),delete t[e]}};return a}}function tt(t){Z(t)||(t._valueTracker=X(t))}function et(t,e){if(!t)return!1;var n=Z(t);if(!n)return!0;var r=n.getValue();return e!==r&&(n.setValue(e),!0)}var nt=/aspect|acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;function rt(t){return"o"===t[0]&&"n"===t[1]}function it(t,e){if(t===e)return!0;if("object"!==(0,u.A)(t)||null===t||"object"!==(0,u.A)(e)||null===e)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(var i=0;i<n.length;i++){var o=n[i];if(t[o]!==e[o])return!1}return!0}function ot(t,e,n){var r=ut(t,e,n);r&&at(t,e,r)}function at(t,e,n){for(var r=[],i=null,o=function(){var o=n[a],u=n[a+1],c=e[o];if("mini"===s.pD.HARMONY){if("__fixed"===o)return i=function(){return dt(t,o,u,c)},1;"__hmStyle"===o?r.splice(0,0,function(){return lt(t,u,c)}):r.push(function(){return dt(t,o,u,c)})}else dt(t,o,u,c)},a=0;a<n.length;a+=2)o();if("mini"===s.pD.HARMONY){i&&i();for(var u=0;u<r.length;u++)r[u]()}}function ut(t,e,n){var r,i=null;for(r in e)r in n||(i=i||[]).push(r,null);var o=t instanceof h.Z;for(r in n)if(e[r]!==n[r]||o&&"value"===r){if("style"===r&&(0,c.Gv)(e[r])&&(0,c.Gv)(n[r])&&it(e[r],n[r]))continue;(i=i||[]).push(r,n[r])}return i}function st(t,e,n,r){var i=e.endsWith("Capture"),o=e.toLowerCase().slice(2);i&&(o=o.slice(0,-7));var a=(0,l.ZH)((0,l.Cb)(t.tagName.toLowerCase()));"click"===o&&"mini"!==s.pD.HARMONY&&a in d.YN&&(o="tap"),(0,c.Tn)(n)?r?(t.removeEventListener(o,r,"mini"===s.pD.HARMONY&&void 0),t.addEventListener(o,n,"mini"!==s.pD.HARMONY?{isCapture:i,sideEffect:!1}:void 0)):t.addEventListener(o,n,"mini"!==s.pD.HARMONY?i:void 0):t.removeEventListener(o,r)}function ct(t,e,n){"-"!==e[0]||"mini"===s.pD.HARMONY?t[e]=(0,c.Et)(n)&&!1===nt.test(e)?"mini"===s.pD.HARMONY?n+"px":(0,v.h6)(n):null===n?"":n:t.setProperty(e,n.toString())}function lt(t,e,n){var r=t._st.hmStyle;if((0,c.Gv)(n))for(var i in n)e&&i in e||("mini"===s.pD.HARMONY?"::after"===i||"::before"===i?ft(t,i,null):["::first-child","::last-child","::empty"].includes(i)||0==="".concat(i).indexOf("::nth-child")?t.set_pseudo_class(i,null):("position"===i&&"fixed"===n[i]?t.setLayer(0):"animationName"===i&&t.setAnimation(!1),r[i]=""):r[i]="");if((0,c.Gv)(e))for(var o in e)n&&it(e[o],n[o])||("mini"===s.pD.HARMONY?"::after"===o||"::before"===o?ft(t,o,e[o]):["::first-child","::last-child","::empty"].includes(o)||o.startsWith("::nth-child")?t.set_pseudo_class(o,e[o]):("position"===o?("fixed"===e[o]||"fixed"!==e[o]&&(null===n||void 0===n?void 0:n[o]))&&t.setLayer("fixed"===e[o]?1:0):"animationName"===o&&t.setAnimation(!0),r[o]=e[o]):r[o]=e[o]);t.setAttribute("__hmStyle",e)}function dt(t,e,n,r){var i,o;if(e="className"===e?"class":e,"key"===e||"children"===e||"ref"===e);else if("style"===e){if(/harmony.*cpp/.test("weapp"))return t.setAttribute("_style4cpp",n);var a=t.style;if((0,c.Kg)(n))a.cssText=n;else{if((0,c.Kg)(r)&&(a.cssText="",r=null),(0,c.Gv)(r))for(var u in r)n&&u in n||("mini"===s.pD.HARMONY&&"position"===u&&"fixed"===r[u]&&t.setLayer(0),ct(a,u,""));if((0,c.Gv)(n))for(var l in n)r&&it(n[l],r[l])||("mini"===s.pD.HARMONY&&"position"===l&&("fixed"===n[l]||"fixed"!==n[l]&&(null===r||void 0===r?void 0:r[l]))&&t.setLayer("fixed"===n[l]?1:0),ct(a,l,n[l]))}}else if(rt(e))st(t,e,n,r);else if("dangerouslySetInnerHTML"===e){var d=null!==(i=null===n||void 0===n?void 0:n.__html)&&void 0!==i?i:"",f=null!==(o=null===r||void 0===r?void 0:r.__html)&&void 0!==o?o:"";(d||f)&&f!==d&&(t.innerHTML=d)}else(0,c.Tn)(n)||(null==n?t.removeAttribute(e):t.setAttribute(e,n))}function ft(t,e,n){"::after"===e?t.set_pseudo_after(n):"::before"===e&&t.set_pseudo_before(n)}var ht={getPublicInstance:function(t){return t},getRootHostContext:function(){return{}},getChildHostContext:function(t){return t},prepareForCommit:function(){return null},resetAfterCommit:l.lQ,createInstance:function(t,e,n,r,i){var o=p.F.createElement(t);return M(i,o),j(o,e),o},appendInitialChild:function(t,e){t.appendChild(e)},finalizeInitialChildren:function(t,e,n){var r=n;if(t instanceof h.Z){var i=["switch","checkbox","radio"].includes(e)?["checked","defaultChecked"]:["value","defaultValue"],u=(0,a.A)(i,2),s=u[0],c=u[1];n.hasOwnProperty(c)&&(r=Object.assign(Object.assign({},r),(0,o.A)({},s,n[c])),delete r[c])}return ot(t,{},r),"input"!==e&&"textarea"!==e||tt(t),!1},prepareUpdate:function(t,e,n,r){return ut(t,n,r)},shouldSetTextContent:function(){return!1},createTextInstance:function(t,e,n,r){var i=p.F.createTextNode(t);return M(r,i),i},scheduleTimeout:setTimeout,cancelTimeout:clearTimeout,noTimeout:-1,isPrimaryRenderer:!0,warnsIfNotActing:!0,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,getInstanceFromNode:function(){return null},beforeActiveInstanceBlur:l.lQ,afterActiveInstanceBlur:l.lQ,preparePortalMount:l.lQ,prepareScopeUpdate:l.lQ,getInstanceFromScope:function(){return null},getCurrentEventPriority:function(){return b.DefaultEventPriority},detachDeletedInstance:l.lQ,supportsMicrotasks:!0,scheduleMicrotask:(0,c.b0)(Promise)?setTimeout:function(t){return Promise.resolve(null).then(t).catch(function(t){setTimeout(function(){throw t})})},appendChild:function(t,e){t.appendChild(e)},appendChildToContainer:function(t,e){t.appendChild(e)},commitTextUpdate:function(t,e,n){t.nodeValue=n},commitMount:l.lQ,commitUpdate:function(t,e,n,r,i){e&&(2===e.length&&e.includes("children")||(at(t,r,e),j(t,i)))},insertBefore:function(t,e,n){t.insertBefore(e,n)},insertInContainerBefore:function(t,e,n){t.insertBefore(e,n)},removeChild:function(t,e){t.removeChild(e)},removeChildFromContainer:function(t,e){t.removeChild(e)},resetTextContent:l.lQ,hideInstance:function(t){var e=t.style;e.setProperty("display","none")},hideTextInstance:function(t){t.nodeValue=""},unhideInstance:function(t,e){var n=e.style,r=(null===n||void 0===n?void 0:n.hasOwnProperty("display"))?n.display:null;r=null==r||(0,c.Lm)(r)||""===r?"":(""+r).trim(),t.style["display"]=r},unhideTextInstance:function(t,e){t.nodeValue=e},clearContainer:function(t){t.childNodes.length>0&&(t.textContent="")}},vt=m()(ht),pt=null;function gt(t,e){var n,r,i=D(e),o=t.type;if(i&&z(e)&&("input"===o||"change"===o)){var a=W(null===(r=null===(n=t.mpEvent)||void 0===n?void 0:n.detail)||void 0===r?void 0:r.value);return mt(i,a)}}function mt(t,e){var n=G(t);return!!n&&(et(n,e)?t:void 0)}function bt(t){pt?pt.push(t):pt=[t]}function yt(){return null!==pt}function kt(){var t=yt();t&&(vt.flushSync(),At())}function At(){if(pt){var t=pt;pt=null;for(var e=0;e<t.length;e++)Et(t[e])}}function wt(t,e,n,r){switch(e){case"input":q(t,n,r);break;case"textarea":Y(t,n,r);break}}function Et(t){var e=D(t.target);if(e){var n=e.stateNode,r=e.type;if(n){var i=F(n);wt(n,r,t.value,i)}}}var St=new WeakMap,Ct=function(){function t(e,n,i){(0,r.A)(this,t),this.renderer=e,this.initInternalRoot(e,n,i)}return(0,i.A)(t,[{key:"initInternalRoot",value:function(t,e,n){var r=e;if(n){var i=1,o=!1,a=!1,u="",s=function(t){return console.error(t)},c=null;!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(u=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&(c=n.unstable_transitionCallbacks),this.internalRoot=t.createContainer(r,i,null,a,o,u,s,c)}else{var l=0;this.internalRoot=t.createContainer(r,l,null,!1,!1,"",function(){},null)}}},{key:"render",value:function(t,e){var n=this.renderer,r=this.internalRoot;return n.updateContainer(t,r,null,e),n.getPublicRootInstance(r)}},{key:"unmount",value:function(t){this.renderer.updateContainer(null,this.internalRoot,null,t)}}])}();function Tt(t,e,n){var r=St.get(e);if(null!=r)return r.render(t,n);var i=new Ct(vt,e);return St.set(e,i),i.render(t,n)}function xt(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=St.get(t);if(null!=r)return r;var i=new Ct(vt,t,n);return St.set(t,i),B(null===(e=null===i||void 0===i?void 0:i.internalRoot)||void 0===e?void 0:e.current,t),f.JL.tap("dispatchTaroEvent",function(t,e){var n=T(t.type);vt.runWithPriority(n,function(){e.dispatchEvent(t)})}),f.JL.tap("modifyTaroEvent",function(t,e){var n,r,i=gt(t,e);if(i){var o=null===(r=null===(n=t.mpEvent)||void 0===n?void 0:n.detail)||void 0===r?void 0:r.value;bt({target:e,value:o})}}),i}var Lt=!1,It=function(t,e){if(Lt)return t(e);Lt=!0;try{return vt.batchedUpdates(t,e)}finally{Lt=!1,kt()}};function Pt(t){(0,l.D8)(t&&[1,8,9,11].includes(t.nodeType),"unmountComponentAtNode(...): Target container is not a DOM element.");var e=St.get(t);return!!e&&(It(function(){e.unmount(function(){St.delete(t)})},null),!0)}function Nt(t){if(null==t)return null;var e=t.nodeType;return 1===e||3===e?t:vt.findHostInstance(t)}var Ot=(0,c.Tn)(Symbol)&&Symbol.for?Symbol.for("react.portal"):60106;function _t(t,e,n){return{$$typeof:Ot,key:null==n?null:String(n),children:t,containerInfo:e,implementation:null}}var Rt=vt.flushSync,Mt={render:Tt,flushSync:Rt,createRoot:xt,unstable_batchedUpdates:It,unmountComponentAtNode:Pt,findDOMNode:Nt,createPortal:_t,internalInstanceKey:I}},831:function(t,e,n){"use strict";n.d(e,{D:function(){return g},l:function(){return p}});var r,i,o,a,u,s,c=n(3029),l=n(2901),d=n(1635),f=n(7766),h=n(1389),v=function(){function t(e,n){(0,c.A)(this,t),r.set(this,""),i.set(this,""),o.set(this,""),a.set(this,""),u.set(this,""),s.set(this,void 0),(0,f.Kg)(e)||(e=String(e));var l=m(e,n),v=l.hash,p=l.hostname,g=l.pathname,b=l.port,y=l.protocol,k=l.search;(0,d.GG)(this,r,v,"f"),(0,d.GG)(this,i,p,"f"),(0,d.GG)(this,o,g||"/","f"),(0,d.GG)(this,a,b,"f"),(0,d.GG)(this,u,y,"f"),(0,d.GG)(this,s,new h.I(k),"f")}return(0,l.A)(t,[{key:"protocol",get:function(){return(0,d.gn)(this,u,"f")},set:function(t){(0,f.Kg)(t)&&(0,d.GG)(this,u,t.trim(),"f")}},{key:"host",get:function(){return this.hostname+(this.port?":"+this.port:"")},set:function(t){if(t&&(0,f.Kg)(t)){t=t.trim();var e=g("//".concat(t)),n=e.hostname,r=e.port;this.hostname=n,this.port=r}}},{key:"hostname",get:function(){return(0,d.gn)(this,i,"f")},set:function(t){t&&(0,f.Kg)(t)&&(0,d.GG)(this,i,t.trim(),"f")}},{key:"port",get:function(){return(0,d.gn)(this,a,"f")},set:function(t){(0,f.Kg)(t)&&(0,d.GG)(this,a,t.trim(),"f")}},{key:"pathname",get:function(){return(0,d.gn)(this,o,"f")},set:function(t){if((0,f.Kg)(t)){t=t.trim();var e=/^(\/|\.\/|\.\.\/)/,n=t;while(e.test(n))n=n.replace(e,"");n?(0,d.GG)(this,o,"/"+n,"f"):(0,d.GG)(this,o,"/","f")}}},{key:"search",get:function(){var t=(0,d.gn)(this,s,"f").toString();return 0===t.length||t.startsWith("?")?t:"?".concat(t)},set:function(t){(0,f.Kg)(t)&&(t=t.trim(),(0,d.GG)(this,s,new h.I(t),"f"))}},{key:"hash",get:function(){return(0,d.gn)(this,r,"f")},set:function(t){(0,f.Kg)(t)&&(t=t.trim(),t?(0,d.GG)(this,r,t.startsWith("#")?t:"#".concat(t),"f"):(0,d.GG)(this,r,"","f"))}},{key:"href",get:function(){return"".concat(this.protocol,"//").concat(this.host).concat(this.pathname).concat(this.search).concat(this.hash)},set:function(t){if(t&&(0,f.Kg)(t)){t=t.trim();var e=g(t),n=e.protocol,r=e.hostname,i=e.port,o=e.hash,a=e.search,u=e.pathname;this.protocol=n,this.hostname=r,this.pathname=u,this.port=i,this.hash=o,this.search=a}}},{key:"origin",get:function(){return"".concat(this.protocol,"//").concat(this.host)},set:function(t){if(t&&(0,f.Kg)(t)){t=t.trim();var e=g(t),n=e.protocol,r=e.hostname,i=e.port;this.protocol=n,this.hostname=r,this.port=i}}},{key:"searchParams",get:function(){return(0,d.gn)(this,s,"f")}},{key:"toString",value:function(){return this.href}},{key:"toJSON",value:function(){return this.toString()}},{key:"_toRaw",value:function(){return{protocol:this.protocol,port:this.port,host:this.host,hostname:this.hostname,pathname:this.pathname,hash:this.hash,search:this.search,origin:this.origin,href:this.href}}}],[{key:"createObjectURL",value:function(){throw new Error("Oops, not support URL.createObjectURL() in miniprogram.")}},{key:"revokeObjectURL",value:function(){throw new Error("Oops, not support URL.revokeObjectURL() in miniprogram.")}}])}();r=new WeakMap,i=new WeakMap,o=new WeakMap,a=new WeakMap,u=new WeakMap,s=new WeakMap;var p=v;function g(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e={href:"",origin:"",protocol:"",hostname:"",host:"",port:"",pathname:"",search:"",hash:""};if(!t||!(0,f.Kg)(t))return e;t=t.trim();var n=/^(([^:/?#]+):)?\/\/(([^/?#]+):(.+)@)?([^/?#:]*)(:(\d+))?([^?#]*)(\?([^#]*))?(#(.*))?/,r=t.match(n);return r?(e.protocol=r[1]||"https:",e.hostname=r[6]||"taro.com",e.port=r[8]||"",e.pathname=r[9]||"/",e.search=r[10]||"",e.hash=r[12]||"",e.href=t,e.origin=e.protocol+"//"+e.hostname,e.host=e.hostname+(e.port?":".concat(e.port):""),e):e}function m(t,e){var n=/^(https?:)\/\//i,r="",i=null;if(!(0,f.b0)(e)){if(e=String(e).trim(),!n.test(e))throw new TypeError("Failed to construct 'URL': Invalid base URL");i=g(e)}if(t=String(t).trim(),n.test(t))r=t;else{if(!i)throw new TypeError("Failed to construct 'URL': Invalid URL");r=t?t.startsWith("//")?i.protocol+t:i.origin+(t.startsWith("/")?t:"/".concat(t)):i.href}return g(r)}},1389:function(t,e,n){"use strict";n.d(e,{I:function(){return m}});var r,i,o=n(3029),a=n(2901),u=n(1635),s=n(7766),c=/[!'()~]|%20|%00/g,l=/\+/g,d={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};function f(t){return d[t]}function h(t,e,n){var r=(0,s.cy)(n)?n.join(","):n;e in t?t[e].push(r):t[e]=[r]}function v(t,e){h(this,e,t)}function p(t){return decodeURIComponent(t.replace(l," "))}function g(t){return encodeURIComponent(t).replace(c,f)}var m=(i=function(){function t(e){(0,o.A)(this,t),r.set(this,Object.create(null)),null!==e&&void 0!==e||(e="");var n=(0,u.gn)(this,r,"f");if("string"===typeof e){"?"===e.charAt(0)&&(e=e.slice(1));for(var i=e.split("&"),a=0,c=i.length;a<c;a++){var l=i[a],d=l.indexOf("=");try{d>-1?h(n,p(l.slice(0,d)),p(l.slice(d+1))):l.length&&h(n,p(l),"")}catch(t){0}}}else if((0,s.cy)(e))for(var f=0,g=e.length;f<g;f++){var m=e[f];h(n,m[0],m[1])}else if(e.forEach)e.forEach(v,n);else for(var b in e)h(n,b,e[b])}return(0,a.A)(t,[{key:"append",value:function(t,e){h((0,u.gn)(this,r,"f"),t,e)}},{key:"delete",value:function(t){delete(0,u.gn)(this,r,"f")[t]}},{key:"get",value:function(t){var e=(0,u.gn)(this,r,"f");return t in e?e[t][0]:null}},{key:"getAll",value:function(t){var e=(0,u.gn)(this,r,"f");return t in e?e[t].slice(0):[]}},{key:"has",value:function(t){return t in(0,u.gn)(this,r,"f")}},{key:"keys",value:function(){return Object.keys((0,u.gn)(this,r,"f"))}},{key:"set",value:function(t,e){(0,u.gn)(this,r,"f")[t]=[""+e]}},{key:"forEach",value:function(t,e){var n=(0,u.gn)(this,r,"f");Object.getOwnPropertyNames(n).forEach(function(r){n[r].forEach(function(n){t.call(e,n,r,this)},this)},this)}},{key:"toJSON",value:function(){return{}}},{key:"toString",value:function(){var t=(0,u.gn)(this,r,"f"),e=[];for(var n in t)for(var i=g(n),o=0,a=t[n];o<a.length;o++)e.push(i+"="+g(a[o]));return e.join("&")}}])}(),r=new WeakMap,i)},6541:function(t,e,n){"use strict";n.d(e,{F:function(){return S}});var r=n(2709),i=n(3029),o=n(2901),a=n(6919),u=n(5501),s=n(6570),c=n(7225),l=n(7766),d=n(6326),f=n(6696),h=n(9452),v=n(8480),p=n(9602),g=n(3173),m=n(9260),b=n(5311),y=n(831),k=function(t){function e(){return(0,i.A)(this,e),(0,a.A)(this,e,arguments)}return(0,u.A)(e,t),(0,o.A)(e,[{key:"href",get:function(){var t;return null!==(t=this.props["href"])&&void 0!==t?t:""},set:function(t){this.setAttribute("href",t)}},{key:"protocol",get:function(){var t;return null!==(t=this.props["protocol"])&&void 0!==t?t:""}},{key:"host",get:function(){var t;return null!==(t=this.props["host"])&&void 0!==t?t:""}},{key:"search",get:function(){var t;return null!==(t=this.props["search"])&&void 0!==t?t:""}},{key:"hash",get:function(){var t;return null!==(t=this.props["hash"])&&void 0!==t?t:""}},{key:"hostname",get:function(){var t;return null!==(t=this.props["hostname"])&&void 0!==t?t:""}},{key:"port",get:function(){var t;return null!==(t=this.props["port"])&&void 0!==t?t:""}},{key:"pathname",get:function(){var t;return null!==(t=this.props["pathname"])&&void 0!==t?t:""}},{key:"setAttribute",value:function(t,n){if("href"===t){var r=(0,y.D)(n);for(var i in r)(0,b.A)(e,"setAttribute",this,3)([i,r[i]])}else(0,b.A)(e,"setAttribute",this,3)([t,n])}}])}(d.$),A=function(t){function e(t){var n;return(0,i.A)(this,e),n=(0,a.A)(this,e),n.dataName=t,n.isTransferElement=!0,n}return(0,u.A)(e,t),(0,o.A)(e,[{key:"_path",get:function(){return this.dataName}}])}(d.$),w=function(t){function e(){var t;return(0,i.A)(this,e),t=(0,a.A)(this,e),t.createEvent=f.lh,t.nodeType=9,t.nodeName=r.$8,t}return(0,u.A)(e,t),(0,o.A)(e,[{key:"createElement",value:function(t){var e,n=t.toLowerCase();switch(!0){case n===r.r7:return e=new p.p,e;case s.Ig.has(n):e=new v.Z;break;case n===r.A:e=new k;break;case"page-meta"===n:case"navigation-bar"===n:e=new A((0,c.Cb)(n));break;default:e=new d.$;break}return e.nodeName=n,e.tagName=t.toUpperCase(),e}},{key:"createElementNS",value:function(t,e){return this.createElement(e)}},{key:"createTextNode",value:function(t){return new g.s(t)}},{key:"getElementById",value:function(t){var e=h.K.get(t);return(0,l.b0)(e)?null:e}},{key:"querySelector",value:function(t){return/^#/.test(t)?this.getElementById(t.slice(1)):null}},{key:"querySelectorAll",value:function(){return[]}},{key:"createComment",value:function(){var t=new g.s("");return t.nodeName=r.YK,t}},{key:"defaultView",get:function(){return m.A.window}}])}(d.$);function E(){var t=new w,e=t.createElement.bind(t),n=e(r.g3),i=e(r.Vx),o=e(r.$p),a=e(r.uq);a.id=r.uq;var u=e(r.MR);return t.appendChild(n),n.appendChild(i),n.appendChild(o),o.appendChild(u),u.appendChild(a),t.documentElement=n,t.head=i,t.body=o,t}var S=m.A.document=E()},7432:function(t,e,n){"use strict";n.d(e,{S:function(){return r}});var r=function(t){return t.style}},1554:function(t,e,n){"use strict";n.d(e,{B:function(){return k}});var r,i,o,a,u,s,c=n(3029),l=n(2901),d=n(6919),f=n(5501),h=n(1635),v=n(7766),p=n(7819),g=n(2709),m=n(2587),b=new m.b("history"),y=function(t){function e(t,n){var l;return(0,c.A)(this,e),l=(0,d.A)(this,e),r.add(l),i.set(l,void 0),o.set(l,[]),a.set(l,0),u.set(l,void 0),(0,h.GG)(l,u,n.window,"f"),(0,h.GG)(l,i,t,"f"),(0,h.gn)(l,i,"f").on("__record_history__",function(t){var e;(0,h.GG)(l,a,(e=(0,h.gn)(l,a,"f"),e++,e),"f"),(0,h.GG)(l,o,(0,h.gn)(l,o,"f").slice(0,(0,h.gn)(l,a,"f")),"f"),(0,h.gn)(l,o,"f").push({state:null,title:"",url:t})},null),(0,h.gn)(l,i,"f").on("__reset_history__",function(t){(0,h.gn)(l,r,"m",s).call(l,t)},null),l.on(g.oF.INIT,function(){(0,h.gn)(l,r,"m",s).call(l)},null),l.on(g.oF.RESTORE,function(t){b.set(t,{location:(0,h.gn)(l,i,"f"),stack:(0,h.gn)(l,o,"f").slice(),cur:(0,h.gn)(l,a,"f")})},null),l.on(g.oF.RECOVER,function(t){if(b.has(t)){var e=b.get(t);(0,h.GG)(l,i,e.location,"f"),(0,h.GG)(l,o,e.stack,"f"),(0,h.GG)(l,a,e.cur,"f")}},null),l.on(g.oF.DESTORY,function(t){b.delete(t)},null),(0,h.gn)(l,r,"m",s).call(l),l}return(0,f.A)(e,t),(0,l.A)(e,[{key:"length",get:function(){return(0,h.gn)(this,o,"f").length}},{key:"state",get:function(){return(0,h.gn)(this,o,"f")[(0,h.gn)(this,a,"f")].state}},{key:"go",value:function(t){if((0,v.Et)(t)&&!isNaN(t)){var e=(0,h.gn)(this,a,"f")+t;e=Math.min(Math.max(e,0),this.length-1),(0,h.GG)(this,a,e,"f"),(0,h.gn)(this,i,"f").trigger("__set_href_without_history__",(0,h.gn)(this,o,"f")[(0,h.gn)(this,a,"f")].url),(0,h.gn)(this,u,"f").trigger("popstate",(0,h.gn)(this,o,"f")[(0,h.gn)(this,a,"f")])}}},{key:"back",value:function(){this.go(-1)}},{key:"forward",value:function(){this.go(1)}},{key:"pushState",value:function(t,e,n){n&&(0,v.Kg)(n)&&((0,h.GG)(this,o,(0,h.gn)(this,o,"f").slice(0,(0,h.gn)(this,a,"f")+1),"f"),(0,h.gn)(this,o,"f").push({state:t,title:e,url:n}),(0,h.GG)(this,a,this.length-1,"f"),(0,h.gn)(this,i,"f").trigger("__set_href_without_history__",n))}},{key:"replaceState",value:function(t,e,n){n&&(0,v.Kg)(n)&&((0,h.gn)(this,o,"f")[(0,h.gn)(this,a,"f")]={state:t,title:e,url:n},(0,h.gn)(this,i,"f").trigger("__set_href_without_history__",n))}},{key:"cache",get:function(){return b}}])}(p.s);i=new WeakMap,o=new WeakMap,a=new WeakMap,u=new WeakMap,r=new WeakSet,s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";(0,h.GG)(this,o,[{state:null,title:"",url:t||(0,h.gn)(this,i,"f").href}],"f"),(0,h.GG)(this,a,0,"f")};var k=y},6079:function(t,e,n){"use strict";n.d(e,{a:function(){return x}});var r,i,o,a,u,s,c,l,d,f=n(3029),h=n(2901),v=n(6919),p=n(5501),g=n(1635),m=n(7766),b=n(7225),y=n(7819),k=n(2709),A=n(5518),w=n(2587),E=n(831),S="https://taro.com",C=new w.b("location"),T=function(t){function e(t){var n;return(0,f.A)(this,e),n=(0,v.A)(this,e),r.add(n),i.set(n,new E.l(S)),o.set(n,!1),a.set(n,void 0),(0,g.GG)(n,a,t.window,"f"),(0,g.gn)(n,r,"m",u).call(n),n.on("__set_href_without_history__",function(t){(0,g.GG)(n,o,!0,"f");var e=(0,g.gn)(n,i,"f").hash;(0,g.gn)(n,i,"f").href=L(t),e!==(0,g.gn)(n,i,"f").hash&&(0,g.gn)(n,a,"f").trigger("hashchange"),(0,g.GG)(n,o,!1,"f")},null),n.on(k.oF.INIT,function(){(0,g.gn)(n,r,"m",u).call(n)},null),n.on(k.oF.RESTORE,function(t){C.set(t,{lastHref:n.href})},null),n.on(k.oF.RECOVER,function(t){if(C.has(t)){var e=C.get(t);(0,g.GG)(n,o,!0,"f"),(0,g.gn)(n,i,"f").href=e.lastHref,(0,g.GG)(n,o,!1,"f")}},null),n.on(k.oF.DESTORY,function(t){C.delete(t)},null),n}return(0,p.A)(e,t),(0,h.A)(e,[{key:"protocol",get:function(){return(0,g.gn)(this,i,"f").protocol},set:function(t){var e=/^(http|https):$/i;if(t&&(0,m.Kg)(t)&&e.test(t.trim())){t=t.trim();var n=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").protocol=t,(0,g.gn)(this,r,"m",d).call(this,n)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"host",get:function(){return(0,g.gn)(this,i,"f").host},set:function(t){if(t&&(0,m.Kg)(t)){t=t.trim();var e=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").host=t,(0,g.gn)(this,r,"m",d).call(this,e)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"hostname",get:function(){return(0,g.gn)(this,i,"f").hostname},set:function(t){if(t&&(0,m.Kg)(t)){t=t.trim();var e=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").hostname=t,(0,g.gn)(this,r,"m",d).call(this,e)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"port",get:function(){return(0,g.gn)(this,i,"f").port},set:function(t){var e=Number(t=t.trim());if((0,m.Et)(e)&&!(e<=0)){var n=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").port=t,(0,g.gn)(this,r,"m",d).call(this,n)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"pathname",get:function(){return(0,g.gn)(this,i,"f").pathname},set:function(t){if(t&&(0,m.Kg)(t)){t=t.trim();var e=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").pathname=t,(0,g.gn)(this,r,"m",d).call(this,e)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"search",get:function(){return(0,g.gn)(this,i,"f").search},set:function(t){if(t&&(0,m.Kg)(t)){t=t.trim(),t=t.startsWith("?")?t:"?".concat(t);var e=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").search=t,(0,g.gn)(this,r,"m",d).call(this,e)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"hash",get:function(){return(0,g.gn)(this,i,"f").hash},set:function(t){if(t&&(0,m.Kg)(t)){t=t.trim(),t=t.startsWith("#")?t:"#".concat(t);var e=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").hash=t,(0,g.gn)(this,r,"m",d).call(this,e)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"href",get:function(){return(0,g.gn)(this,i,"f").href},set:function(t){var e=/^(http:|https:)?\/\/.+/;if(t&&(0,m.Kg)(t)&&e.test(t=t.trim())){var n=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").href=t,(0,g.gn)(this,r,"m",d).call(this,n)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"origin",get:function(){return(0,g.gn)(this,i,"f").origin},set:function(t){var e=/^(http:|https:)?\/\/.+/;if(t&&(0,m.Kg)(t)&&e.test(t=t.trim())){var n=(0,g.gn)(this,r,"m",s).call(this);(0,g.gn)(this,i,"f").origin=t,(0,g.gn)(this,r,"m",d).call(this,n)&&(0,g.gn)(this,r,"m",l).call(this)}}},{key:"assign",value:function(){(0,b.R8)(!0,"\u5c0f\u7a0b\u5e8f\u73af\u5883\u4e2d\u8c03\u7528location.assign()\u65e0\u6548.")}},{key:"reload",value:function(){(0,b.R8)(!0,"\u5c0f\u7a0b\u5e8f\u73af\u5883\u4e2d\u8c03\u7528location.reload()\u65e0\u6548.")}},{key:"replace",value:function(t){this.trigger("__set_href_without_history__",t)}},{key:"toString",value:function(){return this.href}},{key:"cache",get:function(){return C}}])}(y.s);i=new WeakMap,o=new WeakMap,a=new WeakMap,r=new WeakSet,u=function(){var t=(0,A.n)(),e=t.router;if(e){var n=e.path,r=e.params,o=Object.keys(r).map(function(t){return"".concat(t,"=").concat(r[t])}),a=o.length>0?"?"+o.join("&"):"",u="".concat(S).concat(n.startsWith("/")?n:"/"+n).concat(a);(0,g.GG)(this,i,new E.l(u),"f"),this.trigger("__reset_history__",this.href)}},s=function(){return(0,g.gn)(this,i,"f")._toRaw()},c=function(t){(0,g.gn)(this,i,"f").href=t},l=function(){this.trigger("__record_history__",this.href)},d=function(t){if((0,g.gn)(this,o,"f"))return!1;var e=(0,g.gn)(this,i,"f")._toRaw(),n=e.protocol,u=e.hostname,s=e.port,l=e.pathname,d=e.search,f=e.hash;return n!==t.protocol||u!==t.hostname||s!==t.port?((0,g.gn)(this,r,"m",c).call(this,t.href),!1):l!==t.pathname||(d!==t.search||(f!==t.hash?((0,g.gn)(this,a,"f").trigger("hashchange"),!0):((0,g.gn)(this,r,"m",c).call(this,t.href),!1)))};var x=T;function L(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=S;return/^[/?#]/.test(t)?e+t:t}},5943:function(t,e,n){"use strict";n.d(e,{C:function(){return u}});var r="Macintosh",i="Intel Mac OS X 10_14_5",o="AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36",a="("+r+"; "+i+") "+o,u={appCodeName:"Mozilla",appName:"Netscape",appVersion:"5.0 "+a,cookieEnabled:!0,mimeTypes:[],onLine:!0,platform:"MacIntel",plugins:[],product:"Taro",productSub:"20030107",userAgent:"Mozilla/5.0 "+a,vendor:"Joyent",vendorSub:""}},3363:function(t,e,n){"use strict";var r;n.d(e,{Ny:function(){return a},er:function(){return o},tB:function(){return r}}),function(){var t;"undefined"!==typeof performance&&null!==performance&&performance.now?r=function(){return performance.now()}:Date.now?(t=Date.now(),r=function(){return Date.now()-t}):(t=(new Date).getTime(),r=function(){return(new Date).getTime()-t})}();var i=0,o=function(t){var e=r(),n=Math.max(i+16,e);return setTimeout(function(){t(i=n)},n-e)},a=function(t){clearTimeout(t)}},7860:function(t,e,n){"use strict";n.d(e,{UW:function(){return k},fC:function(){return y},mw:function(){return b}});var r=n(436),i=n(3029),o=n(2901),a=n(6919),u=n(5501),s=n(7766),c=n(7819),l=n(2709),d=n(9260),f=n(7432),h=n(1554),v=n(6079),p=n(5943),g=n(3363),m=function(t){function e(){var t;(0,i.A)(this,e),t=(0,a.A)(this,e),t.navigator=p.C,t.requestAnimationFrame=g.er,t.cancelAnimationFrame=g.Ny,t.getComputedStyle=f.S;var o=[].concat((0,r.A)(Object.getOwnPropertyNames(n.g||{})),(0,r.A)(Object.getOwnPropertySymbols(n.g||{})));return o.forEach(function(e){if("atob"!==e&&"document"!==e&&!Object.prototype.hasOwnProperty.call(t,e))try{t[e]=n.g[e]}catch(t){0}}),t.Date||(t.Date=Date),t.location=new v.a({window:t}),t.history=new h.B(t.location,{window:t}),t.initEvent(),t}return(0,u.A)(e,t),(0,o.A)(e,[{key:"initEvent",value:function(){var t=this.location,e=this.history;this.on(l.oF.INIT,function(e){t.trigger(l.oF.INIT,e)},null),this.on(l.oF.RECOVER,function(n){t.trigger(l.oF.RECOVER,n),e.trigger(l.oF.RECOVER,n)},null),this.on(l.oF.RESTORE,function(n){t.trigger(l.oF.RESTORE,n),e.trigger(l.oF.RESTORE,n)},null),this.on(l.oF.DESTORY,function(n){t.trigger(l.oF.DESTORY,n),e.trigger(l.oF.DESTORY,n)},null)}},{key:"document",get:function(){return d.A.document}},{key:"addEventListener",value:function(t,e){(0,s.Kg)(t)&&this.on(t,e,null)}},{key:"removeEventListener",value:function(t,e){(0,s.Kg)(t)&&this.off(t,e,null)}},{key:"setTimeout",value:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return setTimeout.apply(void 0,arguments)})},{key:"clearTimeout",value:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}(function(){return clearTimeout.apply(void 0,arguments)})}])}(c.s),b=d.A.window=new m,y=b.location,k=b.history},2709:function(t,e,n){"use strict";n.d(e,{$8:function(){return p},$p:function(){return f},A:function(){return tt},DO:function(){return U},FY:function(){return g},GK:function(){return D},HT:function(){return Q},IB:function(){return z},ID:function(){return m},L_:function(){return A},M9:function(){return u},MR:function(){return v},Me:function(){return o},OC:function(){return G},PD:function(){return S},PL:function(){return N},QJ:function(){return W},Qg:function(){return I},Qn:function(){return _},Ru:function(){return C},Su:function(){return V},UX:function(){return X},V0:function(){return b},Vu:function(){return a},Vx:function(){return d},XT:function(){return q},Y:function(){return k},YK:function(){return $},Yb:function(){return i},ZE:function(){return M},_0:function(){return Y},bO:function(){return P},d_:function(){return Z},fx:function(){return E},g3:function(){return l},gR:function(){return w},lw:function(){return J},m$:function(){return R},n5:function(){return T},nG:function(){return B},oF:function(){return r},qb:function(){return O},qv:function(){return L},r7:function(){return c},tp:function(){return y},uq:function(){return h},vH:function(){return j},vx:function(){return F},x_:function(){return H},zO:function(){return x},zP:function(){return s},zV:function(){return K}});var r,i=2046,o="Taro runtime",a="taro-app",u="\u5c0f\u7a0b\u5e8f setData",s="\u9875\u9762\u521d\u59cb\u5316",c="root",l="html",d="head",f="body",h="app",v="container",p="#document",g="document-fragment",m="id",b="uid",y="class",k="style",A="focus",w="view",E="static-view",S="pure-view",C="click-view",T="props",x="dataset",L="object",I="value",P="input",N="change",O="custom-wrapper",_="target",R="currentTarget",M="type",B="confirm",D="timeStamp",G="keyCode",F="touchmove",j="Date",U="setTimeout",V="compileMode",H="catchMove",W="catch-view",$="comment",K="onLoad",z="onReady",Y="onShow",q="onHide",J="options",Z="externalClasses",Q="e_result",X="behaviors",tt="a";(function(t){t["INIT"]="0",t["RESTORE"]="1",t["RECOVER"]="2",t["DESTORY"]="3"})(r||(r={}))},5518:function(t,e,n){"use strict";n.d(e,{E:function(){return r},n:function(){return i}});var r={app:null,router:null,page:null},i=function(){return r}},1702:function(t,e,n){"use strict";n.d(e,{_:function(){return f}});var r=n(3029),i=n(2901),o=n(7225),a=[],u=function(t,e){return!!t&&t.sid===(null===e||void 0===e?void 0:e.sid)},s=function(t,e){var n=e.characterData,r=e.characterDataOldValue,i=e.attributes,o=e.attributeOldValue,a=e.childList;switch(t.type){case"characterData":return!!n&&(r||(t.oldValue=null),!0);case"attributes":return!!i&&(o||(t.oldValue=null),!0);case"childList":return!!a}},c=!1;function l(t,e){t.records.push(e),c||(c=!0,Promise.resolve().then(function(){c=!1,a.forEach(function(t){return t.callback(t.takeRecords())})}))}function d(t){a.forEach(function(e){for(var n=e.options,r=t.target;r;r=r.parentNode){if(u(e.target,r)&&s(t,n)){l(e,t);break}if(!n.subtree)break}})}var f=function(){function t(e){(0,r.A)(this,t),this.core={observe:o.lQ,disconnect:o.lQ,takeRecords:o.lQ}}return(0,i.A)(t,[{key:"observe",value:function(){var t;(t=this.core).observe.apply(t,arguments)}},{key:"disconnect",value:function(){this.core.disconnect()}},{key:"takeRecords",value:function(){return this.core.takeRecords()}}],[{key:"record",value:function(t){d(t)}}])}()},6326:function(t,e,n){"use strict";n.d(e,{$:function(){return w}});var r=n(3029),i=n(2901),o=n(6919),a=n(5311),u=n(9413),s=n(5501),c=n(7225),l=n(6021),d=n(7766),f=n(2709),h=n(1702),v=n(4733),p=function(){function t(e,n){var i=this;(0,r.A)(this,t),this.tokenList=[],this.el=n,e.trim().split(/\s+/).forEach(function(t){return i.tokenList.push(t)})}return(0,i.A)(t,[{key:"value",get:function(){return this.toString()}},{key:"length",get:function(){return this.tokenList.length}},{key:"add",value:function(){var t=0,e=!1,n=arguments,r=n.length,i=this.tokenList;do{var o=n[t];this.checkTokenIsValid(o)&&!~i.indexOf(o)&&(i.push(o),e=!0)}while(++t<r);e&&this._update()}},{key:"remove",value:function(){var t=0,e=!1,n=arguments,r=n.length,i=this.tokenList;do{var o=n[t]+"";if(this.checkTokenIsValid(o)){var a=i.indexOf(o);~i.indexOf(o)&&(i.splice(a,1),e=!0)}}while(++t<r);e&&this._update()}},{key:"contains",value:function(t){return!!this.checkTokenIsValid(t)&&!!~this.tokenList.indexOf(t)}},{key:"toggle",value:function(t,e){var n=this.contains(t),r=n?!0!==e&&"remove":!1!==e&&"add";return r&&this[r](t),!0===e||!1===e?e:!n}},{key:"replace",value:function(t,e){if(this.checkTokenIsValid(t)&&this.checkTokenIsValid(e)){var n=this.tokenList.indexOf(t);~n&&(this.tokenList.splice(n,1,e),this._update())}}},{key:"toString",value:function(){return this.tokenList.filter(function(t){return""!==t}).join(" ")}},{key:"checkTokenIsValid",value:function(t){return""!==t&&!/\s/.test(t)}},{key:"_update",value:function(){this.el.className=this.value}}])}(),g=n(9452),m=n(3897),b=n(3186);function y(){return!0}function k(t,e){var n=[],r=null!==e&&void 0!==e?e:y,i=t;while(i)1===i.nodeType&&r(i)&&n.push(i),i=A(i,t);return n}function A(t,e){var n=t.firstChild,r=1===t.nodeType||9===t.nodeType;if(n&&r)return n;var i=t;do{if(i===e)return null;var o=i.nextSibling;if(o)return o;i=i.parentElement}while(i);return null}var w=function(t){function e(){var t;return(0,r.A)(this,e),t=(0,o.A)(this,e),t.props={},t.dataset=c.MZ,t.nodeType=1,t.style=new b.O(t),l.JL.call("patchElement",t),t}return(0,s.A)(e,t),(0,i.A)(e,[{key:"_stopPropagation",value:function(t){var e=this;while(e=e.parentNode){var n=e.__handlers[t.type];if((0,d.cy)(n))for(var r=n.length;r--;){var i=n[r];i._stop=!0}}}},{key:"id",get:function(){return this.getAttribute(f.ID)},set:function(t){this.setAttribute(f.ID,t)}},{key:"className",get:function(){return this.getAttribute(f.tp)||""},set:function(t){this.setAttribute(f.tp,t)}},{key:"cssText",get:function(){return this.getAttribute(f.Y)||""}},{key:"classList",get:function(){return new p(this.className,this)}},{key:"children",get:function(){return this.childNodes.filter(v.vq)}},{key:"attributes",get:function(){var t=this.props,e=Object.keys(t),n=this.style.cssText,r=e.map(function(e){return{name:e,value:t[e]}});return r.concat(n?{name:f.Y,value:n}:[])}},{key:"textContent",get:function(){for(var t="",e=this.childNodes,n=0;n<e.length;n++)t+=e[n].textContent;return t},set:function(t){(0,u.A)(e,"textContent",t,this,1,1)}},{key:"hasAttribute",value:function(t){return!(0,d.b0)(this.props[t])}},{key:"hasAttributes",value:function(){return this.attributes.length>0}},{key:"focus",get:function(){return function(){this.setAttribute(f.L_,!0)}},set:function(t){this.setAttribute(f.L_,t)}},{key:"blur",value:function(){this.setAttribute(f.L_,!1)}},{key:"setAttribute",value:function(t,e){var n=this.nodeName===f.gR&&!(0,v.Vg)(this)&&!this.isAnyEventBinded();switch(t!==f.Y&&h._.record({target:this,type:"attributes",attributeName:t,oldValue:this.getAttribute(t)}),t){case f.Y:this.style.cssText=e;break;case f.ID:this.uid!==this.sid&&g.K.delete(this.uid),e=String(e),this.props[t]=this.uid=e,g.K.set(e,this);break;default:this.props[t]=e,t.startsWith("data-")&&(this.dataset===c.MZ&&(this.dataset=Object.create(null)),this.dataset[(0,c.Cb)(t.replace(/^data-/,""))]=e);break}if(this._root){var r=(0,v.dg)(),i=r[this.nodeName],o=r[f.gR]._num,a=r[f.Ru]._num,u=r[f.fx]._num,s=r[f.QJ]._num,p=this._path;t=(0,v.Oc)(t);var m=(0,c.Cb)(t),b={path:"".concat(p,".").concat(m),value:(0,d.Tn)(e)?function(){return e}:e};if(l.JL.call("modifySetAttrPayload",this,t,b,r),i){var y=i[m]||t;b.path="".concat(p,".").concat((0,c.Cb)(y))}this.enqueueUpdate(b),this.nodeName===f.gR&&(m===f.x_?this.enqueueUpdate({path:"".concat(p,".","nn"),value:e?s:this.isOnlyClickBinded()&&!(0,v.Vg)(this)?a:this.isAnyEventBinded()?o:u}):n&&(0,v.Vg)(this)&&this.enqueueUpdate({path:"".concat(p,".","nn"),value:u}))}}},{key:"removeAttribute",value:function(t){var e=this.nodeName===f.gR&&(0,v.Vg)(this)&&!this.isAnyEventBinded();if(h._.record({target:this,type:"attributes",attributeName:t,oldValue:this.getAttribute(t)}),t===f.Y)this.style.cssText="";else{var n=l.JL.call("onRemoveAttribute",this,t);if(n)return;if(!this.props.hasOwnProperty(t))return;delete this.props[t]}if(this._root){var r=(0,v.dg)(),i=r[this.nodeName],o=r[f.gR]._num,a=r[f.fx]._num,u=r[f.PD]._num,s=r[f.Ru]._num,d=this._path;t=(0,v.Oc)(t);var p=(0,c.Cb)(t),g={path:"".concat(d,".").concat(p),value:""};if(l.JL.call("modifyRmAttrPayload",this,t,g,r),i){var m=i[p]||t;g.path="".concat(d,".").concat((0,c.Cb)(m))}this.enqueueUpdate(g),this.nodeName===f.gR&&(p===f.x_?this.enqueueUpdate({path:"".concat(d,".","nn"),value:this.isOnlyClickBinded()&&!(0,v.Vg)(this)?s:this.isAnyEventBinded()?o:(0,v.Vg)(this)?a:u}):e&&!(0,v.Vg)(this)&&this.enqueueUpdate({path:"".concat(d,".","nn"),value:u}))}}},{key:"getAttribute",value:function(t){var e=t===f.Y?this.style.cssText:this.props[t];return null!==e&&void 0!==e?e:""}},{key:"getElementsByTagName",value:function(t){var e=this;return k(this,function(n){return n.nodeName===t||"*"===t&&e!==n})}},{key:"getElementsByClassName",value:function(t){var e=t.trim().split(/\s+/);return k(this,function(t){var n=t.classList;return e.every(function(t){return n.contains(t)})})}},{key:"dispatchEvent",value:function(t){var e=t.cancelable,n=this.__handlers[t.type];if(!(0,d.cy)(n))return!1;for(var r=n.length;r--;){var i=n[r],o=void 0;if(i._stop?i._stop=!1:(l.JL.call("modifyDispatchEvent",t,this),o=i.call(this,t)),(!1===o||t._end)&&e&&(t.defaultPrevented=!0),!(0,d.b0)(o)&&t.mpEvent){var a=l.JL.call("modifyTaroEventReturn",this,t,o);a&&(t.mpEvent[f.HT]=o)}if(t._end&&t._stop)break}return t._stop&&this._stopPropagation(t),null!=n}},{key:"addEventListener",value:function(t,n,r){var i=this.nodeName,o=l.JL.call("getSpecialNodes"),u=!0;if((0,d.Gv)(r)&&!1===r.sideEffect&&(u=!1,delete r.sideEffect),l.JL.call("modifyAddEventListener",this,u,v.dg),!1!==u&&!this.isAnyEventBinded()&&o.indexOf(i)>-1){var s=(0,v.dg)(),c=s[i]._num;this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:c})}(0,a.A)(e,"addEventListener",this,3)([t,n,r])}},{key:"removeEventListener",value:function(t,n){var r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];(0,a.A)(e,"removeEventListener",this,3)([t,n]);var i=this.nodeName,o=l.JL.call("getSpecialNodes");if(l.JL.call("modifyRemoveEventListener",this,r,v.dg),!1!==r&&!this.isAnyEventBinded()&&o.indexOf(i)>-1){var u=(0,v.dg)(),s=(0,v.Vg)(this)?"static-".concat(i):"pure-".concat(i),c=u[s]._num;this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:c})}}}],[{key:"extend",value:function(t,n){(0,v.X$)(e,t,n)}}])}(m.X)},9452:function(t,e,n){"use strict";n.d(e,{K:function(){return c}});var r=n(3029),i=n(2901),o=n(6919),a=n(5501),u=n(3437),s=function(t){function e(){return(0,r.A)(this,e),(0,o.A)(this,e,arguments)}return(0,a.A)(e,t),(0,i.A)(e,[{key:"removeNode",value:function(t){var e=t.sid,n=t.uid;this.delete(e),n!==e&&n&&this.delete(n)}},{key:"removeNodeTree",value:function(t){var e=this;this.removeNode(t);var n=t.childNodes;n.forEach(function(t){return e.removeNodeTree(t)})}}])}((0,u.A)(Map)),c=new s},6696:function(t,e,n){"use strict";n.d(e,{$r:function(){return p},Nd:function(){return d},lh:function(){return f}});var r=n(3029),i=n(2901),o=n(7225),a=n(7766),u=n(6021),s=n(2709),c=n(9260),l=n(4733),d=function(){function t(e,n,i){(0,r.A)(this,t),this._stop=!1,this._end=!1,this.defaultPrevented=!1,this.button=0,this.timeStamp=Date.now(),this.type=e.toLowerCase(),this.mpEvent=i,this.bubbles=Boolean(n&&n.bubbles),this.cancelable=Boolean(n&&n.cancelable)}return(0,i.A)(t,[{key:"stopPropagation",value:function(){this._stop=!0}},{key:"stopImmediatePropagation",value:function(){this._end=this._stop=!0}},{key:"preventDefault",value:function(){this.defaultPrevented=!0}},{key:"target",get:function(){var t,e,n,r,i,a=this.cacheTarget;if(a)return a;var u=Object.create((null===(t=this.mpEvent)||void 0===t?void 0:t.target)||null),s=c.A.document.getElementById((null===(e=u.dataset)||void 0===e?void 0:e.sid)||u.id||null),l=c.A.document.getElementById((null===(n=u.targetDataset)||void 0===n?void 0:n.sid)||(null===(r=u.dataset)||void 0===r?void 0:r.sid)||u.id||null);for(var d in u.dataset=Object.assign(Object.assign({},null!==s?s.dataset:o.MZ),null!==l?l.dataset:o.MZ),null===(i=this.mpEvent)||void 0===i?void 0:i.detail)u[d]=this.mpEvent.detail[d];return this.cacheTarget=u,u}},{key:"currentTarget",get:function(){var t,e,n,r,i,o,a,u,s=this.cacheCurrentTarget;if(s)return s;var l=c.A.document,d=Object.create((null===(t=this.mpEvent)||void 0===t?void 0:t.currentTarget)||null),f=l.getElementById((null===(e=d.dataset)||void 0===e?void 0:e.sid)||d.id||null),h=l.getElementById((null===(i=null===(r=null===(n=this.mpEvent)||void 0===n?void 0:n.target)||void 0===r?void 0:r.dataset)||void 0===i?void 0:i.sid)||(null===(a=null===(o=this.mpEvent)||void 0===o?void 0:o.target)||void 0===a?void 0:a.id)||null);if(null===f||f&&f===h)return this.cacheCurrentTarget=this.target,this.target;for(var v in d.dataset=f.dataset,null===(u=this.mpEvent)||void 0===u?void 0:u.detail)d[v]=this.mpEvent.detail[v];return this.cacheCurrentTarget=d,d}}])}();function f(t,e){if("string"===typeof t)return new d(t,{bubbles:!0,cancelable:!0});var n=new d(t.type,{bubbles:!0,cancelable:!0},t);for(var r in t)r!==s.m$&&r!==s.Qn&&r!==s.ZE&&r!==s.GK&&(n[r]=t[r]);return n.type===s.nG&&(null===e||void 0===e?void 0:e.nodeName)===s.bO&&(n[s.OC]=13),n}var h={};function v(t){var e=t[s.HT];return(0,a.b0)(e)||delete t[s.HT],e}function p(t){var e,n;void 0===t.type&&Object.defineProperty(t,"type",{value:t._type}),void 0===t.detail&&Object.defineProperty(t,"detail",{value:t._detail||Object.assign({},t)}),t.currentTarget=t.currentTarget||t.target||Object.assign({},t),u.JL.call("modifyMpEventImpl",t);var r=t.currentTarget,i=(null===(e=r.dataset)||void 0===e?void 0:e.sid)||r.id||(null===(n=t.detail)||void 0===n?void 0:n.id)||"",o=c.A.document.getElementById(i);if(o){var a=function(){var e=f(t,o);u.JL.call("modifyTaroEvent",e,o),u.JL.call("dispatchTaroEvent",e,o),u.JL.call("dispatchTaroEventFinish",e,o)};if(!u.JL.isExist("batchedEventUpdates"))return a(),v(t);var d=t.type;if(!u.JL.call("isBubbleEvents",d)||!(0,l.Rh)(o,d)||d===s.vx&&o.props.catchMove)return u.JL.call("batchedEventUpdates",function(){h[d]&&(h[d].forEach(function(t){return t()}),delete h[d]),a()}),v(t);(h[d]||(h[d]=[])).push(a)}}},8480:function(t,e,n){"use strict";n.d(e,{Z:function(){return l}});var r=n(3029),i=n(2901),o=n(6919),a=n(5311),u=n(5501),s=n(2709),c=n(6326),l=function(t){function e(){return(0,r.A)(this,e),(0,o.A)(this,e,arguments)}return(0,u.A)(e,t),(0,i.A)(e,[{key:"type",get:function(){var t;return null!==(t=this.props[s.ZE])&&void 0!==t?t:""},set:function(t){this.setAttribute(s.ZE,t)}},{key:"value",get:function(){var t=this.props[s.Qg];return null==t?"":t},set:function(t){this.setAttribute(s.Qg,t)}},{key:"dispatchEvent",value:function(t){if(t.mpEvent){var n=t.mpEvent.detail.value;t.type===s.PL?this.props.value=n:t.type===s.bO&&(this.value=n)}return(0,a.A)(e,"dispatchEvent",this,3)([t])}}])}(c.$)},3897:function(t,e,n){"use strict";n.d(e,{X:function(){return y}});var r=n(3029),i=n(2901),o=n(6919),a=n(5501),u=n(7225),s=n(6021),c=n(2709),l=n(1702),d=n(9260),f=n(8132),h=n(4733),v=n(9452),p=n(7766),g=function(){function t(){(0,r.A)(this,t),this.__handlers={}}return(0,i.A)(t,[{key:"addEventListener",value:function(t,e,n){if(t=t.toLowerCase(),s.JL.call("onAddEvent",t,e,n,this),"regionchange"===t)return this.addEventListener("begin",e,n),void this.addEventListener("end",e,n);Boolean(n);var r=!1;if((0,p.Gv)(n)&&(Boolean(n.capture),r=Boolean(n.once)),r){var i=function(){e.apply(this,arguments),this.removeEventListener(t,i)};this.addEventListener(t,i,Object.assign(Object.assign({},n),{once:!1}))}else{var o=e;e=function(){return o.apply(this,arguments)},e.oldHandler=o;var a=this.__handlers[t];(0,p.cy)(a)?a.push(e):this.__handlers[t]=[e]}}},{key:"removeEventListener",value:function(t,e){if(t=t.toLowerCase(),"regionchange"===t)return this.removeEventListener("begin",e),void this.removeEventListener("end",e);if(e){var n=this.__handlers[t];if((0,p.cy)(n)){var r=n.findIndex(function(t){if(t===e||t.oldHandler===e)return!0});n.splice(r,1)}}}},{key:"isAnyEventBinded",value:function(){var t=this.__handlers,e=Object.keys(t).find(function(e){return t[e].length});return Boolean(e)}},{key:"isOnlyClickBinded",value:function(){var t=this.__handlers,e=t.tap&&1===Object.keys(t).length;return Boolean(e)}}])}(),m="cn",b=(0,h.F$)(),y=function(t){function e(){var t;return(0,r.A)(this,e),t=(0,o.A)(this,e),t.parentNode=null,t.childNodes=[],t.hydrate=function(t){return function(){return(0,f.Q)(t)}},t.uid="_"+b(),t.sid=t.uid,v.K.set(t.sid,t),t}return(0,a.A)(e,t),(0,i.A)(e,[{key:"updateChildNodes",value:function(t){var e=this,n=function(){return[]},r=function(){var t=e.childNodes.filter(function(t){return!(0,h.Cn)(t)});return t.map(f.Q)};this.enqueueUpdate({path:"".concat(this._path,".").concat(m),value:t?n:r})}},{key:"updateSingleChild",value:function(t){var e=this;this.childNodes.forEach(function(n,r){(0,h.Cn)(n)||t&&r<t||e.enqueueUpdate({path:n._path,value:e.hydrate(n)})})}},{key:"_root",get:function(){var t;return(null===(t=this.parentNode)||void 0===t?void 0:t._root)||null}},{key:"findIndex",value:function(t){var e=this.childNodes.indexOf(t);return(0,u.D8)(-1!==e,"The node to be replaced is not a child of this node."),e}},{key:"_path",get:function(){var t=this.parentNode;if(t){var e=t.childNodes.filter(function(t){return!(0,h.Cn)(t)}),n=e.indexOf(this),r=s.JL.call("getPathIndex",n);return"".concat(t._path,".").concat(m,".").concat(r)}return""}},{key:"nextSibling",get:function(){var t=this.parentNode;return(null===t||void 0===t?void 0:t.childNodes[t.findIndex(this)+1])||null}},{key:"previousSibling",get:function(){var t=this.parentNode;return(null===t||void 0===t?void 0:t.childNodes[t.findIndex(this)-1])||null}},{key:"parentElement",get:function(){var t=this.parentNode;return 1===(null===t||void 0===t?void 0:t.nodeType)?t:null}},{key:"firstChild",get:function(){return this.childNodes[0]||null}},{key:"lastChild",get:function(){var t=this.childNodes;return t[t.length-1]||null}},{key:"textContent",set:function(t){var e=this.childNodes.slice(),n=[];while(this.firstChild)this.removeChild(this.firstChild,{doUpdate:!1});if(""===t)this.updateChildNodes(!0);else{var r=d.A.document.createTextNode(t);n.push(r),this.appendChild(r),this.updateChildNodes()}l._.record({type:"childList",target:this,removedNodes:e,addedNodes:n})}},{key:"insertBefore",value:function(t,e,n){var r=this;if(t.nodeName===c.FY)return t.childNodes.reduceRight(function(t,e){return r.insertBefore(e,t),e},e),t;t.remove({cleanRef:!1});var i=0;t.parentNode=this,e?(i=this.findIndex(e),this.childNodes.splice(i,0,t)):this.childNodes.push(t);var o=this.childNodes.length;if(this._root)if(e)if(n)this.enqueueUpdate({path:t._path,value:this.hydrate(t)});else{var a=2*o/3;a>i?this.updateChildNodes():this.updateSingleChild(i)}else{var u=1===o;u?this.updateChildNodes():this.enqueueUpdate({path:t._path,value:this.hydrate(t)})}return l._.record({type:"childList",target:this,addedNodes:[t],removedNodes:n?[e]:[],nextSibling:n?e.nextSibling:e||null,previousSibling:t.previousSibling}),t}},{key:"appendChild",value:function(t){return this.insertBefore(t)}},{key:"replaceChild",value:function(t,e){if(e.parentNode===this)return this.insertBefore(t,e,!0),e.remove({doUpdate:!1}),e}},{key:"removeChild",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.cleanRef,r=e.doUpdate;!1!==n&&!1!==r&&l._.record({type:"childList",target:this,removedNodes:[t],nextSibling:t.nextSibling,previousSibling:t.previousSibling});var i=this.findIndex(t);return this.childNodes.splice(i,1),t.parentNode=null,!1!==n&&v.K.removeNodeTree(t),this._root&&!1!==r&&this.updateChildNodes(),t}},{key:"remove",value:function(t){var e;null===(e=this.parentNode)||void 0===e||e.removeChild(this,t)}},{key:"hasChildNodes",value:function(){return this.childNodes.length>0}},{key:"enqueueUpdate",value:function(t){var e;null===(e=this._root)||void 0===e||e.enqueueUpdate(t)}},{key:"ownerDocument",get:function(){return d.A.document}}],[{key:"extend",value:function(t,n){(0,h.X$)(e,t,n)}}])}(g)},9602:function(t,e,n){"use strict";n.d(e,{p:function(){return p}});var r=n(4467),i=n(3029),o=n(2901),a=n(6919),u=n(5501),s=n(7766),c=n(6021),l=n(2709),d=n(7768),f=n(4733),h=n(6326);function v(t,e){var n,r=e.slice(1),i=t,o="";if(r.some(function(t,r){var a=t.replace(/^\[(.+)\]$/,"$1").replace(/\bcn\b/g,"childNodes");if(i=i[a],(0,s.cy)(i)&&(i=i.filter(function(t){return!(0,f.Cn)(t)})),(0,s.b0)(i))return!0;if(i.nodeName===l.qb){var u=f.DD.get(i.sid);u&&(n=u,o=e.slice(r+2).join("."))}}),n)return{customWrapper:n,splitedPath:o}}var p=function(t){function e(){var t;return(0,i.A)(this,e),t=(0,a.A)(this,e),t.updatePayloads=[],t.updateCallbacks=[],t.pendingUpdate=!1,t.ctx=null,t.nodeName=l.r7,t.tagName=l.r7.toUpperCase(),t}return(0,u.A)(e,t),(0,o.A)(e,[{key:"_path",get:function(){return l.r7}},{key:"_root",get:function(){return this}},{key:"scheduleTask",value:function(t){setTimeout(t)}},{key:"enqueueUpdate",value:function(t){this.updatePayloads.push(t),!this.pendingUpdate&&this.ctx&&this.performUpdate()}},{key:"performUpdate",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0;this.pendingUpdate=!0;var i=c.JL.call("proxyToRaw",this.ctx);this.scheduleTask(function(){var o="".concat(l.M9," \u5f00\u59cb\u65f6\u95f4\u6233 ").concat(Date.now());d.k.start(o);var a=Object.create(null),u=new Set(e?["root.cn.[0]","root.cn[0]"]:[]);while(t.updatePayloads.length>0){var c=t.updatePayloads.shift(),f=c.path,h=c.value;f.endsWith("cn")&&u.add(f),a[f]=h}var p=function(t){u.forEach(function(e){t.includes(e)&&t!==e&&delete a[t]});var e=a[t];(0,s.Tn)(e)&&(a[t]=e())};for(var g in a)p(g);if((0,s.Tn)(n))return n(a);t.pendingUpdate=!1;var m={},b=new Map;if(e)m=a;else for(var y in a){var k=y.split("."),A=v(t,k);if(A){var w=A.customWrapper,E=A.splitedPath;b.set(w,Object.assign(Object.assign({},b.get(w)||{}),(0,r.A)({},"i.".concat(E),a[y])))}else m[y]=a[y]}var S=b.size,C=Object.keys(m).length>0,T=S+(C?1:0),x=0,L=function(){++x===T&&(d.k.stop(o),t.flushUpdateCallback(),e&&d.k.stop(l.zP))};S&&b.forEach(function(t,e){e.setData(t,L)}),C&&i.setData(m,L)})}},{key:"enqueueUpdateCallback",value:function(t,e){this.updateCallbacks.push(function(){e?t.call(e):t()})}},{key:"flushUpdateCallback",value:function(){var t=this.updateCallbacks;if(t.length){var e=t.slice(0);this.updateCallbacks.length=0;for(var n=0;n<e.length;n++)e[n]()}}}])}(h.$)},3186:function(t,e,n){"use strict";n.d(e,{O:function(){return _}});var r=n(7695),i=n(3029),o=n(2901),a=n(7766),u=n(7225),s=n(6021),c=n(1702),l="webkit",d=["all","appearance","backdropFilter","blockOverflow","blockSize","bottom","clear","contain","content","continue","cursor","direction","display","filter","float","gap","height","inset","isolation","left","letterSpacing","lightingColor","markerSide","mixBlendMode","opacity","order","position","quotes","resize","right","rowGap","tabSize","tableLayout","top","userSelect","verticalAlign","visibility","voiceFamily","volume","whiteSpace","widows","width","zIndex","pointerEvents","aspectRatio"];function f(t,e,n){!n&&d.push(t),e.forEach(function(e){d.push(t+e),t===l&&d.push("Webkit"+e)})}var h="Color",v="Style",p="Width",g="Image",m="Size",b=[h,v,p],y=["FitLength","FitWidth",g],k=[].concat(y,["Radius"]),A=[].concat(b,y),w=["EndRadius","StartRadius"],E=["Bottom","Left","Right","Top"],S=["End","Start"],C=["Content","Items","Self"],T=["BlockSize","Height","InlineSize",p],x=["After","Before"];function L(t){c._.record({type:"attributes",target:t._element,attributeName:"style",oldValue:t.cssText})}function I(t){var e=t._element;e._root&&e.enqueueUpdate({path:"".concat(e._path,".","st"),value:t.cssText})}function P(t,e){var n=this[e];n!==t&&(!this._pending&&L(this),(0,a.kZ)(t)||(0,a.b0)(t)||""===t?(this._usedStyleProp.delete(e),delete this._value[e]):(this._usedStyleProp.add(e),this._value[e]=t),!this._pending&&I(this))}function N(t,e){for(var n,r={},i=function(){var n=e[o];if(t[n])return{v:void 0};r[n]={get:function(){var t=this._value[n];return(0,a.kZ)(t)||(0,a.b0)(t)?"":t},set:function(t){P.call(this,t,n)}}},o=0;o<e.length;o++)if(n=i(),n)return n.v;Object.defineProperties(t.prototype,r)}function O(t){return/^--/.test(t)}f("borderBlock",b),f("borderBlockEnd",b),f("borderBlockStart",b),f("outline",[].concat(b,["Offset"])),f("border",[].concat(b,["Boundary","Break","Collapse","Radius","Spacing"])),f("borderFit",["Length",p]),f("borderInline",b),f("borderInlineEnd",b),f("borderInlineStart",b),f("borderLeft",A),f("borderRight",A),f("borderTop",A),f("borderBottom",A),f("textDecoration",[h,v,"Line"]),f("textEmphasis",[h,v,"Position"]),f("scrollMargin",E),f("scrollPadding",E),f("padding",E),f("margin",[].concat(E,["Trim"])),f("scrollMarginBlock",S),f("scrollMarginInline",S),f("scrollPaddingBlock",S),f("scrollPaddingInline",S),f("gridColumn",S),f("gridRow",S),f("insetBlock",S),f("insetInline",S),f("marginBlock",S),f("marginInline",S),f("paddingBlock",S),f("paddingInline",S),f("pause",x),f("cue",x),f("mask",["Clip","Composite",g,"Mode","Origin","Position","Repeat",m,"Type"]),f("borderImage",["Outset","Repeat","Slice","Source","Transform",p]),f("maskBorder",["Mode","Outset","Repeat","Slice","Source",p]),f("font",["Family","FeatureSettings","Kerning","LanguageOverride","MaxSize","MinSize","OpticalSizing","Palette",m,"SizeAdjust","Stretch",v,"Weight","VariationSettings"]),f("transform",["Box","Origin",v]),f("background",[h,g,"Attachment","BlendMode","Clip","Origin","Position","Repeat",m]),f("listStyle",[g,"Position","Type"]),f("scrollSnap",["Align","Stop","Type"]),f("grid",["Area","AutoColumns","AutoFlow","AutoRows"]),f("gridTemplate",["Areas","Columns","Rows"]),f("overflow",["Block","Inline","Wrap","X","Y"]),f("transition",["Delay","Duration","Property","TimingFunction"]),f("color",["Adjust","InterpolationFilters","Scheme"]),f("textAlign",["All","Last"]),f("page",["BreakAfter","BreakBefore","BreakInside"]),f("animation",["Delay","Direction","Duration","FillMode","IterationCount","Name","PlayState","TimingFunction"]),f("flex",["Basis","Direction","Flow","Grow","Shrink","Wrap"]),f("offset",[].concat(x,S,["Anchor","Distance","Path","Position","Rotate"])),f("perspective",["Origin"]),f("clip",["Path","Rule"]),f("flow",["From","Into"]),f("align",["Content","Items","Self"],!0),f("alignment",["Adjust","Baseline"],!0),f("borderStart",w,!0),f("borderEnd",w,!0),f("borderCorner",["Fit",g,"ImageTransform"],!0),f("borderTopLeft",k,!0),f("borderTopRight",k,!0),f("borderBottomLeft",k,!0),f("borderBottomRight",k,!0),f("column",["s","Count","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","Span",p],!0),f("break",[].concat(x,["Inside"]),!0),f("wrap",[].concat(x,["Flow","Inside","Through"]),!0),f("justify",C,!0),f("place",C,!0),f("max",[].concat(T,["Lines"]),!0),f("min",T,!0),f("line",["Break","Clamp","Grid","Height","Padding","Snap"],!0),f("inline",["BoxAlign",m,"Sizing"],!0),f("text",["CombineUpright","GroupAlign","Height","Indent","Justify","Orientation","Overflow","Shadow","SpaceCollapse","SpaceTrim","Spacing","Transform","UnderlinePosition","Wrap"],!0),f("shape",["ImageThreshold","Inside","Margin","Outside"],!0),f("word",["Break","Spacing","Wrap"],!0),f("object",["Fit","Position"],!0),f("box",["DecorationBreak","Shadow","Sizing","Snap"],!0),f(l,["LineClamp","BoxOrient","TextFillColor","TextStroke","TextStrokeColor","TextStrokeWidth"],!0);var _=function(){function t(e){(0,i.A)(this,t),this._element=e,this._usedStyleProp=new Set,this._value={}}return(0,o.A)(t,[{key:"setCssVariables",value:function(t){var e=this;this.hasOwnProperty(t)||Object.defineProperty(this,t,{enumerable:!0,configurable:!0,get:function(){return e._value[t]||""},set:function(n){P.call(e,n,t)}})}},{key:"cssText",get:function(){var t=this;if(!this._usedStyleProp.size)return"";var e=[];return this._usedStyleProp.forEach(function(n){var r=t[n];if(!(0,a.kZ)(r)&&!(0,a.b0)(r)){var i=O(n)?n:(0,u.Lj)(n);0!==i.indexOf("webkit")&&0!==i.indexOf("Webkit")||(i="-".concat(i)),e.push("".concat(i,": ").concat(r,";"))}}),e.join(" ")},set:function(t){var e=this;if(this._pending=!0,L(this),this._usedStyleProp.forEach(function(t){e.removeProperty(t)}),""===t||(0,a.b0)(t)||(0,a.kZ)(t))return this._pending=!1,void I(this);for(var n=t.split(";"),i=0;i<n.length;i++){var o=n[i].trim();if(""!==o){var u=o.split(":"),s=(0,r.A)(u),c=s[0],l=s.slice(1),d=l.join(":");(0,a.b0)(d)||this.setProperty(c.trim(),d.trim())}}this._pending=!1,I(this)}},{key:"setProperty",value:function(t,e){"-"===t[0]?this.setCssVariables(t):t=(0,u.Cb)(t),(0,a.kZ)(e)||(0,a.b0)(e)?this.removeProperty(t):this[t]=e}},{key:"removeProperty",value:function(t){if(t=(0,u.Cb)(t),!this._usedStyleProp.has(t))return"";var e=this[t];return this[t]=void 0,e}},{key:"getPropertyValue",value:function(t){t=(0,u.Cb)(t);var e=this[t];return e||""}}])}();N(_,d),s.JL.tap("injectNewStyleProperties",function(t){if((0,a.cy)(t))N(_,t);else{if("string"!==typeof t)return;N(_,[t])}})},3173:function(t,e,n){"use strict";n.d(e,{s:function(){return c}});var r=n(3029),i=n(2901),o=n(6919),a=n(5501),u=n(1702),s=n(3897),c=function(t){function e(t){var n;return(0,r.A)(this,e),n=(0,o.A)(this,e),n.nodeType=3,n.nodeName="#text",n._value=t,n}return(0,a.A)(e,t),(0,i.A)(e,[{key:"textContent",get:function(){return this._value},set:function(t){u._.record({target:this,type:"characterData",oldValue:this._value}),this._value=t,this.enqueueUpdate({path:"".concat(this._path,".","v"),value:t})}},{key:"nodeValue",get:function(){return this._value},set:function(t){this.textContent=t}},{key:"data",get:function(){return this._value},set:function(t){this.textContent=t}}])}(s.X)},8870:function(t,e,n){"use strict";n.d(e,{As:function(){return T},Er:function(){return I},RW:function(){return S},V5:function(){return C},Yn:function(){return x},eU:function(){return N},pW:function(){return P},s$:function(){return _},sQ:function(){return L},sq:function(){return O},wT:function(){return w},zk:function(){return E}});var r=n(436),i=n(4467),o=n(5544),a=n(6021),u=n(7766),s=n(7225),c=n(6570),l=n(3363),d=n(7860),f=n(2709),h=n(5518),v=n(6696),p=n(3896),g=n(9260),m=n(7768),b=n(4733),y=n(9844),k=new Map,A=(0,b.F$)();function w(t,e){a.JL.call("mergePageInstance",k.get(e),t),k.set(e,t)}function E(t){return k.get(t)}function S(t){k.delete(t)}function C(t,e){for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];var o=k.get(t);if(null!=o){var s=a.JL.call("getLifecycle",o,e);if((0,u.cy)(s)){var c=s.map(function(t){return t.apply(o,r)});return c[0]}if((0,u.Tn)(s))return s.apply(o,r)}}function T(t){if(null==t)return"";var e=Object.keys(t).map(function(e){return e+"="+t[e]}).join("&");return""===e?e:"?"+e}function x(t,e){var n=t.indexOf("?");return"".concat(n>-1?t.substring(0,n):t).concat(T(e))}function L(t){return t+"."+f.IB}function I(t){return t+"."+f._0}function P(t){return t+"."+f.XT}function N(t,e,n,c){var b,w,E=null!==e&&void 0!==e?e:"taro_page_".concat(A()),S=(0,o.A)(a.JL.call("getMiniLifecycleImpl").page,7),T=S[0],N=S[1],O=S[2],_=S[3],R=S[4],M=S[5],B=S[6],D=null,G=!1,F=[];function j(t){var e=t.route||t.__route__||t.$taroPath;h.E.router={params:t.$taroParams,path:(0,y.tb)(e),$taroPath:t.$taroPath,onReady:L(E),onShow:I(E),onHide:P(E)},(0,u.b0)(t.exitState)||(h.E.router.exitState=t.exitState)}var U=(0,i.A)((0,i.A)((0,i.A)((0,i.A)((0,i.A)({},T,function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0;w=new Promise(function(t){b=t}),m.k.start(f.zP),h.E.page=this,this.config=c||{};var i=Object.assign({},n,{$taroTimestamp:Date.now()}),o=this.$taroPath=x(E,i);null==this.$taroParams&&(this.$taroParams=i),j(this),d.mw.trigger(f.oF.INIT,o);var a=function(){h.E.app.mount(t,o,function(){D=g.A.document.getElementById(o),(0,s.D8)(null!==D,"\u6ca1\u6709\u627e\u5230\u9875\u9762\u5b9e\u4f8b\u3002"),C(o,f.zV,e.$taroParams),b(),D.ctx=e,D.performUpdate(!0,r)})};G?F.push(a):a()}),N,function(){var t=this.$taroPath;d.mw.trigger(f.oF.DESTORY,t),C(t,N),G=!0,h.E.app.unmount(t,function(){G=!1,k.delete(t),D&&(D.ctx=null,D=null),F.length&&(F.forEach(function(t){return t()}),F=[])})}),O,function(){var t=this;w.then(function(){C(t.$taroPath,f.IB),(0,l.er)(function(){return p.k.trigger(L(E))}),t.onReady.called=!0})}),_,function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};w.then(function(){h.E.page=t,j(t),d.mw.trigger(f.oF.RECOVER,t.$taroPath),C(t.$taroPath,f._0,e),(0,l.er)(function(){return p.k.trigger(I(E))})})}),R,function(){d.mw.trigger(f.oF.RESTORE,this.$taroPath),h.E.page===this&&(h.E.page=null,h.E.router=null),C(this.$taroPath,f.XT),p.k.trigger(P(E))});return M.forEach(function(t){var e=!1;t=t.replace(/^defer:/,function(){return e=!0,""}),U[t]=function(){var n=arguments,i=this,o=function(){return C.apply(void 0,[i.$taroPath,t].concat((0,r.A)(n)))};if(!e)return o();w.then(o)}}),B.forEach(function(e){var n;(t[e]||(null===(n=t.prototype)||void 0===n?void 0:n[e])||t[e.replace(/^on/,"enable")]||(null===c||void 0===c?void 0:c[e.replace(/^on/,"enable")]))&&(U[e]=function(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];var o=null===(t=r[0])||void 0===t?void 0:t.target;if(null===o||void 0===o?void 0:o.id){var a=o.id,u=g.A.document.getElementById(a);u&&(o.dataset=u.dataset)}return C.apply(void 0,[this.$taroPath,e].concat(r))})}),U.eh=v.$r,(0,u.b0)(n)||(U.data=n),a.JL.call("modifyPageObject",U),U}function O(t,e,n){var r=null!==e&&void 0!==e?e:"taro_component_".concat(A()),c=null,l=(0,o.A)(a.JL.call("getMiniLifecycleImpl").component,2),d=l[0],p=l[1],b=(0,i.A)((0,i.A)((0,i.A)({},d,function(){var e,n=this;m.k.start(f.zP),this.pageIdCache=(null===(e=this.getPageId)||void 0===e?void 0:e.call(this))||A();var i=x(r,{id:this.pageIdCache});h.E.app.mount(t,i,function(){c=g.A.document.getElementById(i),(0,s.D8)(null!==c,"\u6ca1\u6709\u627e\u5230\u7ec4\u4ef6\u5b9e\u4f8b\u3002"),n.$taroInstances=k.get(i),C(i,f.zV),c.ctx=n,c.performUpdate(!0)})}),p,function(){var t=x(r,{id:this.pageIdCache});h.E.app.unmount(t,function(){k.delete(t),c&&(c.ctx=null)})}),"methods",{eh:v.$r});return(0,u.b0)(n)||(b.data=n),[f.lw,f.d_,f.UX].forEach(function(e){var n;b[e]=null!==(n=t[e])&&void 0!==n?n:s.MZ}),b}function _(t){var e=t===f.qb,n=(0,o.A)(a.JL.call("getMiniLifecycleImpl").component,2),r=n[0],l=n[1],d=e?(0,i.A)((0,i.A)({},r,function(){var t,e,n=(null===(t=this.data.i)||void 0===t?void 0:t.sid)||(null===(e=this.props.i)||void 0===e?void 0:e.sid);if((0,u.Kg)(n)){b.DD.set(n,this);var r=g.A.document.getElementById(n);r&&(r.ctx=this)}}),l,function(){var t,e,n=(null===(t=this.data.i)||void 0===t?void 0:t.sid)||(null===(e=this.props.i)||void 0===e?void 0:e.sid);if((0,u.Kg)(n)){b.DD.delete(n);var r=g.A.document.getElementById(n);r&&(r.ctx=null)}}):s.MZ,h={};return a.JL.call("modifyRecursiveComponentConfig",Object.assign({properties:{i:{type:Object,value:(0,i.A)({},"nn",(0,s.dg)(c.YN)[f.gR]._num)},l:{type:String,value:""}},options:Object.assign(Object.assign({},h),{virtualHost:!e}),methods:{eh:v.$r}},d),{isCustomWrapper:e})}},3896:function(t,e,n){"use strict";n.d(e,{k:function(){return o}});var r=n(6021),i=n(7819),o=r.JL.call("getEventCenter",i.s)},9260:function(t,e,n){"use strict";n.d(e,{A:function(){return i}});var r=n(7225),i={window:r.MZ,document:r.MZ}},8132:function(t,e,n){"use strict";n.d(e,{Q:function(){return l}});var r,i,o=n(4467),a=n(6021),u=n(7225),s=n(2709),c=n(4733);function l(t){var e;i||(i=(0,c.dg)()),r||(r=a.JL.call("getSpecialNodes"));var n=t.nodeName,d=null;if((0,c.KH)(t))return(0,o.A)((0,o.A)({sid:t.sid},"v",t.nodeValue),"nn",(null===(e=i[n])||void 0===e?void 0:e._num)||"8");var f=(0,o.A)((0,o.A)({},"nn",n),"sid",t.sid);t.uid!==t.sid&&(f.uid=t.uid),r.indexOf(n)>-1&&(t.isAnyEventBinded()||(f["nn"]="static-".concat(n),n!==s.gR||(0,c.Vg)(t)||(f["nn"]=s.PD)),n===s.gR&&t.isOnlyClickBinded()&&!(0,c.Vg)(t)&&(f["nn"]=s.Ru));var h=t.props;for(var v in h){var p=(0,u.Cb)(v);v.startsWith("data-")||v===s.tp||v===s.Y||v===s.ID||p===s.x_||p===s.Su||(f[p]=h[v]),n===s.gR&&p===s.x_&&!1!==h[v]&&(f["nn"]=s.QJ),p===s.Su&&(d=h[v])}f["cn"]=t.childNodes.filter(function(t){return!(0,c.Cn)(t)}).map(l),""!==t.className&&(f["cl"]=t.className);var g=t.cssText;""!==g&&"swiper-item"!==n&&(f["st"]=g),a.JL.call("modifyHydrateData",f,t);var m=f["nn"],b=i[m];if(b)for(var y in f["nn"]=b._num,f)y in b&&(f[b[y]]=f[y],delete f[y]);null!==d&&(f["nn"]=d);var k=a.JL.call("transferHydrateData",f,t,b);return k||f}},892:function(t,e,n){"use strict";n.r(e),n.d(e,{A:function(){return kt.A},APP:function(){return kt.uq},BEHAVIORS:function(){return kt.UX},BODY:function(){return kt.$p},CATCHMOVE:function(){return kt.x_},CATCH_VIEW:function(){return kt.QJ},CHANGE:function(){return kt.PL},CLASS:function(){return kt.tp},CLICK_VIEW:function(){return kt.Ru},COMMENT:function(){return kt.YK},COMPILE_MODE:function(){return kt.Su},CONFIRM:function(){return kt.nG},CONTAINER:function(){return kt.MR},CONTEXT_ACTIONS:function(){return kt.oF},CURRENT_TARGET:function(){return kt.m$},CUSTOM_WRAPPER:function(){return kt.qb},Current:function(){return At.E},DATASET:function(){return kt.zO},DATE:function(){return kt.vH},DOCUMENT_ELEMENT_NAME:function(){return kt.$8},DOCUMENT_FRAGMENT:function(){return kt.FY},EVENT_CALLBACK_RESULT:function(){return kt.HT},EXTERNAL_CLASSES:function(){return kt.d_},Events:function(){return X.s},FOCUS:function(){return kt.L_},FormElement:function(){return ft.Z},HEAD:function(){return kt.Vx},HOOKS_APP_ID:function(){return kt.Vu},HTML:function(){return kt.g3},History:function(){return rt.B},ID:function(){return kt.ID},INPUT:function(){return kt.bO},KEY_CODE:function(){return kt.OC},Location:function(){return it.a},MutationObserver:function(){return yt._},OBJECT:function(){return kt.qv},ON_HIDE:function(){return kt.XT},ON_LOAD:function(){return kt.zV},ON_READY:function(){return kt.IB},ON_SHOW:function(){return kt._0},OPTIONS:function(){return kt.lw},PAGE_INIT:function(){return kt.zP},PROPERTY_THRESHOLD:function(){return kt.Yb},PROPS:function(){return kt.n5},PURE_VIEW:function(){return kt.PD},ROOT_STR:function(){return kt.r7},SET_DATA:function(){return kt.M9},SET_TIMEOUT:function(){return kt.DO},STATIC_VIEW:function(){return kt.fx},STYLE:function(){return kt.Y},SVGElement:function(){return mt},Style:function(){return vt.O},TARGET:function(){return kt.Qn},TARO_RUNTIME:function(){return kt.Me},TIME_STAMP:function(){return kt.GK},TOUCHMOVE:function(){return kt.vx},TYPE:function(){return kt.ZE},TaroElement:function(){return lt.$},TaroEvent:function(){return dt.Nd},TaroNode:function(){return i.X},TaroRootElement:function(){return ht.p},TaroText:function(){return bt.s},UID:function(){return kt.V0},URL:function(){return ut.l},URLSearchParams:function(){return st.I},VALUE:function(){return kt.Qg},VIEW:function(){return kt.gR},addLeadingSlash:function(){return Nt.tb},cancelAnimationFrame:function(){return at.Ny},convertNumber2PX:function(){return Lt.h6},createComponentConfig:function(){return Et.sq},createEvent:function(){return dt.lh},createPageConfig:function(){return Et.eU},createRecursiveComponentConfig:function(){return Et.s$},customWrapperCache:function(){return Lt.DD},debounce:function(){return Pt.s},document:function(){return et.F},env:function(){return Q.A},eventCenter:function(){return St.k},eventHandler:function(){return dt.$r},eventSource:function(){return wt.K},extend:function(){return Lt.X$},getComponentsAlias:function(){return Lt.dg},getComputedStyle:function(){return nt.S},getCurrentInstance:function(){return At.n},getCurrentPage:function(){return Nt.p$},getHomePage:function(){return Nt.rE},getOnHideEventKey:function(){return Et.pW},getOnReadyEventKey:function(){return Et.sQ},getOnShowEventKey:function(){return Et.Er},getPageInstance:function(){return Et.zk},getPath:function(){return Et.Yn},handlePolyfill:function(){return It},hasBasename:function(){return Nt.lA},history:function(){return ct.UW},hooks:function(){return tt.JL},hydrate:function(){return Ct.Q},incrementId:function(){return Lt.F$},injectPageInstance:function(){return Et.wT},isComment:function(){return Lt.Cn},isElement:function(){return Lt.vq},isHasExtractProp:function(){return Lt.Vg},isParentBinded:function(){return Lt.Rh},isText:function(){return Lt.KH},location:function(){return ct.fC},navigator:function(){return ot.C},nextTick:function(){return Tt.d},now:function(){return at.tB},options:function(){return o.f},parseUrl:function(){return ut.D},perf:function(){return xt.k},removePageInstance:function(){return Et.RW},requestAnimationFrame:function(){return at.er},safeExecute:function(){return Et.V5},shortcutAttr:function(){return Lt.Oc},stringify:function(){return Et.As},stripBasename:function(){return Nt.pb},stripSuffix:function(){return Nt.yK},stripTrailing:function(){return Nt.Hl},throttle:function(){return Pt.n},window:function(){return ct.mw}});var r=n(8885),i=n(3897),o=n(9489),a=n(5544),u=n(7766),s=n(3029),c=n(2901);function l(){return{index:0,column:0,line:0}}function d(t,e,n){for(var r=t.index,i=t.index=r+n,o=r;o<i;o++){var a=e.charAt(o);"\n"===a?(t.line++,t.column=0):t.column++}}function f(t,e,n){var r=n-t.index;return d(t,e,r)}function h(t){return{index:t.index,line:t.line,column:t.column}}var v=/\s/;function p(t){return v.test(t)}var g=/=/;function m(t){return g.test(t)}function b(t){var e=t.toLowerCase();return!!o.f.html.skipElements.has(e)}var y=/[A-Za-z0-9]/;function k(t,e){while(1){var n=t.indexOf("<",e);if(-1===n)return n;var r=t.charAt(n+1);if("/"===r||"!"===r||y.test(r))return n;e=n+1}}function A(t,e,n){if(!p(n.charAt(t)))return!1;for(var r=n.length,i=t-1;i>e;i--){var o=n.charAt(i);if(!p(o)){if(m(o))return!1;break}}for(var a=t+1;a<r;a++){var u=n.charAt(a);if(!p(u))return!m(u)}}var w=function(){function t(e){(0,s.A)(this,t),this.tokens=[],this.position=l(),this.html=e}return(0,c.A)(t,[{key:"scan",value:function(){var t=this.html,e=this.position,n=t.length;while(e.index<n){var r=e.index;if(this.scanText(),e.index===r){var i=t.startsWith("!--",r+1);if(i)this.scanComment();else{var o=this.scanTag();b(o)&&this.scanSkipTag(o)}}}return this.tokens}},{key:"scanText",value:function(){var t="text",e=this.html,n=this.position,r=k(e,n.index);if(r!==n.index){-1===r&&(r=e.length);var i=h(n),o=e.slice(n.index,r);f(n,e,r);var a=h(n);this.tokens.push({type:t,content:o,position:{start:i,end:a}})}}},{key:"scanComment",value:function(){var t="comment",e=this.html,n=this.position,r=h(n);d(n,e,4);var i=e.indexOf("--\x3e",n.index),o=i+3;-1===i&&(i=o=e.length);var a=e.slice(n.index,i);f(n,e,o),this.tokens.push({type:t,content:a,position:{start:r,end:h(n)}})}},{key:"scanTag",value:function(){this.scanTagStart();var t=this.scanTagName();return this.scanAttrs(),this.scanTagEnd(),t}},{key:"scanTagStart",value:function(){var t="tag-start",e=this.html,n=this.position,r=e.charAt(n.index+1),i="/"===r,o=h(n);d(n,e,i?2:1),this.tokens.push({type:t,close:i,position:{start:o}})}},{key:"scanTagEnd",value:function(){var t="tag-end",e=this.html,n=this.position,r=e.charAt(n.index),i="/"===r;d(n,e,i?2:1);var o=h(n);this.tokens.push({type:t,close:i,position:{end:o}})}},{key:"scanTagName",value:function(){var t="tag",e=this.html,n=this.position,r=e.length,i=n.index;while(i<r){var o=e.charAt(i),a=!(p(o)||"/"===o||">"===o);if(a)break;i++}var u=i+1;while(u<r){var s=e.charAt(u),c=!(p(s)||"/"===s||">"===s);if(!c)break;u++}f(n,e,u);var l=e.slice(i,u);return this.tokens.push({type:t,content:l}),l}},{key:"scanAttrs",value:function(){var t=this.html,e=this.position,n=this.tokens,r=e.index,i=null,o=r,a=[],u=t.length;while(r<u){var s=t.charAt(r);if(i){var c=s===i;c&&(i=null),r++}else{var l="/"===s||">"===s;if(l){r!==o&&a.push(t.slice(o,r));break}if(A(r,o,t))r!==o&&a.push(t.slice(o,r)),o=r+1,r++;else{var d="'"===s||'"'===s;d?(i=s,r++):r++}}}f(e,t,r);for(var h=a.length,v="attribute",p=0;p<h;p++){var g=a[p],m=g.includes("=");if(m){var b=a[p+1];if(b&&b.startsWith("=")){if(b.length>1){var y=g+b;n.push({type:v,content:y}),p+=1;continue}var k=a[p+2];if(p+=1,k){var w=g+"="+k;n.push({type:v,content:w}),p+=1;continue}}}if(g.endsWith("=")){var E=a[p+1];if(E&&!E.includes("=")){var S=g+E;n.push({type:v,content:S}),p+=1;continue}var C=g.slice(0,-1);n.push({type:v,content:C})}else n.push({type:v,content:g})}}},{key:"scanSkipTag",value:function(t){var e=this.html,n=this.position,r=t.toLowerCase(),i=e.length;while(n.index<i){var o=e.indexOf("</",n.index);if(-1===o){this.scanText();break}f(n,e,o);var a=this.scanTag();if(r===a.toLowerCase())break}}}])}();function E(t){var e=t.charAt(0),n=t.length-1,r='"'===e||"'"===e;return r&&e===t.charAt(n)?t.slice(1,n):t}var S="{",C="}",T=".",x="#",L=">",I="~",P="+",N=function(){function t(){(0,s.A)(this,t),this.styles=[]}return(0,c.A)(t,[{key:"extractStyle",value:function(t){var e=this,n=/<style\s?[^>]*>((.|\n|\s)+?)<\/style>/g,r=t;return r=r.replace(n,function(t,n){var r=n.trim();return e.stringToSelector(r),""}),r.trim()}},{key:"stringToSelector",value:function(t){var e=this,n=t.indexOf(S),r=function(){var r=t.indexOf(C),i=t.slice(0,n).trim(),o=t.slice(n+1,r);o=o.replace(/:(.*);/g,function(t,e){var n=e.trim().replace(/ +/g,"+++");return":".concat(n,";")}),o=o.replace(/ /g,""),o=o.replace(/\+\+\+/g," "),/;$/.test(o)||(o+=";"),i.split(",").forEach(function(t){var n=e.parseSelector(t);e.styles.push({content:o,selectorList:n})}),t=t.slice(r+1),n=t.indexOf(S)};while(n>-1)r()}},{key:"parseSelector",value:function(t){var e=t.trim().replace(/ *([>~+]) */g," $1").replace(/ +/g," ").replace(/\[\s*([^[\]=\s]+)\s*=\s*([^[\]=\s]+)\s*\]/g,"[$1=$2]").split(" "),n=e.map(function(t){var e=t.charAt(0),n={isChild:e===L,isGeneralSibling:e===I,isAdjacentSibling:e===P,tag:null,id:null,class:[],attrs:[]};return t=t.replace(/^[>~+]/,""),t=t.replace(/\[(.+?)\]/g,function(t,e){var r=e.split("="),i=(0,a.A)(r,2),o=i[0],u=i[1],s=-1===e.indexOf("="),c={all:s,key:o,value:s?null:u};return n.attrs.push(c),""}),t=t.replace(/([.#][A-Za-z0-9-_]+)/g,function(t,e){return e[0]===x?n.id=e.substr(1):e[0]===T&&n.class.push(e.substr(1)),""}),""!==t&&(n.tag=t),n});return n}},{key:"matchStyle",value:function(t,e,n){var r=this,i=_(this.styles).reduce(function(i,o,a){var u=o.content,s=o.selectorList,c=n[a],l=s[c],d=s[c+1];((null===d||void 0===d?void 0:d.isGeneralSibling)||(null===d||void 0===d?void 0:d.isAdjacentSibling))&&(l=d,c+=1,n[a]+=1);var f=r.matchCurrent(t,e,l);if(f&&l.isGeneralSibling){var h=O(e);while(h){if(h.h5tagName&&r.matchCurrent(h.h5tagName,h,s[c-1])){f=!0;break}h=O(h),f=!1}}if(f&&l.isAdjacentSibling){var v=O(e);if(v&&v.h5tagName){var p=r.matchCurrent(v.h5tagName,v,s[c-1]);p||(f=!1)}else f=!1}if(f){if(c===s.length-1)return i+u;c<s.length-1&&(n[a]+=1)}else l.isChild&&c>0&&(n[a]-=1,r.matchCurrent(t,e,s[n[a]])&&(n[a]+=1));return i},"");return i}},{key:"matchCurrent",value:function(t,e,n){if(n.tag&&n.tag!==t)return!1;if(n.id&&n.id!==e.id)return!1;if(n.class.length)for(var r=e.className.split(" "),i=0;i<n.class.length;i++){var o=n.class[i];if(-1===r.indexOf(o))return!1}if(n.attrs.length)for(var a=0;a<n.attrs.length;a++){var u=n.attrs[a],s=u.all,c=u.key,l=u.value;if(s&&!e.hasAttribute(c))return!1;var d=e.getAttribute(c);if(d!==E(l||""))return!1}return!0}}])}();function O(t){var e=t.parentElement;if(!e)return null;var n=t.previousSibling;return n?1===n.nodeType?n:O(n):null}function _(t){return t.sort(function(t,e){var n=R(t.selectorList),r=R(e.selectorList);if(n!==r)return n-r;var i=M(t.selectorList),o=M(e.selectorList);if(i!==o)return i-o;var a=B(t.selectorList),u=B(e.selectorList);return a-u})}function R(t){return t.reduce(function(t,e){return t+(e.id?1:0)},0)}function M(t){return t.reduce(function(t,e){return t+e.class.length+e.attrs.length},0)}function B(t){return t.reduce(function(t,e){return t+(e.tag?1:0)},0)}var D=n(6570);function G(t,e){for(var n=Object.create(null),r=t.split(","),i=0;i<r.length;i++)n[r[i]]=!0;return function(t){return!!n[t.toLowerCase()]}}var F={img:"image",iframe:"web-view"},j=Object.keys(D.YN).map(function(t){return t.toLowerCase()}).join(","),U=G(j),V=G("a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b"),H=G("address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt"),W={li:["ul","ol","menu"],dt:["dl"],dd:["dl"],tbody:["table"],thead:["table"],tfoot:["table"],tr:["table"],td:["table"]};function $(t,e){var n=W[t];if(n){var r=e.length-1;while(r>=0){var i=e[r].tagName;if(i===t)break;if(n&&n.includes(i))return!0;r--}}return!1}function K(t){return o.f.html.renderHTMLTag?t:F[t]?F[t]:U(t)?t:H(t)?"view":V(t)?"text":"view"}function z(t){var e="=",n=t.indexOf(e);if(-1===n)return[t];var r=t.slice(0,n).trim(),i=t.slice(n+e.length).trim();return[r,i]}function Y(t,e,n,r){return t.filter(function(t){return"comment"!==t.type&&("text"!==t.type||""!==t.content)}).map(function(t){if("text"===t.type){var i=e.createTextNode(t.content);return(0,u.Tn)(o.f.html.transformText)&&(i=o.f.html.transformText(i,t)),null===r||void 0===r||r.appendChild(i),i}var s=e.createElement(K(t.tagName));s.h5tagName=t.tagName,null===r||void 0===r||r.appendChild(s),o.f.html.renderHTMLTag||(s.className="h5-".concat(t.tagName));for(var c=0;c<t.attributes.length;c++){var l=t.attributes[c],d=z(l),f=(0,a.A)(d,2),h=f[0],v=f[1];if("class"===h)s.className+=" "+E(v);else{if("o"===h[0]&&"n"===h[1])continue;s.setAttribute(h,null==v||E(v))}}var p=n.styleTagParser,g=n.descendantList,m=g.slice(),b=p.matchStyle(t.tagName,s,m);return s.setAttribute("style",b+s.style.cssText),Y(t.children,e,{styleTagParser:p,descendantList:m},s),(0,u.Tn)(o.f.html.transformElement)?o.f.html.transformElement(s,t):s})}function q(t,e){var n=new N;t=n.extractStyle(t);var r=new w(t).scan(),i={tagName:"",children:[],type:"element",attributes:[]},o={tokens:r,cursor:0,stack:[i]};return J(o),Y(i.children,e,{styleTagParser:n,descendantList:Array(n.styles.length).fill(0)})}function J(t){var e=t.tokens,n=t.stack,r=t.cursor,i=e.length,a=n[n.length-1].children;while(r<i){var u=e[r];if("tag-start"===u.type){var s=e[++r];r++;var c=s.content.toLowerCase();if(u.close){var l=n.length,d=!1;while(--l>-1)if(n[l].tagName===c){d=!0;break}while(r<i){var f=e[r];if("tag-end"!==f.type)break;r++}if(d){n.splice(l);break}}else{var h=o.f.html.closingElements.has(c),v=h;if(v&&(v=!$(c,n)),v){var p=n.length-1;while(p>0){if(c===n[p].tagName){n.splice(p);var g=p-1;a=n[g].children;break}p-=1}}var m=[],b=void 0;while(r<i){if(b=e[r],"tag-end"===b.type)break;m.push(b.content),r++}r++;var y=[],k={type:"element",tagName:s.content,attributes:m,children:y};a.push(k);var A=!(b.close||o.f.html.voidElements.has(c));if(A){n.push({tagName:c,children:y});var w={tokens:e,cursor:r,stack:n};J(w),r=w.cursor}}}else a.push(u),r++}t.cursor=r}function Z(t,e){while(t.firstChild)t.removeChild(t.firstChild);for(var n=q(e,t.ownerDocument),r=0;r<n.length;r++)t.appendChild(n[r])}o.f.html={skipElements:new Set(["style","script"]),voidElements:new Set(["!doctype","area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),closingElements:new Set(["html","head","body","p","dt","dd","li","option","thead","th","tbody","tr","td","tfoot","colgroup"]),renderHTMLTag:!1},"mini"!==r.pD.WEB&&"mini"!==r.pD.HARMONY&&i.X.extend("innerHTML",{set:function(t){Z.call(this,this,t)},get:function(){return""}});var Q=n(9260),X=n(7819),tt=n(6021),et=n(6541),nt=n(7432),rt=n(1554),it=n(6079),ot=n(5943),at=n(3363),ut=n(831),st=n(1389),ct=n(7860),lt=n(6326),dt=n(6696),ft=n(8480),ht=n(9602),vt=n(3186),pt=n(6919),gt=n(5501),mt=function(t){function e(){return(0,s.A)(this,e),(0,pt.A)(this,e,arguments)}return(0,gt.A)(e,t),(0,c.A)(e)}(lt.$),bt=n(3173),yt=n(1702),kt=n(2709),At=n(5518),wt=n(9452),Et=n(8870),St=n(3896),Ct=n(8132),Tt=n(8322),xt=n(7768),Lt=n(4733);function It(){}var Pt=n(9924),Nt=n(9844)},8322:function(t,e,n){"use strict";n.d(e,{d:function(){return a}});var r=n(5518),i=n(9260),o=100,a=function(t,e){var n=Date.now(),a=r.E.router,u=function(){setTimeout(function(){e?t.call(e):t()},1)};if(null===a)return u();var s=a.$taroPath;function c(){var r=i.A.document.getElementById(s);(null===r||void 0===r?void 0:r.pendingUpdate)?r.enqueueUpdateCallback(t,e):Date.now()-n>o?u():setTimeout(function(){return c()},20)}c()}},9489:function(t,e,n){"use strict";n.d(e,{f:function(){return r}});var r={prerender:!0,debug:!1}},7768:function(t,e,n){"use strict";n.d(e,{k:function(){return d}});var r,i,o=n(3029),a=n(2901),u=n(1635),s=n(9489),c=n(9924),l=function(){function t(){(0,o.A)(this,t),r.add(this),this.recorder=new Map}return(0,a.A)(t,[{key:"start",value:function(t){s.f.debug&&this.recorder.set(t,Date.now())}},{key:"stop",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();if(s.f.debug){var n=this.recorder.get(t);if(n>=0){this.recorder.delete(t);var o=e-n;console.log("".concat(t," \u65f6\u957f\uff1a ").concat(o,"ms \u5f00\u59cb\u65f6\u95f4\uff1a").concat((0,u.gn)(this,r,"m",i).call(this,n)," \u7ed3\u675f\u65f6\u95f4\uff1a").concat((0,u.gn)(this,r,"m",i).call(this,e)))}}}},{key:"delayStop",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500;if(s.f.debug)return(0,c.s)(function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now(),r=arguments.length>1?arguments[1]:void 0;e.stop(t,n),null===r||void 0===r||r()},n)}}])}();r=new WeakSet,i=function(t){var e=new Date(t);return"".concat(e.getHours(),":").concat(e.getMinutes(),":").concat(e.getSeconds(),".").concat("".concat(e.getMilliseconds()).padStart(3,"0"))};var d=new l},2587:function(t,e,n){"use strict";n.d(e,{b:function(){return o}});var r=n(3029),i=n(2901),o=function(){function t(e){(0,r.A)(this,t),this.cache=new Map,this.name=e}return(0,i.A)(t,[{key:"has",value:function(t){return this.cache.has(t)}},{key:"set",value:function(t,e){t&&e&&this.cache.set(t,e)}},{key:"get",value:function(t){if(this.has(t))return this.cache.get(t)}},{key:"delete",value:function(t){this.cache.delete(t)}}])}()},4733:function(t,e,n){"use strict";n.d(e,{Cn:function(){return d},DD:function(){return g},F$:function(){return s},KH:function(){return l},Oc:function(){return v},Rh:function(){return h},Vg:function(){return f},X$:function(){return m},dg:function(){return b},h6:function(){return y},vq:function(){return c}});var r=n(436),i=n(7766),o=n(7225),a=n(6570),u=n(2709),s=function(){for(var t=[],e=65;e<=90;e++)t.push(e);for(var n=97;n<=122;n++)t.push(n);var i=t.length-1,o=[0,0];return function(){var e=o.map(function(e){return t[e]}),n=String.fromCharCode.apply(String,(0,r.A)(e)),a=o.length-1;o[a]++;while(o[a]>i){if(o[a]=0,a-=1,a<0){o.push(0);break}o[a]++}return n}};function c(t){return 1===t.nodeType}function l(t){return 3===t.nodeType}function d(t){return t.nodeName===u.YK}function f(t){var e=Object.keys(t.props).find(function(t){return!(/^(class|style|id)$/.test(t)||t.startsWith("data-"))});return Boolean(e)}function h(t,e){var n;while(t=(null===t||void 0===t?void 0:t.parentElement)||null){if(!t||t.nodeName===u.r7||"root-portal"===t.nodeName)return!1;if(null===(n=t.__handlers[e])||void 0===n?void 0:n.length)return!0}return!1}function v(t){switch(t){case u.Y:return"st";case u.ID:return u.V0;case u.tp:return"cl";default:return t}}var p,g=new Map;function m(t,e,n){(0,i.Tn)(n)&&(n={value:n}),Object.defineProperty(t.prototype,e,Object.assign({configurable:!0,enumerable:!0},n))}function b(){return p||(p=(0,o.dg)(a.YN)),p}function y(t){return t+"px"}},9924:function(t,e,n){"use strict";function r(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:250,r=arguments.length>2?arguments[2]:void 0,i=0;return function(){for(var o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];var s=r||this,c=Date.now();c-i>n?(t.apply(this,a),i=c):(clearTimeout(e),e=setTimeout(function(){i=c,t.apply(s,a)},n))}}function i(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:250,r=arguments.length>2?arguments[2]:void 0;return function(){for(var i=arguments.length,o=new Array(i),a=0;a<i;a++)o[a]=arguments[a];var u=r||this;clearTimeout(e),e=setTimeout(function(){t.apply(u,o)},n)}}n.d(e,{n:function(){return r},s:function(){return i}})},9844:function(t,e,n){"use strict";n.d(e,{Hl:function(){return s},lA:function(){return a},p$:function(){return d},pb:function(){return u},rE:function(){return l},tb:function(){return o},yK:function(){return c}});var r=n(5544),i=n(7860),o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"/"===t.charAt(0)?t:"/"+t},a=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return new RegExp("^"+e+"(\\/|\\?|#|$)","i").test(t)||t===e},u=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return a(t,e)?t.substring(e.length):t},s=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return t.replace(/[?#][\s\S]*$/,"")},c=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return t.includes(e)?t.substring(0,t.length-e.length):t},l=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",s=o(u(e,n)),c=(null===(t=Object.entries(i).find(function(t){var e=(0,r.A)(t,1),n=e[0];return n===s}))||void 0===t?void 0:t[1])||s;return a||("string"===typeof c?c:c[0])||n},d=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"hash",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",n="hash"===t?i.fC.hash.slice(1).split("?")[0]:i.fC.pathname;return o(u(n,e))}},6570:function(t,e,n){"use strict";n.d(e,{Ig:function(){return Q},YN:function(){return Z}});var r="[]",i="",o="!0",a="!1",u={bindTouchStart:i,bindTouchMove:i,bindTouchEnd:i,bindTouchCancel:i,bindLongTap:i},s={animation:i,bindAnimationStart:i,bindAnimationIteration:i,bindAnimationEnd:i,bindTransitionEnd:i};function c(t){return"'".concat(t,"'")}var l=Object.assign(Object.assign({"hover-class":c("none"),"hover-stop-propagation":a,"hover-start-time":"50","hover-stay-time":"400"},u),s),d={type:i,size:"23",color:i},f=Object.assign({longitude:i,latitude:i,scale:"16",markers:r,covers:i,polyline:r,circles:r,controls:r,"include-points":r,"show-location":i,"layer-style":"1",bindMarkerTap:i,bindControlTap:i,bindCalloutTap:i,bindUpdated:i},u),h={percent:i,"stroke-width":"6",color:c("#09BB07"),activeColor:c("#09BB07"),backgroundColor:c("#EBEBEB"),active:a,"active-mode":c("backwards"),"show-info":a},v={nodes:r},p=Object.assign({selectable:a,space:i,decode:a},u),g=Object.assign({size:c("default"),type:i,plain:a,disabled:i,loading:a,"form-type":i,"open-type":i,"hover-class":c("button-hover"),"hover-stop-propagation":a,"hover-start-time":"20","hover-stay-time":"70",name:i,bindagreeprivacyauthorization:i},u),m={value:i,disabled:i,checked:a,color:c("#09BB07"),name:i},b={bindChange:i,name:i},y={"report-submit":a,bindSubmit:i,bindReset:i,name:i},k={value:i,type:c(i),password:a,placeholder:i,"placeholder-style":i,"placeholder-class":c("input-placeholder"),disabled:i,maxlength:"140","cursor-spacing":"0",focus:a,"confirm-type":c("done"),"confirm-hold":a,cursor:"-1","selection-start":"-1","selection-end":"-1",bindInput:i,bindFocus:i,bindBlur:i,bindConfirm:i,name:i},A=Object.assign({for:i,name:i},u),w={mode:c("selector"),disabled:i,range:i,"range-key":i,value:i,start:i,end:i,fields:c("day"),"custom-item":i,name:i,bindCancel:i,bindChange:i,bindColumnChange:i},E={value:i,"indicator-style":i,"indicator-class":i,"mask-style":i,"mask-class":i,bindChange:i,name:i},S={name:i},C={value:i,checked:a,disabled:i,color:c("#09BB07"),name:i},T={bindChange:i,name:i},x={min:"0",max:"100",step:"1",disabled:i,value:"0",activeColor:c("#1aad19"),backgroundColor:c("#e9e9e9"),"block-size":"28","block-color":c("#ffffff"),"show-value":a,bindChange:i,bindChanging:i,name:i},L={checked:a,disabled:i,type:c("switch"),color:c("#04BE02"),bindChange:i,name:i},I={value:i,placeholder:i,"placeholder-style":i,"placeholder-class":c("textarea-placeholder"),disabled:i,maxlength:"140","auto-focus":a,focus:a,"auto-height":a,fixed:a,"cursor-spacing":"0",cursor:"-1","selection-start":"-1","selection-end":"-1",bindFocus:i,bindBlur:i,bindLineChange:i,bindInput:i,bindConfirm:i,name:i},P={src:i,bindLoad:"eh",bindError:"eh"},N=Object.assign({"scroll-top":a},u),O={"scale-area":a},_=Object.assign(Object.assign({direction:"none",inertia:a,"out-of-bounds":a,x:i,y:i,damping:"20",friction:"2",disabled:i,scale:a,"scale-min":"0.5","scale-max":"10","scale-value":"1",bindChange:i,bindScale:i,bindHTouchMove:i,bindVTouchMove:i,width:c("10px"),height:c("10px")},u),s),R=Object.assign(Object.assign({"scroll-x":a,"scroll-y":a,"upper-threshold":"50","lower-threshold":"50","scroll-top":i,"scroll-left":i,"scroll-into-view":i,"scroll-with-animation":a,"enable-back-to-top":a,bindScrollToUpper:i,bindScrollToLower:i,bindScroll:i},u),s),M=Object.assign({"indicator-dots":a,"indicator-color":c("rgba(0, 0, 0, .3)"),"indicator-active-color":c("#000000"),autoplay:a,current:"0",interval:"5000",duration:"500",circular:a,vertical:a,"previous-margin":c("0px"),"next-margin":c("0px"),"display-multiple-items":"1",bindChange:i,bindTransition:i,bindAnimationFinish:i},u),B={"item-id":i},D={url:i,"open-type":c("navigate"),delta:"1","hover-class":c("navigator-hover"),"hover-stop-propagation":a,"hover-start-time":"50","hover-stay-time":"600",bindSuccess:i,bindFail:i,bindComplete:i},G={id:i,src:i,loop:a,controls:a,poster:i,name:i,author:i,bindError:i,bindPlay:i,bindPause:i,bindTimeUpdate:i,bindEnded:i},F={"device-position":c("back"),flash:c("auto"),bindStop:i,bindError:i},j=Object.assign({src:i,mode:c("scaleToFill"),"lazy-load":a,bindError:i,bindLoad:i},u),U=Object.assign({src:i,autoplay:a,muted:a,orientation:c("vertical"),"object-fit":c("contain"),"background-mute":a,"min-cache":"1","max-cache":"3",bindStateChange:i,bindFullScreenChange:i,bindNetStatus:i},s),V=Object.assign({src:i,duration:i,controls:o,"danmu-list":i,"danmu-btn":i,"enable-danmu":i,autoplay:a,loop:a,muted:a,"initial-time":"0","page-gesture":a,direction:i,"show-progress":o,"show-fullscreen-btn":o,"show-play-btn":o,"show-center-play-btn":o,"enable-progress-gesture":o,"object-fit":c("contain"),poster:i,"show-mute-btn":a,bindPlay:i,bindPause:i,bindEnded:i,bindTimeUpdate:i,bindFullScreenChange:i,bindWaiting:i,bindError:i},s),H=Object.assign({"canvas-id":i,"disable-scroll":a,bindError:i},u),W={"unit-id":i,"ad-intervals":i,bindLoad:i,bindError:i,bindClose:i},$={src:i,bindMessage:i,bindLoad:i,bindError:i},K={},z={name:i},Y={name:i},q={name:i},J={},Z={View:l,Icon:d,Progress:h,RichText:v,Text:p,Button:g,Checkbox:m,CheckboxGroup:b,Form:y,Input:k,Label:A,Picker:w,PickerView:E,PickerViewColumn:S,Radio:C,RadioGroup:T,Slider:x,Switch:L,CoverImage:P,Textarea:I,CoverView:N,MovableArea:O,MovableView:_,ScrollView:R,Swiper:M,SwiperItem:B,Navigator:D,Audio:G,Camera:F,Image:j,LivePlayer:U,Video:V,Canvas:H,Ad:W,WebView:$,Block:K,Map:f,Slot:Y,SlotView:z,NativeSlot:q,Script:J},Q=new Set(["input","checkbox","picker","picker-view","radio","slider","switch","textarea"]);new Set(["input","textarea"]),new Set(["progress","icon","rich-text","input","textarea","slider","switch","audio","ad","official-account","open-data","navigation-bar"]),new Map([["view",-1],["catch-view",-1],["cover-view",-1],["static-view",-1],["pure-view",-1],["click-view",-1],["block",-1],["text",-1],["static-text",6],["slot",8],["slot-view",8],["label",6],["form",4],["scroll-view",4],["swiper",4],["swiper-item",4]])},8885:function(t,e,n){"use strict";var r;n.d(e,{pD:function(){return r}}),function(t){t["MINI"]="mini",t["ASCF"]="ascf",t["WEB"]="web",t["RN"]="rn",t["HARMONY"]="harmony",t["QUICK"]="quickapp"}(r||(r={}));r.WEB,r.HARMONY,r.ASCF,r.MINI,r.RN,r.QUICK},7819:function(t,e,n){"use strict";n.d(e,{s:function(){return a}});var r=n(2284),i=n(3029),o=n(2901),a=function(){function t(e){var n;(0,i.A)(this,t),this.callbacks=null!==(n=null===e||void 0===e?void 0:e.callbacks)&&void 0!==n?n:{}}return(0,o.A)(t,[{key:"on",value:function(e,n,i){var o,a,u;if(!n)return this;u="symbol"===(0,r.A)(e)?[e]:e.split(t.eventSplitter),this.callbacks||(this.callbacks={});var s=this.callbacks;while(o=u.shift()){var c=s[o],l=c?c.tail:{};l.next=a={},l.context=i,l.callback=n,s[o]={tail:a,next:c?c.next:l}}return this}},{key:"once",value:function(t,e,n){var r=this,i=function(){for(var o=arguments.length,a=new Array(o),u=0;u<o;u++)a[u]=arguments[u];e.apply(r,a),r.off(t,i,n)};return this.on(t,i,n),this}},{key:"off",value:function(e,n,i){var o,a,u;if(!(a=this.callbacks))return this;if(!(e||n||i))return delete this.callbacks,this;u="symbol"===(0,r.A)(e)?[e]:e?e.split(t.eventSplitter):Object.keys(a);while(o=u.shift()){var s=a[o];if(delete a[o],s&&(n||i)){var c=s.tail;while((s=s.next)!==c){var l=s.callback,d=s.context;(n&&l!==n||i&&d!==i)&&this.on(o,l,d)}}}return this}},{key:"trigger",value:function(e){var n,i,o,a;if(!(o=this.callbacks))return this;a="symbol"===(0,r.A)(e)?[e]:e.split(t.eventSplitter);for(var u=arguments.length,s=new Array(u>1?u-1:0),c=1;c<u;c++)s[c-1]=arguments[c];while(n=a.shift())if(i=o[n]){var l=i.tail;while((i=i.next)!==l)i.callback.apply(i.context||this,s)}return this}}])}();a.eventSplitter=","},7766:function(t,e,n){"use strict";n.d(e,{Et:function(){return l},Gv:function(){return u},Kg:function(){return i},Lm:function(){return s},Tn:function(){return c},b0:function(){return o},cy:function(){return d},kZ:function(){return a}});var r=n(2284);function i(t){return"string"===typeof t}function o(t){return"undefined"===typeof t}function a(t){return null===t}function u(t){return null!==t&&"object"===(0,r.A)(t)}function s(t){return!0===t||!1===t}function c(t){return"function"===typeof t}function l(t){return Number.isFinite?Number.isFinite(t):"number"===typeof t}var d=Array.isArray},6021:function(t,e,n){"use strict";n.d(e,{JL:function(){return h}});var r,i=n(3029),o=n(2901),a=n(6919),u=n(5501),s=n(7819),c=n(7766);(function(t){t[t["SINGLE"]=0]="SINGLE",t[t["MULTI"]=1]="MULTI",t[t["WATERFALL"]=2]="WATERFALL"})(r||(r={}));var l={app:["onLaunch","onShow","onHide"],page:["onLoad","onUnload","onReady","onShow","onHide",["onPullDownRefresh","onReachBottom","onPageScroll","onResize","defer:onTabItemTap","onTitleClick","onOptionMenuClick","onPopMenuClick","onPullIntercept","onAddToFavorites"],["onShareAppMessage","onShareTimeline"]],component:["attached","detached"]};function d(t,e){return{type:t,initial:e||null}}var f=function(t){function e(t,n){var r;for(var o in(0,i.A)(this,e),r=(0,a.A)(this,e,[n]),r.hooks=t,t){var u=t[o].initial;(0,c.Tn)(u)&&r.on(o,u)}return r}return(0,u.A)(e,t),(0,o.A)(e,[{key:"tapOneOrMany",value:function(t,e){var n=this,r=(0,c.Tn)(e)?[e]:e;r.forEach(function(e){return n.on(t,e)})}},{key:"tap",value:function(t,e){var n=this.hooks,i=n[t],o=i.type,a=i.initial;o===r.SINGLE?(this.off(t),this.on(t,(0,c.Tn)(e)?e:e[e.length-1])):(a&&this.off(t,a),this.tapOneOrMany(t,e))}},{key:"call",value:function(t){var e,n=this.hooks[t];if(n){var i=n.type,o=this.callbacks;if(o){var a=o[t];if(a){for(var u=a.tail,s=a.next,c=arguments.length,l=new Array(c>1?c-1:0),d=1;d<c;d++)l[d-1]=arguments[d];var f,h=l;while(s!==u){if(f=null===(e=s.callback)||void 0===e?void 0:e.apply(s.context||this,h),i===r.WATERFALL){var v=[f];h=v}s=s.next}return f}}}}},{key:"isExist",value:function(t){var e;return Boolean(null===(e=this.callbacks)||void 0===e?void 0:e[t])}}])}(s.s),h=new f({getMiniLifecycle:d(r.SINGLE,function(t){return t}),getMiniLifecycleImpl:d(r.SINGLE,function(){return this.call("getMiniLifecycle",l)}),getLifecycle:d(r.SINGLE,function(t,e){return t[e]}),modifyRecursiveComponentConfig:d(r.SINGLE,function(t){return t}),getPathIndex:d(r.SINGLE,function(t){return"[".concat(t,"]")}),getEventCenter:d(r.SINGLE,function(t){return new t}),isBubbleEvents:d(r.SINGLE,function(t){var e=new Set(["touchstart","touchmove","touchcancel","touchend","touchforcechange","tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend"]);return e.has(t)}),getSpecialNodes:d(r.SINGLE,function(){return["view","text","image"]}),onRemoveAttribute:d(r.SINGLE),batchedEventUpdates:d(r.SINGLE),mergePageInstance:d(r.SINGLE),modifyPageObject:d(r.SINGLE),createPullDownComponent:d(r.SINGLE),getDOMNode:d(r.SINGLE),modifyHydrateData:d(r.SINGLE),transferHydrateData:d(r.SINGLE),modifySetAttrPayload:d(r.SINGLE),modifyRmAttrPayload:d(r.SINGLE),onAddEvent:d(r.SINGLE),proxyToRaw:d(r.SINGLE,function(t){return t}),modifyMpEvent:d(r.MULTI),modifyMpEventImpl:d(r.SINGLE,function(t){try{this.call("modifyMpEvent",t)}catch(t){console.warn("[Taro modifyMpEvent hook Error]: "+(null===t||void 0===t?void 0:t.message))}}),injectNewStyleProperties:d(r.SINGLE),modifyTaroEvent:d(r.MULTI),dispatchTaroEvent:d(r.SINGLE,function(t,e){e.dispatchEvent(t)}),dispatchTaroEventFinish:d(r.MULTI),modifyTaroEventReturn:d(r.SINGLE,function(){}),modifyDispatchEvent:d(r.MULTI),initNativeApi:d(r.MULTI),patchElement:d(r.MULTI),modifyAddEventListener:d(r.SINGLE),modifyRemoveEventListener:d(r.SINGLE),getMemoryLevel:d(r.SINGLE)})},7225:function(t,e,n){"use strict";n.d(e,{Cb:function(){return s},D8:function(){return l},IQ:function(){return p},Lj:function(){return u},MZ:function(){return o},R8:function(){return d},ZG:function(){return m},ZH:function(){return c},Zb:function(){return b},dg:function(){return g},lQ:function(){return a},vk:function(){return y}});var r=n(6570),i=n(6021),o={},a=function(){};function u(t){return t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}function s(t){for(var e="",n=!1,r=0;r<t.length;r++)"-"!==t[r]?(e+=n?t[r].toUpperCase():t[r],n=!1):n=!0;return e}function c(t){return t.charAt(0).toUpperCase()+t.slice(1)}Object.prototype.hasOwnProperty;function l(t,e){if(!t)throw new Error(e)}function d(t,e){0}var f=1,h=(new Date).getTime().toString();function v(){return h+f++}function p(t){return Object.keys(t).forEach(function(e){e in r.YN?Object.assign(r.YN[e],t[e]):r.YN[e]=t[e]}),r.YN}function g(t){var e={},n=t.View,r={"#text":{},StaticView:n,StaticImage:t.Image,StaticText:t.Text,PureView:n,CatchView:n,ClickView:n};return t=Object.assign(Object.assign({},t),r),Object.keys(t).sort(function(t,e){var n=/^(Static|Pure|Catch|Click)*(View|Image|Text)$/,r=n.test(t),i=n.test(e);return r&&i?t>e?1:-1:r?-1:i||t>=e?1:-1}).forEach(function(n,r){var i={_num:String(r)};Object.keys(t[n]).filter(function(t){return!/^bind/.test(t)&&!["focus","blur"].includes(t)}).sort().forEach(function(t,e){i[s(t)]="p"+e}),e[u(n)]=i}),e}function m(t,e){var n=e||i.JL,r=Object.keys(t);r.forEach(function(e){n.tap(e,t[e])})}function b(t){return function(){console.warn("\u5c0f\u7a0b\u5e8f\u6682\u4e0d\u652f\u6301 ".concat(t))}}function y(t,e){var n="__key_",r=["navigateTo","redirectTo","reLaunch","switchTab"];if(r.indexOf(t)>-1){var i=e.url=e.url||"",o=i.indexOf("?")>-1,a=v();e.url+=(o?"&":"?")+"".concat(n,"=").concat(a)}}},758:function(t,e,n){var r=n(892),i=r.hooks,o=n(3114).A;i.isExist("initNativeApi")&&i.call("initNativeApi",o),t.exports=o,t.exports["default"]=t.exports},5838:function(t,e,n){"use strict";var r=n(8870);Component((0,r.s$)())},2736:function(t,e,n){"use strict";var r=n(8870);Component((0,r.s$)("custom-wrapper"))}}]);