<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国打工人</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            overflow: hidden;
            user-select: none;
            touch-action: manipulation;
            position: relative;
            width: 100vw;
            height: 100vh;
            background-color: #f0f0e8;
        }

        #game-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        #background {
            position: absolute;
            width: 100%;
            height: 100%;
            background-image: url('./图片素材/游戏背景.png');
            background-size: cover;
            background-position: center;
        }

        #title-bar {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background-color: rgba(100, 100, 180, 0.5);
            z-index: 1;
        }

        #title {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            max-width: 500px;
            height: auto;
            z-index: 2;
        }

        #timer {
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 60px;
            background-image: url('./图片素材/倒计时框.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            z-index: 3;
        }

        #character {
            position: absolute;
            top: 40%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 30%;
            height: 60%;
            background-image: url('./图片素材/张飞主角色招聘图.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 3;
        }

        #speech-bubble {
            position: absolute;
            top: 20%;
            right: 10%;
            width: 35%;
            height: 20%;
            background-image: url('./图片素材/主角色说话弹窗.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            z-index: 5;
        }

        #speech-text {
            font-size: 1rem;
            text-align: center;
            color: #333;
            width: 80%;
        }

        #info-card {
            position: absolute;
            bottom: 15%;
            left: 5%;
            width: 35%;
            height: 60%;
            background-image: url('./图片素材/左下角资料信息图.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transition: transform 0.3s ease;
            transform-origin: bottom left;
            cursor: pointer;
            z-index: 4;
        }

        #info-card.enlarged {
            transform: scale(1.5);
            z-index: 100;
        }

        #info-content {
            position: absolute;
            top: 25%;
            left: 15%;
            width: 70%;
            height: 60%;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: flex-start;
            font-size: 0.8rem;
            color: #333;
        }

        #info-name {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .info-item {
            margin-bottom: 5px;
        }

        #bottom-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background-color: rgba(50, 50, 100, 0.7);
            z-index: 2;
        }

        #buttons {
            position: absolute;
            bottom: 10%;
            right: 5%;
            display: flex;
            gap: 20px;
            z-index: 5;
        }

        #hire-button {
            width: 80px;
            height: 80px;
            background-image: url('./图片素材/录用按钮.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        #reject-button {
            width: 80px;
            height: 80px;
            background-image: url('./图片素材/淘汰按钮.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        #hire-button:hover, #reject-button:hover {
            transform: scale(1.1);
        }

        #hire-button:active, #reject-button:active {
            transform: scale(0.95);
        }

        #scanner {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: none;
            z-index: 10;
        }

        #scan-frame {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 70%;
            background-image: url('./图片素材/扫描时的框.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        #scan-bar {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 70%;
            height: 10px;
            background-image: url('./图片素材/扫描条(由上向下普速).png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            animation: scanMove 2s linear infinite;
        }

        @keyframes scanMove {
            0% { top: 15%; }
            100% { top: 85%; }
        }

        #result {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0);
            width: 150px;
            height: 150px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            transition: transform 0.3s ease;
            z-index: 20;
        }

        #result.success {
            background-image: url('./图片素材/招聘成功的盖章.png');
            transform: translate(-50%, -50%) scale(1);
        }

        #result.fail {
            background-image: url('./图片素材/招聘失败的淘汰盖章.png');
            transform: translate(-50%, -50%) scale(1);
        }

        #targets {
            position: absolute;
            top: 20%;
            left: 2%;
            width: 60px;
            height: 300px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 4;
        }

        .target-slot {
            width: 50px;
            height: 50px;
            background-image: url('./图片素材/左侧需要招到的人数标(招到一个，填黑一个).png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.5;
        }

        .target-slot.filled {
            opacity: 1;
        }

        #tools {
            position: absolute;
            top: 50%;
            right: 2%;
            display: flex;
            flex-direction: column;
            gap: 20px;
            z-index: 4;
        }

        #detector {
            width: 60px;
            height: 60px;
            background-image: url('./图片素材/探测图标.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        #time-add {
            width: 60px;
            height: 60px;
            background-image: url('./图片素材/加时图标.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        #detector:hover, #time-add:hover {
            transform: scale(1.1);
        }

        #detector:active, #time-add:active {
            transform: scale(0.95);
        }

        /* 左侧人物头像列表 */
        #avatar-list {
            position: absolute;
            left: 10px;
            top: 20%;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 4;
        }

        .avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #fff;
            border: 2px solid #ccc;
        }

        #start-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
        }

        #start-button {
            padding: 15px 40px;
            font-size: 1.5rem;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.5);
            transition: all 0.3s ease;
        }

        #start-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 7px 20px rgba(255, 107, 107, 0.7);
        }

        #game-over {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 100;
            color: white;
        }

        #game-over h1 {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        #game-over p {
            font-size: 1.5rem;
            margin-bottom: 30px;
        }

        #restart-button {
            padding: 15px 40px;
            font-size: 1.5rem;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            border-radius: 30px;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.5);
            transition: all 0.3s ease;
        }

        #restart-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 7px 20px rgba(79, 172, 254, 0.7);
        }

        @media (max-width: 768px) {
            #speech-bubble {
                width: 50%;
                height: 15%;
                right: 10%;
            }

            #speech-text {
                font-size: 0.8rem;
            }

            #info-card {
                width: 40%;
                height: 40%;
                left: 5%;
            }

            #info-content {
                font-size: 0.7rem;
            }

            #info-name {
                font-size: 1rem;
            }

            #hire-button, #reject-button {
                width: 60px;
                height: 60px;
            }

            #detector, #time-add {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="background"></div>
        <div id="title-bar"></div>
        <img id="title" src="./图片素材/背景上部标题(临时工招聘会).png" alt="临时工招聘会">
        
        <div id="timer">01:53</div>
        
        <div id="bottom-bar"></div>
        <div id="character"></div>
        
        <div id="speech-bubble">
            <div id="speech-text">老板，很多人冒充我们三国人士，我才是真的</div>
        </div>
        
        <div id="info-card" onclick="toggleInfoCard()">
            <div id="info-content">
                <div id="info-name">张飞</div>
                <div class="info-item">• 黑发</div>
                <div class="info-item">• 墨镜</div>
                <div class="info-item">• 皮肤黝黑</div>
                <div class="info-item">• 易怒</div>
                <div class="info-item">• 擅长使矛</div>
            </div>
        </div>
        
        <div id="buttons">
            <div id="hire-button" onclick="makeDecision('hire')"></div>
            <div id="reject-button" onclick="makeDecision('reject')"></div>
        </div>
        
        <div id="targets">
            <div class="target-slot" id="slot-1"></div>
            <div class="target-slot" id="slot-2"></div>
            <div class="target-slot" id="slot-3"></div>
            <div class="target-slot" id="slot-4"></div>
            <div class="target-slot" id="slot-5"></div>
        </div>
        
        <div id="tools">
            <div id="detector" onclick="useDetector()"></div>
            <div id="time-add" onclick="addTime()"></div>
        </div>

        <!-- 左侧头像列表 -->
        <div id="avatar-list">
            <div class="avatar"></div>
            <div class="avatar"></div>
            <div class="avatar"></div>
            <div class="avatar"></div>
            <div class="avatar"></div>
        </div>
        
        <div id="scanner">
            <div id="scan-frame"></div>
            <div id="scan-bar"></div>
        </div>
        
        <div id="result"></div>
        
        <div id="start-screen">
            <h1 style="color: white; font-size: 2.5rem; margin-bottom: 30px;">三国打工人</h1>
            <!-- <p style="color: white; font-size: 1.2rem; margin-bottom: 50px; text-align: center; max-width: 80%;">
                在众多冒充者中招到真正的三国打工人！<br>
                仔细对比资料，做出明智的选择。
            </p> -->
            <button id="start-button" onclick="startGame()">开始招聘</button>
        </div>
        
        <div id="game-over">
            <h1>游戏结束</h1>
            <p id="result-text">你成功招聘了 0 名真正的三国打工人！</p>
            <button id="restart-button" onclick="restartGame()">再来一次</button>
        </div>
    </div>

    <script>
        // 游戏变量
        let gameRunning = false;
        let timeLeft = 120; // 2分钟
        let timerInterval;
        let hiredCount = 0;
        let targetCount = 5;
        let wrongHires = 0;
        let maxWrongHires = 3;
        
        // 角色数据
        const characters = [
            {
                name: "张飞",
                traits: ["黑发", "墨镜", "皮肤黝黑", "易怒", "擅长使矛"],
                isReal: true,
                image: "./图片素材/张飞主角色招聘图.png",
                startDialogues: [
                    "老板，我很强的，我抓鹅特别厉害",
                    "老板，你看我合适吗",
                    "老板，请让我和您一起干番大事业",
                    "老板，你看我的宝剑锋利吗"
                ],
                hireDialogues: [
                    "老板，您眼光真准",
                    "老板，您一看就是做大事的人",
                    "谢谢老板，我一定会好好努力",
                    "我确实很厉害"
                ],
                rejectDialogues: [
                    "我被淘汰辣？？？",
                    "哎老板你听我说啊，好歹加个v啊，哎老板……",
                    "不要我算了，正好另一家开高薪请我呢",
                    "你不相信我？也罢"
                ]
            },
            {
                name: "假张飞",
                traits: ["黑发", "墨镜", "皮肤白皙", "易怒", "擅长使矛"],
                isReal: false,
                image: "./图片素材/张飞主角色招聘图.png",
                startDialogues: [
                    "老板，很多人冒充我们三国人士，我才是真的",
                    "老板您看，我是大汉三本毕业的",
                    "不用看，就我了",
                    "老板，可找着你们了"
                ],
                hireDialogues: [
                    "老板，您眼光真准",
                    "老板，您一看就是做大事的人",
                    "谢谢老板，我一定会好好努力",
                    "我确实很厉害"
                ],
                rejectDialogues: [
                    "呃，我回去准备一下再来",
                    "哎呀，老板你是不是没看清楚啊",
                    "哎不是，老板，我真能辅佐老板打天下的啊",
                    "我就知道白跑一趟"
                ]
            }
            // 可以添加更多角色
        ];
        
        let currentCharacter = null;
        
        // 初始化游戏
        function initGame() {
            hiredCount = 0;
            wrongHires = 0;
            timeLeft = 120;
            updateTimer();
            updateTargets();
        }
        
        // 开始游戏
        function startGame() {
            document.getElementById('start-screen').style.display = 'none';
            gameRunning = true;
            initGame();
            nextCharacter();
            
            // 启动计时器
            timerInterval = setInterval(() => {
                timeLeft--;
                updateTimer();
                
                if (timeLeft <= 0) {
                    endGame();
                }
            }, 1000);
        }
        
        // 更新计时器显示
        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            document.getElementById('timer').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
        
        // 更新目标槽位
        function updateTargets() {
            for (let i = 1; i <= targetCount; i++) {
                const slot = document.getElementById(`slot-${i}`);
                if (i <= hiredCount) {
                    slot.classList.add('filled');
                } else {
                    slot.classList.remove('filled');
                }
            }
        }
        
        // 下一个角色
        function nextCharacter() {
            // 随机选择角色
            const randomIndex = Math.floor(Math.random() * characters.length);
            currentCharacter = characters[randomIndex];
            
            // 更新角色图像
            document.getElementById('character').style.backgroundImage = `url('${currentCharacter.image}')`;
            
            // 更新资料卡
            document.getElementById('info-name').textContent = currentCharacter.name;
            const infoContent = document.getElementById('info-content');
            infoContent.innerHTML = `<div id="info-name">${currentCharacter.name}</div>`;
            
            currentCharacter.traits.forEach(trait => {
                const traitElement = document.createElement('div');
                traitElement.className = 'info-item';
                traitElement.textContent = `• ${trait}`;
                infoContent.appendChild(traitElement);
            });
            
            // 更新对话
            const randomDialogueIndex = Math.floor(Math.random() * currentCharacter.startDialogues.length);
            document.getElementById('speech-text').textContent = currentCharacter.startDialogues[randomDialogueIndex];
        }
        
        // 做出决定（录用/淘汰）
        function makeDecision(decision) {
            if (!gameRunning) return;
            
            // 显示扫描动画
            document.getElementById('scanner').style.display = 'block';
            
            setTimeout(() => {
                document.getElementById('scanner').style.display = 'none';
                
                const result = document.getElementById('result');
                let isCorrectDecision = false;
                
                if (decision === 'hire' && currentCharacter.isReal) {
                    // 正确录用
                    result.className = 'success';
                    hiredCount++;
                    isCorrectDecision = true;
                } else if (decision === 'reject' && !currentCharacter.isReal) {
                    // 正确淘汰
                    result.className = 'fail';
                    isCorrectDecision = true;
                } else if (decision === 'hire' && !currentCharacter.isReal) {
                    // 错误录用
                    result.className = 'fail';
                    wrongHires++;
                } else {
                    // 错误淘汰
                    result.className = 'fail';
                }
                
                // 显示结果
                result.style.display = 'block';
                
                // 更新对话
                const dialogues = decision === 'hire' ? 
                    currentCharacter.hireDialogues : 
                    currentCharacter.rejectDialogues;
                const randomDialogueIndex = Math.floor(Math.random() * dialogues.length);
                document.getElementById('speech-text').textContent = dialogues[randomDialogueIndex];
                
                // 更新目标槽位
                updateTargets();
                
                // 检查游戏是否结束
                if (hiredCount >= targetCount || wrongHires >= maxWrongHires) {
                    setTimeout(endGame, 1500);
                    return;
                }
                
                // 延迟后进入下一个角色
                setTimeout(() => {
                    result.style.display = 'none';
                    nextCharacter();
                }, 1500);
            }, 2000); // 扫描动画持续2秒
        }
        
        // 使用探测仪
        function useDetector() {
            if (!gameRunning) return;
            
            // 这里可以添加探测仪功能，比如高亮显示不匹配的特征
            alert('探测到可疑特征！请仔细检查资料信息。');
        }
        
        // 添加时间
        function addTime() {
            if (!gameRunning) return;
            
            // 添加60秒
            timeLeft += 60;
            updateTimer();
            
            // 显示通知
            alert('已增加60秒时间！');
        }
        
        // 切换资料卡大小
        function toggleInfoCard() {
            const infoCard = document.getElementById('info-card');
            infoCard.classList.toggle('enlarged');
        }
        
        // 游戏结束
        function endGame() {
            gameRunning = false;
            clearInterval(timerInterval);
            
            // 显示游戏结束界面
            const gameOver = document.getElementById('game-over');
            const resultText = document.getElementById('result-text');
            
            if (hiredCount >= targetCount) {
                resultText.textContent = `恭喜！你成功招聘了 ${hiredCount} 名真正的三国打工人！`;
            } else if (wrongHires >= maxWrongHires) {
                resultText.textContent = `你错误录用了 ${wrongHires} 名冒充者，招聘失败！`;
            } else {
                resultText.textContent = `时间到！你只招聘到了 ${hiredCount}/${targetCount} 名三国打工人。`;
            }
            
            gameOver.style.display = 'flex';
        }
        
        // 重新开始游戏
        function restartGame() {
            document.getElementById('game-over').style.display = 'none';
            startGame();
        }

        // 页面加载完成后自动启动游戏（可选，也可以保留开始界面）
        // document.addEventListener('DOMContentLoaded', startGame);
    </script>
</body>
</html>