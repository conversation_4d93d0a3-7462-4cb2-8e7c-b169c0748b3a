<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽印章测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
            height: 100vh;
            overflow: hidden;
            position: relative;
        }
        
        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .info-card {
            position: absolute;
            bottom: 15%;
            left: 5%;
            width: 300px;
            height: 400px;
            background: rgba(139, 69, 19, 0.9);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            z-index: 4;
            transition: all 0.3s ease;
            border: 3px solid transparent;
        }
        
        .info-card.drag-target {
            border-color: #ff4444;
            box-shadow: 0 0 20px #ff4444;
            transform: scale(1.05);
        }
        
        .info-content {
            color: #FFD700;
            font-size: 16px;
            line-height: 1.5;
        }
        
        .info-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            color: #FFF;
        }
        
        .info-item {
            margin-bottom: 8px;
            font-size: 16px;
            display: flex;
            align-items: center;
        }
        
        .info-item::before {
            content: '•';
            color: #FFD700;
            margin-right: 6px;
        }
        
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 120px;
            background: rgba(139, 69, 19, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 20;
        }
        
        .buttons {
            display: flex;
            gap: 60px;
            align-items: center;
            justify-content: center;
        }
        
        .hire-button, .reject-button {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            color: white;
            cursor: grab;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: none;
            user-select: none;
        }
        
        .hire-button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
        }
        
        .hire-button:active {
            cursor: grabbing;
            transform: scale(1.1);
        }
        
        .reject-button {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .reject-button:active {
            cursor: grabbing;
            transform: scale(1.1);
        }
        
        .dragging-stamp {
            position: fixed;
            width: 60px;
            height: 60px;
            z-index: 1000;
            pointer-events: none;
            opacity: 0.8;
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
            color: white;
        }
        
        .dragging-stamp.hire {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
        }
        
        .dragging-stamp.reject {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .stamp-progress-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            z-index: 50;
        }
        
        .stamp-progress-bg {
            position: relative;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .stamp-progress-fill {
            position: absolute;
            top: -3px;
            left: -3px;
            width: calc(100% + 6px);
            height: calc(100% + 6px);
            border-radius: 50%;
            border: 3px solid transparent;
            border-top-color: #ff4444;
            transition: transform 0.1s ease;
            transform-origin: center;
        }
        
        .stamp-progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 14px;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 4px rgba(0, 0, 0, 0.8);
        }
        
        .stamp-result-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 100;
            background: rgba(0, 0, 0, 0.5);
        }
        
        .stamp-result-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: stampAppear 0.5s ease-out;
        }
        
        .stamp-result-text {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
        }
        
        .stamp-result-icon {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            border: 4px solid white;
        }
        
        .stamp-result-icon.hire {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
        }
        
        .stamp-result-icon.reject {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .stamp-icon-text {
            font-size: 48px;
            font-weight: bold;
            color: white;
            text-shadow: 0 0 10px rgba(0, 0, 0, 0.8);
        }
        
        @keyframes stampAppear {
            0% {
                transform: scale(0) rotate(-180deg);
                opacity: 0;
            }
            50% {
                transform: scale(1.2) rotate(-90deg);
                opacity: 0.8;
            }
            100% {
                transform: scale(1) rotate(0deg);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="game-container" id="gameContainer">
        <!-- 信息卡片 -->
        <div class="info-card" id="infoCard">
            <div class="info-content">
                <div class="info-name">张飞</div>
                <div class="info-item">姓名：张飞</div>
                <div class="info-item">字：翼德</div>
                <div class="info-item">籍贯：涿郡</div>
                <div class="info-item">职业：武将</div>
                <div class="info-item">特长：使用丈八蛇矛</div>
                <div class="info-item">性格：勇猛刚烈</div>
                <div class="info-item">学历：大汉三本毕业</div>
            </div>
        </div>

        <!-- 底部按钮 -->
        <div class="bottom-bar">
            <div class="buttons">
                <button class="hire-button" id="hireButton">录用</button>
                <button class="reject-button" id="rejectButton">淘汰</button>
            </div>
        </div>
    </div>

    <script>
        let isDragging = false;
        let dragType = null;
        let dragPosition = { x: 0, y: 0 };
        let isOverInfoCard = false;
        let stampProgress = 0;
        let isStamping = false;
        let progressInterval = null;

        const gameContainer = document.getElementById('gameContainer');
        const infoCard = document.getElementById('infoCard');
        const hireButton = document.getElementById('hireButton');
        const rejectButton = document.getElementById('rejectButton');

        // 拖拽开始
        function handleDragStart(type, event) {
            event.preventDefault();
            isDragging = true;
            dragType = type;
            
            const touch = event.touches ? event.touches[0] : event;
            dragPosition = { x: touch.clientX, y: touch.clientY };
            
            // 创建拖拽印章
            createDraggingStamp();
        }

        // 拖拽移动
        function handleDragMove(event) {
            if (!isDragging) return;
            event.preventDefault();
            
            const touch = event.touches ? event.touches[0] : event;
            dragPosition = { x: touch.clientX, y: touch.clientY };
            
            // 更新拖拽印章位置
            updateDraggingStamp();
            
            // 检查是否在信息卡区域
            const rect = infoCard.getBoundingClientRect();
            const isOver = touch.clientX >= rect.left && 
                          touch.clientX <= rect.right && 
                          touch.clientY >= rect.top && 
                          touch.clientY <= rect.bottom;
            
            if (isOver && !isOverInfoCard) {
                setIsOverInfoCard(true);
                startStamping();
            } else if (!isOver && isOverInfoCard) {
                setIsOverInfoCard(false);
                cancelStamping();
            }
        }

        // 拖拽结束
        function handleDragEnd(event) {
            if (!isDragging) return;
            event.preventDefault();
            
            isDragging = false;
            dragType = null;
            setIsOverInfoCard(false);
            cancelStamping();
            removeDraggingStamp();
        }

        // 创建拖拽印章
        function createDraggingStamp() {
            const stamp = document.createElement('div');
            stamp.id = 'draggingStamp';
            stamp.className = `dragging-stamp ${dragType}`;
            stamp.textContent = dragType === 'hire' ? '录用' : '淘汰';
            stamp.style.left = `${dragPosition.x - 30}px`;
            stamp.style.top = `${dragPosition.y - 30}px`;
            document.body.appendChild(stamp);
        }

        // 更新拖拽印章位置
        function updateDraggingStamp() {
            const stamp = document.getElementById('draggingStamp');
            if (stamp) {
                stamp.style.left = `${dragPosition.x - 30}px`;
                stamp.style.top = `${dragPosition.y - 30}px`;
            }
        }

        // 移除拖拽印章
        function removeDraggingStamp() {
            const stamp = document.getElementById('draggingStamp');
            if (stamp) {
                stamp.remove();
            }
        }

        // 设置信息卡状态
        function setIsOverInfoCard(value) {
            isOverInfoCard = value;
            if (value) {
                infoCard.classList.add('drag-target');
            } else {
                infoCard.classList.remove('drag-target');
            }
        }

        // 开始盖章进度
        function startStamping() {
            if (isStamping) return;
            
            isStamping = true;
            stampProgress = 0;
            
            // 创建进度条
            createProgressBar();
            
            progressInterval = setInterval(() => {
                stampProgress += 10;
                updateProgressBar();
                
                if (stampProgress >= 100) {
                    clearInterval(progressInterval);
                    completeStamping();
                }
            }, 100);
        }

        // 取消盖章
        function cancelStamping() {
            isStamping = false;
            stampProgress = 0;
            
            if (progressInterval) {
                clearInterval(progressInterval);
                progressInterval = null;
            }
            
            removeProgressBar();
        }

        // 完成盖章
        function completeStamping() {
            isStamping = false;
            isDragging = false;
            setIsOverInfoCard(false);
            removeDraggingStamp();
            removeProgressBar();
            
            // 显示盖章结果
            showStampResult();
        }

        // 创建进度条
        function createProgressBar() {
            const container = document.createElement('div');
            container.id = 'progressContainer';
            container.className = 'stamp-progress-container';
            
            const bg = document.createElement('div');
            bg.className = 'stamp-progress-bg';
            
            const fill = document.createElement('div');
            fill.id = 'progressFill';
            fill.className = 'stamp-progress-fill';
            
            const text = document.createElement('div');
            text.id = 'progressText';
            text.className = 'stamp-progress-text';
            text.textContent = '0%';
            
            bg.appendChild(fill);
            container.appendChild(bg);
            container.appendChild(text);
            infoCard.appendChild(container);
        }

        // 更新进度条
        function updateProgressBar() {
            const fill = document.getElementById('progressFill');
            const text = document.getElementById('progressText');
            
            if (fill && text) {
                fill.style.transform = `rotate(${(stampProgress / 100) * 360}deg)`;
                text.textContent = `${Math.round(stampProgress)}%`;
            }
        }

        // 移除进度条
        function removeProgressBar() {
            const container = document.getElementById('progressContainer');
            if (container) {
                container.remove();
            }
        }

        // 显示盖章结果
        function showStampResult() {
            const overlay = document.createElement('div');
            overlay.className = 'stamp-result-overlay';
            
            const content = document.createElement('div');
            content.className = 'stamp-result-content';
            
            const text = document.createElement('div');
            text.className = 'stamp-result-text';
            text.textContent = dragType === 'hire' ? '录用成功！' : '淘汰成功！';
            
            const icon = document.createElement('div');
            icon.className = `stamp-result-icon ${dragType}`;
            
            const iconText = document.createElement('div');
            iconText.className = 'stamp-icon-text';
            iconText.textContent = dragType === 'hire' ? '✓' : '✗';
            
            icon.appendChild(iconText);
            content.appendChild(text);
            content.appendChild(icon);
            overlay.appendChild(content);
            document.body.appendChild(overlay);
            
            // 2秒后移除
            setTimeout(() => {
                overlay.remove();
            }, 2000);
        }

        // 绑定事件
        hireButton.addEventListener('touchstart', (e) => handleDragStart('hire', e));
        hireButton.addEventListener('mousedown', (e) => handleDragStart('hire', e));

        rejectButton.addEventListener('touchstart', (e) => handleDragStart('reject', e));
        rejectButton.addEventListener('mousedown', (e) => handleDragStart('reject', e));

        document.addEventListener('touchmove', handleDragMove);
        document.addEventListener('mousemove', handleDragMove);

        document.addEventListener('touchend', handleDragEnd);
        document.addEventListener('mouseup', handleDragEnd);
    </script>
</body>
</html>
