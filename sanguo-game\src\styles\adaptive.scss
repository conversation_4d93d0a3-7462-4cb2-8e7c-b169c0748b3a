/* 适配工具类样式 */

/* 安全区域适配类 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-all {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 响应式显示类 */
.hide-on-small {
  @media (max-width: 375px) {
    display: none !important;
  }
}

.hide-on-medium {
  @media (max-width: 750px) {
    display: none !important;
  }
}

.hide-on-landscape {
  @media (orientation: landscape) and (max-height: 500px) {
    display: none !important;
  }
}

.show-on-small {
  display: none !important;
  @media (max-width: 375px) {
    display: block !important;
  }
}

.show-on-medium {
  display: none !important;
  @media (max-width: 750px) {
    display: block !important;
  }
}

.show-on-landscape {
  display: none !important;
  @media (orientation: landscape) and (max-height: 500px) {
    display: block !important;
  }
}

/* 响应式字体大小类 */
.text-responsive-xs {
  font-size: 1.2rem;
  @media (max-width: 750px) {
    font-size: 1.1rem;
  }
  @media (max-width: 375px) {
    font-size: 1rem;
  }
}

.text-responsive-sm {
  font-size: 1.6rem;
  @media (max-width: 750px) {
    font-size: 1.4rem;
  }
  @media (max-width: 375px) {
    font-size: 1.2rem;
  }
}

.text-responsive-md {
  font-size: 2rem;
  @media (max-width: 750px) {
    font-size: 1.8rem;
  }
  @media (max-width: 375px) {
    font-size: 1.6rem;
  }
}

.text-responsive-lg {
  font-size: 2.4rem;
  @media (max-width: 750px) {
    font-size: 2.2rem;
  }
  @media (max-width: 375px) {
    font-size: 2rem;
  }
}

.text-responsive-xl {
  font-size: 3rem;
  @media (max-width: 750px) {
    font-size: 2.6rem;
  }
  @media (max-width: 375px) {
    font-size: 2.2rem;
  }
}

/* 响应式间距类 */
.spacing-responsive-xs {
  margin: 10rpx;
  @media (max-width: 750px) {
    margin: 8rpx;
  }
  @media (max-width: 375px) {
    margin: 6rpx;
  }
}

.spacing-responsive-sm {
  margin: 20rpx;
  @media (max-width: 750px) {
    margin: 16rpx;
  }
  @media (max-width: 375px) {
    margin: 12rpx;
  }
}

.spacing-responsive-md {
  margin: 30rpx;
  @media (max-width: 750px) {
    margin: 24rpx;
  }
  @media (max-width: 375px) {
    margin: 18rpx;
  }
}

.spacing-responsive-lg {
  margin: 40rpx;
  @media (max-width: 750px) {
    margin: 32rpx;
  }
  @media (max-width: 375px) {
    margin: 24rpx;
  }
}

/* 响应式尺寸类 */
.size-responsive-sm {
  width: 80rpx;
  height: 80rpx;
  @media (max-width: 750px) {
    width: 70rpx;
    height: 70rpx;
  }
  @media (max-width: 375px) {
    width: 60rpx;
    height: 60rpx;
  }
}

.size-responsive-md {
  width: 120rpx;
  height: 120rpx;
  @media (max-width: 750px) {
    width: 100rpx;
    height: 100rpx;
  }
  @media (max-width: 375px) {
    width: 80rpx;
    height: 80rpx;
  }
}

.size-responsive-lg {
  width: 160rpx;
  height: 160rpx;
  @media (max-width: 750px) {
    width: 140rpx;
    height: 140rpx;
  }
  @media (max-width: 375px) {
    width: 120rpx;
    height: 120rpx;
  }
}

/* 横屏适配类 */
.landscape-compact {
  @media (orientation: landscape) and (max-height: 500px) {
    transform: scale(0.8);
    transform-origin: center;
  }
}

.landscape-hide {
  @media (orientation: landscape) and (max-height: 500px) {
    display: none !important;
  }
}

/* 触摸优化类 */
.touch-friendly {
  min-width: 44px;
  min-height: 44px;
  touch-action: manipulation;
}

/* 防止文本选择 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 硬件加速 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* 平滑滚动 */
.smooth-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
