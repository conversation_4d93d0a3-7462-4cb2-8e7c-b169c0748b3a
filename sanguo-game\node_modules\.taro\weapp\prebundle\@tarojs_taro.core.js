import { Current, dist_exports, eventCenter, getCurrentInstance, init_dist as init_dist2, nextTick, options } from "./chunk-OU7DT526.js";
import { Events, init_dist, isFunction, isObject, isUndefined } from "./chunk-UH2DPGZU.js";
import { __commonJS, __esm, __export, __toCommonJS } from "./chunk-QRPWKJ4C.js";
// node_modules/@tarojs/api/dist/env.js
function getEnv() {
    if (true) {
        return ENV_TYPE.WEAPP;
    } else if (false) {
        return ENV_TYPE.ALIPAY;
    } else if (false) {
        return ENV_TYPE.SWAN;
    } else if (false) {
        return ENV_TYPE.TT;
    } else if (false) {
        return ENV_TYPE.JD;
    } else if (false) {
        return ENV_TYPE.QQ;
    } else if (false) {
        return ENV_TYPE.HARMONYHYBRID;
    } else if (false) {
        return ENV_TYPE.WEB;
    } else if (false) {
        return ENV_TYPE.RN;
    } else if (false) {
        return ENV_TYPE.HARMONY;
    } else if (false) {
        return ENV_TYPE.QUICKAPP;
    } else if (false) {
        return ENV_TYPE.ASCF;
    } else {
        return "weapp";
    }
}
var ENV_TYPE;
var init_env = __esm({
    "node_modules/@tarojs/api/dist/env.js" () {
        ENV_TYPE = {
            ASCF: "ASCF",
            WEAPP: "WEAPP",
            SWAN: "SWAN",
            ALIPAY: "ALIPAY",
            TT: "TT",
            QQ: "QQ",
            JD: "JD",
            WEB: "WEB",
            RN: "RN",
            HARMONY: "HARMONY",
            QUICKAPP: "QUICKAPP",
            HARMONYHYBRID: "HARMONYHYBRID"
        };
    }
});
// node_modules/@tarojs/api/dist/interceptor/chain.js
var Chain;
var init_chain = __esm({
    "node_modules/@tarojs/api/dist/interceptor/chain.js" () {
        init_dist();
        Chain = class _Chain {
            proceed(requestParams = {}) {
                this.requestParams = requestParams;
                if (this.index >= this.interceptors.length) {
                    throw new Error("chain \u53C2\u6570\u9519\u8BEF, \u8BF7\u52FF\u76F4\u63A5\u4FEE\u6539 request.chain");
                }
                const nextInterceptor = this._getNextInterceptor();
                const nextChain = this._getNextChain();
                const p = nextInterceptor(nextChain);
                const res = p.catch((err)=>Promise.reject(err));
                Object.keys(p).forEach((k)=>isFunction(p[k]) && (res[k] = p[k]));
                return res;
            }
            _getNextInterceptor() {
                return this.interceptors[this.index];
            }
            _getNextChain() {
                return new _Chain(this.requestParams, this.interceptors, this.index + 1);
            }
            constructor(requestParams, interceptors, index){
                this.index = index || 0;
                this.requestParams = requestParams || {};
                this.interceptors = interceptors || [];
            }
        };
    }
});
// node_modules/@tarojs/api/dist/interceptor/index.js
function interceptorify(promiseifyApi) {
    return new Link(function(chain) {
        return promiseifyApi(chain.requestParams);
    });
}
var Link;
var init_interceptor = __esm({
    "node_modules/@tarojs/api/dist/interceptor/index.js" () {
        init_chain();
        Link = class {
            request(requestParams) {
                const chain = this.chain;
                const taroInterceptor = this.taroInterceptor;
                chain.interceptors = chain.interceptors.filter((interceptor)=>interceptor !== taroInterceptor).concat(taroInterceptor);
                return chain.proceed(Object.assign({}, requestParams));
            }
            addInterceptor(interceptor) {
                this.chain.interceptors.push(interceptor);
            }
            cleanInterceptors() {
                this.chain = new Chain();
            }
            constructor(interceptor){
                this.taroInterceptor = interceptor;
                this.chain = new Chain();
            }
        };
    }
});
// node_modules/@tarojs/api/dist/interceptor/interceptors.js
var interceptors_exports = {};
__export(interceptors_exports, {
    logInterceptor: ()=>logInterceptor,
    timeoutInterceptor: ()=>timeoutInterceptor
});
function timeoutInterceptor(chain) {
    const requestParams = chain.requestParams;
    let p;
    const res = new Promise((resolve, reject)=>{
        const timeout = setTimeout(()=>{
            clearTimeout(timeout);
            reject(new Error("\u7F51\u7EDC\u94FE\u63A5\u8D85\u65F6,\u8BF7\u7A0D\u540E\u518D\u8BD5\uFF01"));
        }, requestParams && requestParams.timeout || 6e4);
        p = chain.proceed(requestParams);
        p.then((res2)=>{
            if (!timeout) return;
            clearTimeout(timeout);
            resolve(res2);
        }).catch((err)=>{
            timeout && clearTimeout(timeout);
            reject(err);
        });
    });
    if (!isUndefined(p) && isFunction(p.abort)) res.abort = p.abort;
    return res;
}
function logInterceptor(chain) {
    const requestParams = chain.requestParams;
    const { method, data, url } = requestParams;
    console.log(`http ${method || "GET"} --> ${url} data: `, data);
    const p = chain.proceed(requestParams);
    const res = p.then((res2)=>{
        console.log(`http <-- ${url} result:`, res2);
        return res2;
    });
    if (isFunction(p.abort)) res.abort = p.abort;
    return res;
}
var init_interceptors = __esm({
    "node_modules/@tarojs/api/dist/interceptor/interceptors.js" () {
        init_dist();
    }
});
// node_modules/@tarojs/api/dist/tools.js
function Behavior(options2) {
    return options2;
}
function getPreload(current) {
    return function(key, val) {
        current.preloadData = isObject(key) ? key : {
            [key]: val
        };
    };
}
function getInitPxTransform(taro) {
    return function(config) {
        const { designWidth = defaultDesignWidth, deviceRatio = defaultDesignRatio, baseFontSize = defaultBaseFontSize, targetUnit = defaultTargetUnit, unitPrecision = defaultUnitPrecision } = config;
        taro.config = taro.config || {};
        taro.config.designWidth = designWidth;
        taro.config.deviceRatio = deviceRatio;
        taro.config.baseFontSize = baseFontSize;
        taro.config.targetUnit = targetUnit;
        taro.config.unitPrecision = unitPrecision;
    };
}
function getPxTransform(taro) {
    return function(size) {
        const config = taro.config || {};
        const baseFontSize = config.baseFontSize;
        const deviceRatio = config.deviceRatio || defaultDesignRatio;
        const designWidth = ((input = 0)=>isFunction(config.designWidth) ? config.designWidth(input) : config.designWidth || defaultDesignWidth)(size);
        if (!(designWidth in deviceRatio)) {
            throw new Error(`deviceRatio \u914D\u7F6E\u4E2D\u4E0D\u5B58\u5728 ${designWidth} \u7684\u8BBE\u7F6E\uFF01`);
        }
        const targetUnit = config.targetUnit || defaultTargetUnit;
        const unitPrecision = config.unitPrecision || defaultUnitPrecision;
        const formatSize = ~~size;
        let rootValue = 1 / deviceRatio[designWidth];
        switch(targetUnit){
            case "rem":
                rootValue *= baseFontSize * 2;
                break;
            case "px":
                rootValue *= 2;
                break;
        }
        let val = formatSize / rootValue;
        if (unitPrecision >= 0 && unitPrecision <= 100) {
            val = Number(val.toFixed(unitPrecision));
        }
        return val + targetUnit;
    };
}
var defaultDesignWidth, defaultDesignRatio, defaultBaseFontSize, defaultUnitPrecision, defaultTargetUnit;
var init_tools = __esm({
    "node_modules/@tarojs/api/dist/tools.js" () {
        init_dist();
        defaultDesignWidth = 750;
        defaultDesignRatio = {
            640: 2.34 / 2,
            750: 1,
            828: 1.81 / 2
        };
        defaultBaseFontSize = 20;
        defaultUnitPrecision = 5;
        defaultTargetUnit = "rpx";
    }
});
// node_modules/@tarojs/api/dist/index.js
var dist_exports2 = {};
__export(dist_exports2, {
    default: ()=>Taro
});
var Taro;
var init_dist3 = __esm({
    "node_modules/@tarojs/api/dist/index.js" () {
        init_dist2();
        init_env();
        init_interceptor();
        init_interceptors();
        init_tools();
        Taro = {
            Behavior,
            getEnv,
            ENV_TYPE,
            Link,
            interceptors: interceptors_exports,
            Current,
            getCurrentInstance,
            options,
            nextTick,
            eventCenter,
            Events,
            getInitPxTransform,
            interceptorify
        };
        Taro.initPxTransform = getInitPxTransform(Taro);
        Taro.preload = getPreload(Current);
        Taro.pxTransform = getPxTransform(Taro);
    }
});
// node_modules/@tarojs/taro/index.js
var require_taro = __commonJS({
    "node_modules/@tarojs/taro/index.js" (exports, module) {
        var { hooks } = (init_dist2(), __toCommonJS(dist_exports));
        var taro = (init_dist3(), __toCommonJS(dist_exports2)).default;
        if (hooks.isExist("initNativeApi")) {
            hooks.call("initNativeApi", taro);
        }
        module.exports = taro;
        module.exports.default = module.exports;
    }
});
// entry:@tarojs_taro
var require_tarojs_taro = __commonJS({
    "entry:@tarojs_taro" (exports, module) {
        module.exports = require_taro();
    }
});
export default require_tarojs_taro();
