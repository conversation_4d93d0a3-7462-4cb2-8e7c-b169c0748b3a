# 三国打工人游戏尺寸调整指南

## 📋 快速调整清单

### 🎯 最常需要调整的尺寸

#### 1. 界面元素位置
```scss
/* 标题位置 */
.title {
  top: 20rpx;        /* 🔧 距离顶部距离 */
  width: 70%;        /* 🔧 标题宽度比例 */
}

/* 计时器位置 */
.timer {
  top: 120rpx;       /* 🔧 距离顶部距离 */
  width: 280rpx;     /* 🔧 计时器宽度 */
  height: 100rpx;    /* 🔧 计时器高度 */
}

/* 角色位置 */
.character {
  top: 45%;          /* 🔧 垂直位置 */
  width: 25%;        /* 🔧 角色宽度比例 */
  height: 50%;       /* 🔧 角色高度比例 */
}
```

#### 2. 文字大小
```scss
/* 计时器文字 */
.timer {
  font-size: 3.5rem; /* 🔧 计时器数字大小 */
}

/* 对话文字 */
.speech-text {
  font-size: 1.8rem; /* 🔧 对话气泡文字大小 */
}

/* 信息卡片文字 */
.info-name {
  font-size: 2.2rem; /* 🔧 姓名文字大小 */
}
.info-content {
  font-size: 1.4rem; /* 🔧 信息内容文字大小 */
}
.info-item {
  font-size: 1.2rem; /* 🔧 详细信息文字大小 */
}
```

#### 3. 按钮尺寸
```scss
/* 底部操作按钮 */
.hire-button, .reject-button {
  width: 140rpx;     /* 🔧 按钮宽度 */
  height: 140rpx;    /* 🔧 按钮高度 */
}

/* 右侧工具按钮 */
.detector, .time-add {
  width: 100rpx;     /* 🔧 工具按钮宽度 */
  height: 100rpx;    /* 🔧 工具按钮高度 */
}
```

## 🎨 界面区域详细说明

### 顶部区域
- **标题栏高度**: `height: 200rpx`
- **标题位置**: `top: 20rpx`
- **标题宽度**: `width: 70%`
- **计时器位置**: `top: 120rpx`
- **计时器尺寸**: `280rpx × 100rpx`
- **计时器文字**: `font-size: 3.5rem`

### 中央区域
- **角色位置**: `top: 45%, left: 50%`
- **角色尺寸**: `width: 25%, height: 50%`
- **对话气泡位置**: `top: 25%, right: 8%`
- **对话气泡尺寸**: `width: 32%, height: 18%`
- **对话文字大小**: `font-size: 1.8rem`

### 左侧信息区域
- **信息卡片位置**: `bottom: 18%, left: 3%`
- **信息卡片尺寸**: `width: 32%, height: 55%`
- **姓名文字**: `font-size: 2.2rem`
- **信息文字**: `font-size: 1.4rem`
- **详情文字**: `font-size: 1.2rem`

### 右侧工具区域
- **工具区位置**: `top: 45%, right: 1%`
- **工具按钮尺寸**: `100rpx × 100rpx`
- **按钮间距**: `gap: 30rpx`

### 底部按钮区域
- **按钮位置**: `bottom: 8%, right: 3%`
- **按钮尺寸**: `140rpx × 140rpx`
- **按钮间距**: `gap: 30rpx`

### 左侧目标区域
- **目标区位置**: `top: 22%, left: 1%`
- **目标槽尺寸**: `80rpx × 80rpx`
- **目标槽间距**: `gap: 15rpx`

## 📱 不同屏幕适配建议

### 小屏设备 (≤375px)
```scss
/* 建议将所有尺寸乘以 0.8 */
.timer { font-size: 2.8rem; }        /* 3.5rem × 0.8 */
.speech-text { font-size: 1.44rem; } /* 1.8rem × 0.8 */
.hire-button { width: 112rpx; }      /* 140rpx × 0.8 */
```

### 中等屏幕 (375px-750px)
```scss
/* 建议将所有尺寸乘以 0.9 */
.timer { font-size: 3.15rem; }       /* 3.5rem × 0.9 */
.speech-text { font-size: 1.62rem; } /* 1.8rem × 0.9 */
.hire-button { width: 126rpx; }      /* 140rpx × 0.9 */
```

### 大屏设备 (>750px)
```scss
/* 保持原始尺寸或略微放大 */
.timer { font-size: 3.5rem; }        /* 原始尺寸 */
.speech-text { font-size: 1.8rem; }  /* 原始尺寸 */
.hire-button { width: 140rpx; }      /* 原始尺寸 */
```

## 🔧 常见调整场景

### 场景1：文字太小看不清
```scss
/* 增加所有文字大小 */
.timer { font-size: 4rem; }          /* 从3.5rem增加到4rem */
.speech-text { font-size: 2rem; }    /* 从1.8rem增加到2rem */
.info-name { font-size: 2.4rem; }    /* 从2.2rem增加到2.4rem */
.info-content { font-size: 1.6rem; } /* 从1.4rem增加到1.6rem */
```

### 场景2：按钮太小难以点击
```scss
/* 增加按钮尺寸 */
.hire-button, .reject-button {
  width: 160rpx;   /* 从140rpx增加到160rpx */
  height: 160rpx;  /* 从140rpx增加到160rpx */
}
.detector, .time-add {
  width: 120rpx;   /* 从100rpx增加到120rpx */
  height: 120rpx;  /* 从100rpx增加到120rpx */
}
```

### 场景3：界面元素重叠
```scss
/* 调整元素位置 */
.speech-bubble {
  top: 20%;        /* 从25%调整到20% */
  right: 10%;      /* 从8%调整到10% */
}
.info-card {
  bottom: 20%;     /* 从18%调整到20% */
  left: 5%;        /* 从3%调整到5% */
}
```

### 场景4：角色太大或太小
```scss
/* 调整角色尺寸 */
.character {
  width: 20%;      /* 从25%调整到20%（变小） */
  height: 45%;     /* 从50%调整到45%（变小） */
  /* 或者 */
  width: 30%;      /* 从25%调整到30%（变大） */
  height: 55%;     /* 从50%调整到55%（变大） */
}
```

## 📝 调整步骤

1. **备份原文件**: 复制 `index.scss` 为 `index-backup.scss`
2. **使用注释版本**: 将 `index-annotated.scss` 复制为 `index.scss`
3. **逐步调整**: 根据需要修改带有 🔧 标记的参数
4. **实时预览**: 在微信开发者工具中查看效果
5. **测试不同设备**: 使用模拟器测试各种屏幕尺寸

## ⚠️ 注意事项

1. **保持比例**: 调整尺寸时要保持元素间的比例关系
2. **触摸友好**: 确保按钮最小尺寸为44rpx×44rpx
3. **文字可读性**: 确保文字在各种背景下都清晰可读
4. **性能考虑**: 避免使用过大的图片和复杂的动画
5. **兼容性**: 确保修改后的样式在微信小程序中正常工作

## 🎯 推荐的调整顺序

1. **先调整文字大小**: 确保所有文字都清晰可读
2. **再调整按钮尺寸**: 确保所有按钮都容易点击
3. **然后调整元素位置**: 避免重叠和遮挡
4. **最后调整间距**: 优化整体视觉效果
5. **全面测试**: 在不同设备上验证效果
