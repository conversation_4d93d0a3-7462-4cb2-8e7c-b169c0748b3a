<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>三国打工人 - 界面适配测试</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
            overflow: hidden;
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            /* 安全区域适配 */
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }

        .title-bar {
            position: absolute;
            top: env(safe-area-inset-top, 0);
            left: 0;
            width: 100%;
            height: 100px;
            z-index: 10;
        }

        .title {
            position: absolute;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 70%;
            max-width: 400px;
            height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
            z-index: 2;
        }

        .timer {
            position: absolute;
            top: calc(env(safe-area-inset-top, 0) + 80px);
            left: 50%;
            transform: translateX(-50%);
            width: 140px;
            height: 50px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 2rem;
            font-weight: bold;
            color: white;
            z-index: 3;
        }

        .character {
            position: absolute;
            top: 45%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 25%;
            max-width: 150px;
            height: 50%;
            max-height: 300px;
            background: rgba(100, 150, 200, 0.8);
            border-radius: 10px;
            z-index: 3;
        }

        .speech-bubble {
            position: absolute;
            top: 25%;
            right: 8%;
            width: 32%;
            max-width: 200px;
            height: 18%;
            min-height: 60px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px;
            z-index: 5;
        }

        .speech-text {
            font-size: 1.2rem;
            text-align: center;
            color: #333;
            line-height: 1.3;
        }

        .info-card {
            position: absolute;
            bottom: calc(env(safe-area-inset-bottom, 0) + 18%);
            left: 3%;
            width: 32%;
            max-width: 175px;
            height: 55%;
            max-height: 325px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            z-index: 4;
        }

        .buttons {
            position: absolute;
            bottom: calc(env(safe-area-inset-bottom, 0) + 8%);
            right: 3%;
            display: flex;
            gap: 15px;
            z-index: 5;
        }

        .hire-button, .reject-button {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            border: none;
            font-size: 1rem;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .hire-button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
        }

        .reject-button {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }

        .hire-button:active, .reject-button:active {
            transform: scale(1.1);
        }

        .tools {
            position: absolute;
            top: 45%;
            right: 1%;
            display: flex;
            flex-direction: column;
            gap: 15px;
            z-index: 4;
        }

        .detector, .time-add {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            font-size: 0.8rem;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .detector:active, .time-add:active {
            transform: scale(0.95);
        }

        /* 响应式调整 */
        @media (max-width: 750px) {
            .title {
                font-size: 1.6rem;
                width: 75%;
            }
            
            .timer {
                font-size: 1.8rem;
                width: 120px;
                height: 40px;
            }
            
            .character {
                width: 30%;
                height: 45%;
            }
            
            .speech-bubble {
                width: 38%;
                right: 5%;
                top: 22%;
            }
            
            .speech-text {
                font-size: 1.1rem;
            }
            
            .info-card {
                width: 35%;
                bottom: calc(env(safe-area-inset-bottom, 0) + 20%);
                left: 2%;
            }
            
            .hire-button, .reject-button {
                width: 60px;
                height: 60px;
                font-size: 0.9rem;
            }
            
            .buttons {
                gap: 10px;
                right: 2%;
                bottom: calc(env(safe-area-inset-bottom, 0) + 10%);
            }
            
            .tools {
                gap: 10px;
                top: 48%;
            }
            
            .detector, .time-add {
                width: 40px;
                height: 40px;
                font-size: 0.7rem;
            }
        }

        @media (max-width: 375px) {
            .title {
                font-size: 1.4rem;
                width: 80%;
            }
            
            .timer {
                font-size: 1.6rem;
                width: 100px;
                height: 35px;
            }
            
            .speech-text {
                font-size: 1rem;
            }
            
            .hire-button, .reject-button {
                width: 50px;
                height: 50px;
                font-size: 0.8rem;
            }
            
            .detector, .time-add {
                width: 35px;
                height: 35px;
                font-size: 0.6rem;
            }
        }

        /* 横屏适配 */
        @media (orientation: landscape) and (max-height: 500px) {
            .timer {
                top: calc(env(safe-area-inset-top, 0) + 30px);
                font-size: 1.4rem;
            }
            
            .character {
                top: 50%;
                width: 20%;
                height: 40%;
            }
            
            .speech-bubble {
                top: 15%;
                width: 28%;
                height: 15%;
            }
            
            .info-card {
                width: 28%;
                height: 45%;
            }
        }

        .info-text {
            font-size: 0.9rem;
            margin-bottom: 5px;
            color: #333;
        }

        .device-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.8rem;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="device-info" id="deviceInfo">
            设备信息加载中...
        </div>
        
        <div class="title-bar">
            <div class="title">三国打工人</div>
        </div>
        
        <div class="timer">01:35</div>
        
        <div class="character"></div>
        
        <div class="speech-bubble">
            <div class="speech-text">老板，可我着你们了</div>
        </div>
        
        <div class="info-card">
            <div class="info-text"><strong>姓名:</strong> 张飞</div>
            <div class="info-text"><strong>籍贯:</strong> 河东解良</div>
            <div class="info-text"><strong>职业:</strong> 武将</div>
            <div class="info-text"><strong>特长:</strong> 使用青龙偃月刀</div>
        </div>
        
        <div class="tools">
            <button class="detector">探测</button>
            <button class="time-add">加时</button>
        </div>
        
        <div class="buttons">
            <button class="hire-button">录用</button>
            <button class="reject-button">淘汰</button>
        </div>
    </div>

    <script>
        function updateDeviceInfo() {
            const info = document.getElementById('deviceInfo');
            const width = window.innerWidth;
            const height = window.innerHeight;
            const ratio = window.devicePixelRatio || 1;
            const orientation = width > height ? '横屏' : '竖屏';
            
            info.innerHTML = `
                屏幕: ${width}×${height}<br>
                像素比: ${ratio}<br>
                方向: ${orientation}<br>
                UA: ${navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备'}
            `;
        }

        // 初始化
        updateDeviceInfo();
        
        // 监听屏幕变化
        window.addEventListener('resize', updateDeviceInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateDeviceInfo, 100);
        });
        
        // 测试按钮交互
        document.querySelectorAll('button').forEach(btn => {
            btn.addEventListener('click', () => {
                console.log('按钮点击:', btn.textContent);
            });
        });
    </script>
</body>
</html>
