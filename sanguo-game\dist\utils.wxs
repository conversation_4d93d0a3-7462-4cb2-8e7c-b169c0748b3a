module.exports = {
  a: function (l, n, s) {
    var a = ["8","0","22","6","3","1","13","7","5","69","70","35","27","66","75","76","63"]
    var b = ["5","69","70","35","27","66","75","76","63"]
    if (a.indexOf(n) === -1) {
      l = 0
    }
    if (b.indexOf(n) > -1) {
      var u = s.split(',')
      var depth = 0
      for (var i = 0; i < u.length; i++) {
        if (u[i] === n) depth++
      }
      l = depth
    }
    if (l >= 15) {
      return 'tmpl_15_container'
    }
    return 'tmpl_' + l + '_' + n
  },
  b: function (a, b) {
    return a === undefined ? b : a
  },
  c: function(i, prefix) {
    var s = i.focus !== undefined ? 'focus' : 'blur'
    return prefix + i.nn + '_' + s
  },
  d: function (a) {
    return a === undefined ? {} : a
  },
  e: function (n) {
    return 'tmpl_' + n + '_container'
  },
  f: function (l, n) {
    var b = ["5","69","70","35","27","66","75","76","63"]
    if (b.indexOf(n) > -1) {
      if (l) l += ','
      l += n
    }
    return l
  }
}