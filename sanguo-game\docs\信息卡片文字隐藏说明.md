# 信息卡片文字隐藏说明

## 📋 修改目的

由于信息卡片的背景图片（`左下角资料信息图.png`）本身已经包含了完整的文字信息，因此不需要再在CSS中显示额外的配置文本。

## 🔧 修改内容

### 隐藏的元素

1. **`.info-content`** - 信息内容容器
   ```scss
   .info-content {
     display: none; /* 隐藏信息内容，因为背景图片已包含文字 */
   }
   ```

2. **`.info-name`** - 姓名文字
   ```scss
   .info-name {
     display: none; /* 隐藏姓名文字，因为背景图片已包含 */
   }
   ```

3. **`.info-item`** - 信息项文字
   ```scss
   .info-item {
     display: none; /* 隐藏信息项文字，因为背景图片已包含 */
   }
   ```

## 🎨 视觉效果

### 修改前
- 信息卡片显示背景图片
- 同时在图片上叠加显示配置的文字内容
- 可能造成文字重叠或视觉混乱

### 修改后
- 信息卡片只显示背景图片
- 不显示任何叠加的文字内容
- 界面更加简洁，完全依赖图片本身的设计

## 📱 功能保持

虽然隐藏了文字显示，但以下功能仍然正常工作：

✅ **信息卡片的交互功能**
- 点击放大效果
- 拖拽目标高亮
- 动画过渡效果

✅ **游戏逻辑功能**
- 角色数据仍然正常加载
- 游戏状态正常管理
- 所有交互逻辑保持不变

## 🔄 如何恢复文字显示

如果将来需要重新显示文字内容，只需要将以下CSS属性修改：

```scss
.info-content {
  display: flex; /* 从 none 改为 flex */
}

.info-name {
  /* 移除 display: none; */
}

.info-item {
  /* 移除 display: none; */
}
```

## 📝 相关文件

- **样式文件**: `src/pages/index/index.scss`
- **背景图片**: `src/images/左下角资料信息图.png`
- **组件文件**: `src/pages/index/index.tsx`

## 🎯 设计理念

这个修改体现了以下设计理念：

1. **图片优先**: 充分利用设计师制作的完整图片素材
2. **避免冗余**: 不重复显示相同的信息内容
3. **视觉统一**: 保持界面的视觉一致性和专业性
4. **性能优化**: 减少不必要的DOM元素渲染

## ✅ 编译状态

- **编译状态**: ✅ 成功
- **功能状态**: ✅ 正常
- **视觉效果**: ✅ 符合预期

修改已完成，信息卡片现在只显示背景图片，不再显示叠加的配置文字。
