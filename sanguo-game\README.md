# 三国打工人 - 微信小程序游戏

这是一个基于Taro框架开发的微信小程序游戏，玩家需要在众多冒充者中识别出真正的三国人物。

## 游戏介绍

### 游戏玩法
- 在限定时间内（2分钟）招聘真正的三国打工人
- 仔细对比应聘者的资料信息，识别真假
- 点击资料卡可以放大查看详细信息
- 使用探测仪和加时道具辅助判断

### 胜利条件
- 成功招聘到5名真正的三国打工人

### 失败条件
- 时间耗尽但未完成招聘目标
- 错误录用3名冒充者

## 技术栈

- **框架**: Taro 4.1.3
- **前端**: React + TypeScript
- **样式**: Sass
- **平台**: 微信小程序

## 开发环境设置

### 前置要求
- Node.js (推荐 16+)
- npm 或 yarn
- 微信开发者工具

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev:weapp
```

### 构建生产版本
```bash
npm run build:weapp
```

## 项目结构

```
sanguo-game/
├── src/
│   ├── pages/
│   │   └── index/           # 游戏主页面
│   ├── images/              # 游戏图片资源
│   ├── app.config.ts        # 应用配置
│   ├── app.scss            # 全局样式
│   └── app.ts              # 应用入口
├── dist/                   # 编译输出目录
├── config/                 # Taro配置文件
└── project.config.json     # 微信小程序配置
```

## 在微信开发者工具中运行

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目的 `dist` 目录
4. 输入项目名称和AppID（可使用测试号）
5. 点击"导入"

## 游戏特性

### 角色系统
- 真张飞：黑发、墨镜、皮肤黝黑、易怒、擅长使矛
- 假张飞：黑发、墨镜、皮肤白皙、易怒、擅长使矛

### 交互功能
- 点击录用/淘汰按钮做出决定
- 扫描动画效果
- 结果反馈（成功/失败印章）
- 实时计时器
- 进度追踪

### 道具系统
- 探测仪：帮助发现可疑特征
- 加时器：增加60秒游戏时间

## 开发说明

### 添加新角色
在 `src/pages/index/index.tsx` 的 `characters` 数组中添加新的角色数据：

```typescript
{
  name: "角色名",
  traits: ["特征1", "特征2", "特征3"],
  isReal: true/false,
  image: require("../../images/角色图片.png"),
  startDialogues: ["开始对话1", "开始对话2"],
  hireDialogues: ["录用对话1", "录用对话2"],
  rejectDialogues: ["淘汰对话1", "淘汰对话2"]
}
```

### 修改游戏参数
在组件中修改以下常量：
- `targetCount`: 目标招聘人数
- `maxWrongHires`: 最大错误录用次数
- `timeLeft`: 游戏时长（秒）

## 注意事项

1. 图片资源需要放在 `src/images/` 目录下
2. 文件名避免使用特殊字符和括号
3. 样式使用rpx单位适配不同屏幕
4. 确保微信开发者工具版本支持当前小程序API

## 许可证

本项目仅供学习和演示使用。
