<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三国打工人 - 测试版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', SimSun, sans-serif;
            background: linear-gradient(135deg, #8B4513 0%, #D2691E 50%, #F4A460 100%);
            height: 100vh;
            overflow: hidden;
        }
        
        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .title-bar {
            position: relative;
            width: 100%;
            height: 80px;
            background: rgba(139, 69, 19, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #FFD700;
            text-align: center;
        }
        
        .timer {
            position: absolute;
            top: 15px;
            right: 20px;
            width: 100px;
            height: 40px;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 18px;
            font-weight: bold;
            color: #FFD700;
            z-index: 15;
        }
        
        .game-main {
            flex: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px 20px;
        }
        
        .character {
            width: 160px;
            height: 200px;
            background: linear-gradient(45deg, #4169E1, #1E90FF);
            border-radius: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.3);
            z-index: 5;
            animation: characterPulse 3s ease-in-out infinite;
        }
        
        .speech-bubble {
            position: absolute;
            top: 120px;
            right: 40px;
            width: 250px;
            min-height: 80px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 6;
            animation: bubbleFloat 2s ease-in-out infinite alternate;
        }
        
        .speech-bubble::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 25px;
            width: 0;
            height: 0;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            border-right: 15px solid rgba(255, 255, 255, 0.95);
        }
        
        .speech-text {
            font-size: 16px;
            color: #333;
            text-align: left;
            line-height: 1.4;
        }
        
        .info-card {
            position: absolute;
            bottom: 120px;
            left: 20px;
            width: 200px;
            min-height: 200px;
            background: rgba(139, 69, 19, 0.9);
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            z-index: 7;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .info-card.enlarged {
            transform: scale(1.2);
            z-index: 100;
        }
        
        .info-content {
            color: #FFD700;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .info-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            color: #FFF;
        }
        
        .info-item {
            margin-bottom: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
        }
        
        .info-item::before {
            content: '•';
            color: #FFD700;
            margin-right: 6px;
        }
        
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: rgba(139, 69, 19, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 20;
        }
        
        .buttons {
            display: flex;
            gap: 60px;
            align-items: center;
            justify-content: center;
        }
        
        .hire-button, .reject-button {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            border: none;
        }
        
        .hire-button {
            background: linear-gradient(45deg, #4CAF50, #66BB6A);
        }
        
        .hire-button:hover {
            background: linear-gradient(45deg, #388E3C, #4CAF50);
            transform: scale(0.95);
        }
        
        .reject-button {
            background: linear-gradient(45deg, #F44336, #EF5350);
        }
        
        .reject-button:hover {
            background: linear-gradient(45deg, #D32F2F, #F44336);
            transform: scale(0.95);
        }
        
        @keyframes bubbleFloat {
            0% { transform: translateY(0px); }
            100% { transform: translateY(-8px); }
        }
        
        @keyframes characterPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="title-bar">
            <!-- <div class="title">三国打工人</div> -->
            <div class="timer">01:30</div>
        </div>

        <div class="game-main">
            <div class="character">
                <div style="font-size: 40px; color: white;">👤</div>
                <div style="font-size: 14px; color: white; margin-top: 8px;">张飞</div>
            </div>
        </div>
        
        <div class="speech-bubble">
            <div class="speech-text">主公，我很强的，我不跨特别厉害</div>
        </div>

        <div class="info-card" onclick="toggleCard()">
            <div class="info-content">
                <div class="info-name">张飞</div>
                <div class="info-item">黑色墨镜皮肤</div>
                <div class="info-item">点黑</div>
                <div class="info-item">易怒</div>
                <div class="info-item">擅长使矛</div>
            </div>
        </div>

        <div class="bottom-bar">
            <div class="buttons">
                <button class="hire-button" onclick="makeDecision('hire')">录用</button>
                <button class="reject-button" onclick="makeDecision('reject')">淘汰</button>
            </div>
        </div>
    </div>

    <script>
        let isCardEnlarged = false;
        
        function toggleCard() {
            const card = document.querySelector('.info-card');
            isCardEnlarged = !isCardEnlarged;
            if (isCardEnlarged) {
                card.classList.add('enlarged');
            } else {
                card.classList.remove('enlarged');
            }
        }
        
        function makeDecision(decision) {
            if (decision === 'hire') {
                alert('录用了张飞！');
            } else {
                alert('淘汰了张飞！');
            }
        }
    </script>
</body>
</html>
