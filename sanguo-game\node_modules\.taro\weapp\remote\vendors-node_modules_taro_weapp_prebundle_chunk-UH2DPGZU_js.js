"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["vendors-node_modules_taro_weapp_prebundle_chunk-UH2DPGZU_js"],{

/***/ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js":
/*!**************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   __commonJS: function() { return /* binding */ __commonJS; },
/* harmony export */   __esm: function() { return /* binding */ __esm; },
/* harmony export */   __export: function() { return /* binding */ __export; },
/* harmony export */   __toCommonJS: function() { return /* binding */ __toCommonJS; },
/* harmony export */   __toESM: function() { return /* binding */ __toESM; }
/* harmony export */ });
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __esm = (fn, res)=>function __init() {
        return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
    };
var __commonJS = (cb, mod)=>function __require() {
        return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = {
            exports: {}
        }).exports, mod), mod.exports;
    };
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
var __copyProps = (to, from, except, desc)=>{
    if (from && typeof from === "object" || typeof from === "function") {
        for (let key of __getOwnPropNames(from))if (!__hasOwnProp.call(to, key) && key !== except) __defProp(to, key, {
            get: ()=>from[key],
            enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable
        });
    }
    return to;
};
var __toESM = (mod, isNodeMode, target)=>(target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(// If the importer is in node compatibility mode or this is not an ESM
    // file that has been converted to a CommonJS file using a Babel-
    // compatible transform (i.e. "__esModule" has not been set), then set
    // "default" to the CommonJS "module.exports" for node compatibility.
    isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", {
        value: mod,
        enumerable: true
    }) : target, mod));
var __toCommonJS = (mod)=>__copyProps(__defProp({}, "__esModule", {
        value: true
    }), mod);



/***/ }),

/***/ "./node_modules/.taro/weapp/prebundle/chunk-UH2DPGZU.js":
/*!**************************************************************!*\
  !*** ./node_modules/.taro/weapp/prebundle/chunk-UH2DPGZU.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   EMPTY_OBJ: function() { return /* binding */ EMPTY_OBJ; },
/* harmony export */   Events: function() { return /* binding */ Events; },
/* harmony export */   PLATFORM_TYPE: function() { return /* binding */ PLATFORM_TYPE; },
/* harmony export */   capitalize: function() { return /* binding */ capitalize; },
/* harmony export */   controlledComponent: function() { return /* binding */ controlledComponent; },
/* harmony export */   ensure: function() { return /* binding */ ensure; },
/* harmony export */   getComponentsAlias: function() { return /* binding */ getComponentsAlias; },
/* harmony export */   hooks: function() { return /* binding */ hooks; },
/* harmony export */   init_dist: function() { return /* binding */ init_dist; },
/* harmony export */   internalComponents: function() { return /* binding */ internalComponents; },
/* harmony export */   isArray: function() { return /* binding */ isArray; },
/* harmony export */   isBoolean: function() { return /* binding */ isBoolean; },
/* harmony export */   isFunction: function() { return /* binding */ isFunction; },
/* harmony export */   isNull: function() { return /* binding */ isNull; },
/* harmony export */   isNumber: function() { return /* binding */ isNumber; },
/* harmony export */   isObject: function() { return /* binding */ isObject; },
/* harmony export */   isString: function() { return /* binding */ isString; },
/* harmony export */   isUndefined: function() { return /* binding */ isUndefined; },
/* harmony export */   mergeInternalComponents: function() { return /* binding */ mergeInternalComponents; },
/* harmony export */   mergeReconciler: function() { return /* binding */ mergeReconciler; },
/* harmony export */   noop: function() { return /* binding */ noop; },
/* harmony export */   processApis: function() { return /* binding */ processApis; },
/* harmony export */   toCamelCase: function() { return /* binding */ toCamelCase; },
/* harmony export */   toDashed: function() { return /* binding */ toDashed; },
/* harmony export */   warn: function() { return /* binding */ warn; }
/* harmony export */ });
/* harmony import */ var _chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./chunk-QRPWKJ4C.js */ "./node_modules/.taro/weapp/prebundle/chunk-QRPWKJ4C.js");

// node_modules/@tarojs/shared/dist/event-emitter.js
var Events;
var init_event_emitter = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/event-emitter.js" () {
        Events = class _Events {
            on(eventName, callback, context) {
                let event, tail, _eventName;
                if (!callback) {
                    return this;
                }
                if (typeof eventName === "symbol") {
                    _eventName = [
                        eventName
                    ];
                } else {
                    _eventName = eventName.split(_Events.eventSplitter);
                }
                this.callbacks || (this.callbacks = {});
                const calls = this.callbacks;
                while(event = _eventName.shift()){
                    const list = calls[event];
                    const node = list ? list.tail : {};
                    node.next = tail = {};
                    node.context = context;
                    node.callback = callback;
                    calls[event] = {
                        tail,
                        next: list ? list.next : node
                    };
                }
                return this;
            }
            once(events, callback, context) {
                const wrapper = (...args)=>{
                    callback.apply(this, args);
                    this.off(events, wrapper, context);
                };
                this.on(events, wrapper, context);
                return this;
            }
            off(events, callback, context) {
                let event, calls, _events;
                if (!(calls = this.callbacks)) {
                    return this;
                }
                if (!(events || callback || context)) {
                    delete this.callbacks;
                    return this;
                }
                if (typeof events === "symbol") {
                    _events = [
                        events
                    ];
                } else {
                    _events = events ? events.split(_Events.eventSplitter) : Object.keys(calls);
                }
                while(event = _events.shift()){
                    let node = calls[event];
                    delete calls[event];
                    if (!node || !(callback || context)) {
                        continue;
                    }
                    const tail = node.tail;
                    while((node = node.next) !== tail){
                        const cb = node.callback;
                        const ctx = node.context;
                        if (callback && cb !== callback || context && ctx !== context) {
                            this.on(event, cb, ctx);
                        }
                    }
                }
                return this;
            }
            trigger(events, ...args) {
                let event, node, calls, _events;
                if (!(calls = this.callbacks)) {
                    return this;
                }
                if (typeof events === "symbol") {
                    _events = [
                        events
                    ];
                } else {
                    _events = events.split(_Events.eventSplitter);
                }
                while(event = _events.shift()){
                    if (node = calls[event]) {
                        const tail = node.tail;
                        while((node = node.next) !== tail){
                            node.callback.apply(node.context || this, args);
                        }
                    }
                }
                return this;
            }
            constructor(opts){
                var _a;
                this.callbacks = (_a = opts === null || opts === void 0 ? void 0 : opts.callbacks) !== null && _a !== void 0 ? _a : {};
            }
        };
        Events.eventSplitter = ",";
    }
});
// node_modules/@tarojs/shared/dist/is.js
function isString(o) {
    return typeof o === "string";
}
function isUndefined(o) {
    return typeof o === "undefined";
}
function isNull(o) {
    return o === null;
}
function isObject(o) {
    return o !== null && typeof o === "object";
}
function isBoolean(o) {
    return o === true || o === false;
}
function isFunction(o) {
    return typeof o === "function";
}
function isNumber(o) {
    if (Number.isFinite) return Number.isFinite(o);
    return typeof o === "number";
}
var isArray;
var init_is = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/is.js" () {
        isArray = Array.isArray;
    }
});
// node_modules/@tarojs/shared/dist/runtime-hooks.js
function TaroHook(type, initial) {
    return {
        type,
        initial: initial || null
    };
}
var HOOK_TYPE, defaultMiniLifecycle, TaroHooks, hooks;
var init_runtime_hooks = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/runtime-hooks.js" () {
        init_event_emitter();
        init_is();
        (function(HOOK_TYPE2) {
            HOOK_TYPE2[HOOK_TYPE2["SINGLE"] = 0] = "SINGLE";
            HOOK_TYPE2[HOOK_TYPE2["MULTI"] = 1] = "MULTI";
            HOOK_TYPE2[HOOK_TYPE2["WATERFALL"] = 2] = "WATERFALL";
        })(HOOK_TYPE || (HOOK_TYPE = {}));
        defaultMiniLifecycle = {
            app: [
                "onLaunch",
                "onShow",
                "onHide"
            ],
            page: [
                "onLoad",
                "onUnload",
                "onReady",
                "onShow",
                "onHide",
                [
                    "onPullDownRefresh",
                    "onReachBottom",
                    "onPageScroll",
                    "onResize",
                    "defer:onTabItemTap",
                    // defer: 需要等页面组件挂载后再调用
                    "onTitleClick",
                    "onOptionMenuClick",
                    "onPopMenuClick",
                    "onPullIntercept",
                    "onAddToFavorites"
                ],
                [
                    "onShareAppMessage",
                    "onShareTimeline"
                ]
            ],
            component: [
                "attached",
                "detached"
            ]
        };
        TaroHooks = class extends Events {
            tapOneOrMany(hookName, callback) {
                const list = isFunction(callback) ? [
                    callback
                ] : callback;
                list.forEach((cb)=>this.on(hookName, cb));
            }
            tap(hookName, callback) {
                const hooks2 = this.hooks;
                const { type, initial } = hooks2[hookName];
                if (type === HOOK_TYPE.SINGLE) {
                    this.off(hookName);
                    this.on(hookName, isFunction(callback) ? callback : callback[callback.length - 1]);
                } else {
                    initial && this.off(hookName, initial);
                    this.tapOneOrMany(hookName, callback);
                }
            }
            call(hookName, ...rest) {
                var _a;
                const hook = this.hooks[hookName];
                if (!hook) return;
                const { type } = hook;
                const calls = this.callbacks;
                if (!calls) return;
                const list = calls[hookName];
                if (list) {
                    const tail = list.tail;
                    let node = list.next;
                    let args = rest;
                    let res;
                    while(node !== tail){
                        res = (_a = node.callback) === null || _a === void 0 ? void 0 : _a.apply(node.context || this, args);
                        if (type === HOOK_TYPE.WATERFALL) {
                            const params = [
                                res
                            ];
                            args = params;
                        }
                        node = node.next;
                    }
                    return res;
                }
            }
            isExist(hookName) {
                var _a;
                return Boolean((_a = this.callbacks) === null || _a === void 0 ? void 0 : _a[hookName]);
            }
            constructor(hooks2, opts){
                super(opts);
                this.hooks = hooks2;
                for(const hookName in hooks2){
                    const { initial } = hooks2[hookName];
                    if (isFunction(initial)) {
                        this.on(hookName, initial);
                    }
                }
            }
        };
        hooks = new TaroHooks({
            getMiniLifecycle: TaroHook(HOOK_TYPE.SINGLE, (defaultConfig)=>defaultConfig),
            getMiniLifecycleImpl: TaroHook(HOOK_TYPE.SINGLE, function() {
                return this.call("getMiniLifecycle", defaultMiniLifecycle);
            }),
            getLifecycle: TaroHook(HOOK_TYPE.SINGLE, (instance, lifecycle)=>instance[lifecycle]),
            modifyRecursiveComponentConfig: TaroHook(HOOK_TYPE.SINGLE, (defaultConfig)=>defaultConfig),
            getPathIndex: TaroHook(HOOK_TYPE.SINGLE, (indexOfNode)=>`[${indexOfNode}]`),
            getEventCenter: TaroHook(HOOK_TYPE.SINGLE, (Events2)=>new Events2()),
            isBubbleEvents: TaroHook(HOOK_TYPE.SINGLE, (eventName)=>{
                const BUBBLE_EVENTS = /* @__PURE__ */ new Set([
                    "touchstart",
                    "touchmove",
                    "touchcancel",
                    "touchend",
                    "touchforcechange",
                    "tap",
                    "longpress",
                    "longtap",
                    "transitionend",
                    "animationstart",
                    "animationiteration",
                    "animationend"
                ]);
                return BUBBLE_EVENTS.has(eventName);
            }),
            getSpecialNodes: TaroHook(HOOK_TYPE.SINGLE, ()=>[
                    "view",
                    "text",
                    "image"
                ]),
            onRemoveAttribute: TaroHook(HOOK_TYPE.SINGLE),
            batchedEventUpdates: TaroHook(HOOK_TYPE.SINGLE),
            mergePageInstance: TaroHook(HOOK_TYPE.SINGLE),
            modifyPageObject: TaroHook(HOOK_TYPE.SINGLE),
            createPullDownComponent: TaroHook(HOOK_TYPE.SINGLE),
            getDOMNode: TaroHook(HOOK_TYPE.SINGLE),
            modifyHydrateData: TaroHook(HOOK_TYPE.SINGLE),
            transferHydrateData: TaroHook(HOOK_TYPE.SINGLE),
            modifySetAttrPayload: TaroHook(HOOK_TYPE.SINGLE),
            modifyRmAttrPayload: TaroHook(HOOK_TYPE.SINGLE),
            onAddEvent: TaroHook(HOOK_TYPE.SINGLE),
            proxyToRaw: TaroHook(HOOK_TYPE.SINGLE, function(proxyObj) {
                return proxyObj;
            }),
            modifyMpEvent: TaroHook(HOOK_TYPE.MULTI),
            modifyMpEventImpl: TaroHook(HOOK_TYPE.SINGLE, function(e) {
                try {
                    this.call("modifyMpEvent", e);
                } catch (error) {
                    console.warn("[Taro modifyMpEvent hook Error]: " + (error === null || error === void 0 ? void 0 : error.message));
                }
            }),
            injectNewStyleProperties: TaroHook(HOOK_TYPE.SINGLE),
            modifyTaroEvent: TaroHook(HOOK_TYPE.MULTI),
            dispatchTaroEvent: TaroHook(HOOK_TYPE.SINGLE, (e, node)=>{
                node.dispatchEvent(e);
            }),
            dispatchTaroEventFinish: TaroHook(HOOK_TYPE.MULTI),
            modifyTaroEventReturn: TaroHook(HOOK_TYPE.SINGLE, ()=>void 0),
            modifyDispatchEvent: TaroHook(HOOK_TYPE.MULTI),
            initNativeApi: TaroHook(HOOK_TYPE.MULTI),
            patchElement: TaroHook(HOOK_TYPE.MULTI),
            modifyAddEventListener: TaroHook(HOOK_TYPE.SINGLE),
            modifyRemoveEventListener: TaroHook(HOOK_TYPE.SINGLE),
            getMemoryLevel: TaroHook(HOOK_TYPE.SINGLE)
        });
    }
});
// node_modules/@tarojs/shared/dist/components.js
function singleQuote(s) {
    return `'${s}'`;
}
var DEFAULT_EMPTY_ARRAY, NO_DEFAULT_VALUE, DEFAULT_TRUE, DEFAULT_FALSE, touchEvents, animation, View, Icon, MapComp, Progress, RichText, Text, Button, Checkbox, CheckboxGroup, Form, Input, Label, Picker, PickerView, PickerViewColumn, Radio, RadioGroup, Slider, Switch, Textarea, CoverImage, CoverView, MovableArea, MovableView, ScrollView, Swiper, SwiperItem, Navigator, Audio, Camera, Image, LivePlayer, Video, Canvas, Ad, WebView, Block, SlotView, Slot, NativeSlot, Script, internalComponents, controlledComponent;
var init_components = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/components.js" () {
        DEFAULT_EMPTY_ARRAY = "[]";
        NO_DEFAULT_VALUE = "";
        DEFAULT_TRUE = "!0";
        DEFAULT_FALSE = "!1";
        touchEvents = {
            bindTouchStart: NO_DEFAULT_VALUE,
            bindTouchMove: NO_DEFAULT_VALUE,
            bindTouchEnd: NO_DEFAULT_VALUE,
            bindTouchCancel: NO_DEFAULT_VALUE,
            bindLongTap: NO_DEFAULT_VALUE
        };
        animation = {
            animation: NO_DEFAULT_VALUE,
            bindAnimationStart: NO_DEFAULT_VALUE,
            bindAnimationIteration: NO_DEFAULT_VALUE,
            bindAnimationEnd: NO_DEFAULT_VALUE,
            bindTransitionEnd: NO_DEFAULT_VALUE
        };
        View = Object.assign(Object.assign({
            "hover-class": singleQuote("none"),
            "hover-stop-propagation": DEFAULT_FALSE,
            "hover-start-time": "50",
            "hover-stay-time": "400"
        }, touchEvents), animation);
        Icon = {
            type: NO_DEFAULT_VALUE,
            size: "23",
            color: NO_DEFAULT_VALUE
        };
        MapComp = Object.assign({
            longitude: NO_DEFAULT_VALUE,
            latitude: NO_DEFAULT_VALUE,
            scale: "16",
            markers: DEFAULT_EMPTY_ARRAY,
            covers: NO_DEFAULT_VALUE,
            polyline: DEFAULT_EMPTY_ARRAY,
            circles: DEFAULT_EMPTY_ARRAY,
            controls: DEFAULT_EMPTY_ARRAY,
            "include-points": DEFAULT_EMPTY_ARRAY,
            "show-location": NO_DEFAULT_VALUE,
            "layer-style": "1",
            bindMarkerTap: NO_DEFAULT_VALUE,
            bindControlTap: NO_DEFAULT_VALUE,
            bindCalloutTap: NO_DEFAULT_VALUE,
            bindUpdated: NO_DEFAULT_VALUE
        }, touchEvents);
        Progress = {
            percent: NO_DEFAULT_VALUE,
            "stroke-width": "6",
            color: singleQuote("#09BB07"),
            activeColor: singleQuote("#09BB07"),
            backgroundColor: singleQuote("#EBEBEB"),
            active: DEFAULT_FALSE,
            "active-mode": singleQuote("backwards"),
            "show-info": DEFAULT_FALSE
        };
        RichText = {
            nodes: DEFAULT_EMPTY_ARRAY
        };
        Text = Object.assign({
            selectable: DEFAULT_FALSE,
            space: NO_DEFAULT_VALUE,
            decode: DEFAULT_FALSE
        }, touchEvents);
        Button = Object.assign({
            size: singleQuote("default"),
            type: NO_DEFAULT_VALUE,
            plain: DEFAULT_FALSE,
            disabled: NO_DEFAULT_VALUE,
            loading: DEFAULT_FALSE,
            "form-type": NO_DEFAULT_VALUE,
            "open-type": NO_DEFAULT_VALUE,
            "hover-class": singleQuote("button-hover"),
            "hover-stop-propagation": DEFAULT_FALSE,
            "hover-start-time": "20",
            "hover-stay-time": "70",
            name: NO_DEFAULT_VALUE,
            bindagreeprivacyauthorization: NO_DEFAULT_VALUE
        }, touchEvents);
        Checkbox = {
            value: NO_DEFAULT_VALUE,
            disabled: NO_DEFAULT_VALUE,
            checked: DEFAULT_FALSE,
            color: singleQuote("#09BB07"),
            name: NO_DEFAULT_VALUE
        };
        CheckboxGroup = {
            bindChange: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        Form = {
            "report-submit": DEFAULT_FALSE,
            bindSubmit: NO_DEFAULT_VALUE,
            bindReset: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        Input = {
            value: NO_DEFAULT_VALUE,
            type: singleQuote(NO_DEFAULT_VALUE),
            password: DEFAULT_FALSE,
            placeholder: NO_DEFAULT_VALUE,
            "placeholder-style": NO_DEFAULT_VALUE,
            "placeholder-class": singleQuote("input-placeholder"),
            disabled: NO_DEFAULT_VALUE,
            maxlength: "140",
            "cursor-spacing": "0",
            focus: DEFAULT_FALSE,
            "confirm-type": singleQuote("done"),
            "confirm-hold": DEFAULT_FALSE,
            cursor: "-1",
            "selection-start": "-1",
            "selection-end": "-1",
            bindInput: NO_DEFAULT_VALUE,
            bindFocus: NO_DEFAULT_VALUE,
            bindBlur: NO_DEFAULT_VALUE,
            bindConfirm: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        Label = Object.assign({
            for: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        }, touchEvents);
        Picker = {
            mode: singleQuote("selector"),
            disabled: NO_DEFAULT_VALUE,
            range: NO_DEFAULT_VALUE,
            "range-key": NO_DEFAULT_VALUE,
            value: NO_DEFAULT_VALUE,
            start: NO_DEFAULT_VALUE,
            end: NO_DEFAULT_VALUE,
            fields: singleQuote("day"),
            "custom-item": NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE,
            bindCancel: NO_DEFAULT_VALUE,
            bindChange: NO_DEFAULT_VALUE,
            bindColumnChange: NO_DEFAULT_VALUE
        };
        PickerView = {
            value: NO_DEFAULT_VALUE,
            "indicator-style": NO_DEFAULT_VALUE,
            "indicator-class": NO_DEFAULT_VALUE,
            "mask-style": NO_DEFAULT_VALUE,
            "mask-class": NO_DEFAULT_VALUE,
            bindChange: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        PickerViewColumn = {
            name: NO_DEFAULT_VALUE
        };
        Radio = {
            value: NO_DEFAULT_VALUE,
            checked: DEFAULT_FALSE,
            disabled: NO_DEFAULT_VALUE,
            color: singleQuote("#09BB07"),
            name: NO_DEFAULT_VALUE
        };
        RadioGroup = {
            bindChange: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        Slider = {
            min: "0",
            max: "100",
            step: "1",
            disabled: NO_DEFAULT_VALUE,
            value: "0",
            activeColor: singleQuote("#1aad19"),
            backgroundColor: singleQuote("#e9e9e9"),
            "block-size": "28",
            "block-color": singleQuote("#ffffff"),
            "show-value": DEFAULT_FALSE,
            bindChange: NO_DEFAULT_VALUE,
            bindChanging: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        Switch = {
            checked: DEFAULT_FALSE,
            disabled: NO_DEFAULT_VALUE,
            type: singleQuote("switch"),
            color: singleQuote("#04BE02"),
            bindChange: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        Textarea = {
            value: NO_DEFAULT_VALUE,
            placeholder: NO_DEFAULT_VALUE,
            "placeholder-style": NO_DEFAULT_VALUE,
            "placeholder-class": singleQuote("textarea-placeholder"),
            disabled: NO_DEFAULT_VALUE,
            maxlength: "140",
            "auto-focus": DEFAULT_FALSE,
            focus: DEFAULT_FALSE,
            "auto-height": DEFAULT_FALSE,
            fixed: DEFAULT_FALSE,
            "cursor-spacing": "0",
            cursor: "-1",
            "selection-start": "-1",
            "selection-end": "-1",
            bindFocus: NO_DEFAULT_VALUE,
            bindBlur: NO_DEFAULT_VALUE,
            bindLineChange: NO_DEFAULT_VALUE,
            bindInput: NO_DEFAULT_VALUE,
            bindConfirm: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE
        };
        CoverImage = {
            src: NO_DEFAULT_VALUE,
            bindLoad: "eh",
            bindError: "eh"
        };
        CoverView = Object.assign({
            "scroll-top": DEFAULT_FALSE
        }, touchEvents);
        MovableArea = {
            "scale-area": DEFAULT_FALSE
        };
        MovableView = Object.assign(Object.assign({
            direction: "none",
            inertia: DEFAULT_FALSE,
            "out-of-bounds": DEFAULT_FALSE,
            x: NO_DEFAULT_VALUE,
            y: NO_DEFAULT_VALUE,
            damping: "20",
            friction: "2",
            disabled: NO_DEFAULT_VALUE,
            scale: DEFAULT_FALSE,
            "scale-min": "0.5",
            "scale-max": "10",
            "scale-value": "1",
            bindChange: NO_DEFAULT_VALUE,
            bindScale: NO_DEFAULT_VALUE,
            bindHTouchMove: NO_DEFAULT_VALUE,
            bindVTouchMove: NO_DEFAULT_VALUE,
            width: singleQuote("10px"),
            height: singleQuote("10px")
        }, touchEvents), animation);
        ScrollView = Object.assign(Object.assign({
            "scroll-x": DEFAULT_FALSE,
            "scroll-y": DEFAULT_FALSE,
            "upper-threshold": "50",
            "lower-threshold": "50",
            "scroll-top": NO_DEFAULT_VALUE,
            "scroll-left": NO_DEFAULT_VALUE,
            "scroll-into-view": NO_DEFAULT_VALUE,
            "scroll-with-animation": DEFAULT_FALSE,
            "enable-back-to-top": DEFAULT_FALSE,
            bindScrollToUpper: NO_DEFAULT_VALUE,
            bindScrollToLower: NO_DEFAULT_VALUE,
            bindScroll: NO_DEFAULT_VALUE
        }, touchEvents), animation);
        Swiper = Object.assign({
            "indicator-dots": DEFAULT_FALSE,
            "indicator-color": singleQuote("rgba(0, 0, 0, .3)"),
            "indicator-active-color": singleQuote("#000000"),
            autoplay: DEFAULT_FALSE,
            current: "0",
            interval: "5000",
            duration: "500",
            circular: DEFAULT_FALSE,
            vertical: DEFAULT_FALSE,
            "previous-margin": singleQuote("0px"),
            "next-margin": singleQuote("0px"),
            "display-multiple-items": "1",
            bindChange: NO_DEFAULT_VALUE,
            bindTransition: NO_DEFAULT_VALUE,
            bindAnimationFinish: NO_DEFAULT_VALUE
        }, touchEvents);
        SwiperItem = {
            "item-id": NO_DEFAULT_VALUE
        };
        Navigator = {
            url: NO_DEFAULT_VALUE,
            "open-type": singleQuote("navigate"),
            delta: "1",
            "hover-class": singleQuote("navigator-hover"),
            "hover-stop-propagation": DEFAULT_FALSE,
            "hover-start-time": "50",
            "hover-stay-time": "600",
            bindSuccess: NO_DEFAULT_VALUE,
            bindFail: NO_DEFAULT_VALUE,
            bindComplete: NO_DEFAULT_VALUE
        };
        Audio = {
            id: NO_DEFAULT_VALUE,
            src: NO_DEFAULT_VALUE,
            loop: DEFAULT_FALSE,
            controls: DEFAULT_FALSE,
            poster: NO_DEFAULT_VALUE,
            name: NO_DEFAULT_VALUE,
            author: NO_DEFAULT_VALUE,
            bindError: NO_DEFAULT_VALUE,
            bindPlay: NO_DEFAULT_VALUE,
            bindPause: NO_DEFAULT_VALUE,
            bindTimeUpdate: NO_DEFAULT_VALUE,
            bindEnded: NO_DEFAULT_VALUE
        };
        Camera = {
            "device-position": singleQuote("back"),
            flash: singleQuote("auto"),
            bindStop: NO_DEFAULT_VALUE,
            bindError: NO_DEFAULT_VALUE
        };
        Image = Object.assign({
            src: NO_DEFAULT_VALUE,
            mode: singleQuote("scaleToFill"),
            "lazy-load": DEFAULT_FALSE,
            bindError: NO_DEFAULT_VALUE,
            bindLoad: NO_DEFAULT_VALUE
        }, touchEvents);
        LivePlayer = Object.assign({
            src: NO_DEFAULT_VALUE,
            autoplay: DEFAULT_FALSE,
            muted: DEFAULT_FALSE,
            orientation: singleQuote("vertical"),
            "object-fit": singleQuote("contain"),
            "background-mute": DEFAULT_FALSE,
            "min-cache": "1",
            "max-cache": "3",
            bindStateChange: NO_DEFAULT_VALUE,
            bindFullScreenChange: NO_DEFAULT_VALUE,
            bindNetStatus: NO_DEFAULT_VALUE
        }, animation);
        Video = Object.assign({
            src: NO_DEFAULT_VALUE,
            duration: NO_DEFAULT_VALUE,
            controls: DEFAULT_TRUE,
            "danmu-list": NO_DEFAULT_VALUE,
            "danmu-btn": NO_DEFAULT_VALUE,
            "enable-danmu": NO_DEFAULT_VALUE,
            autoplay: DEFAULT_FALSE,
            loop: DEFAULT_FALSE,
            muted: DEFAULT_FALSE,
            "initial-time": "0",
            "page-gesture": DEFAULT_FALSE,
            direction: NO_DEFAULT_VALUE,
            "show-progress": DEFAULT_TRUE,
            "show-fullscreen-btn": DEFAULT_TRUE,
            "show-play-btn": DEFAULT_TRUE,
            "show-center-play-btn": DEFAULT_TRUE,
            "enable-progress-gesture": DEFAULT_TRUE,
            "object-fit": singleQuote("contain"),
            poster: NO_DEFAULT_VALUE,
            "show-mute-btn": DEFAULT_FALSE,
            bindPlay: NO_DEFAULT_VALUE,
            bindPause: NO_DEFAULT_VALUE,
            bindEnded: NO_DEFAULT_VALUE,
            bindTimeUpdate: NO_DEFAULT_VALUE,
            bindFullScreenChange: NO_DEFAULT_VALUE,
            bindWaiting: NO_DEFAULT_VALUE,
            bindError: NO_DEFAULT_VALUE
        }, animation);
        Canvas = Object.assign({
            "canvas-id": NO_DEFAULT_VALUE,
            "disable-scroll": DEFAULT_FALSE,
            bindError: NO_DEFAULT_VALUE
        }, touchEvents);
        Ad = {
            "unit-id": NO_DEFAULT_VALUE,
            "ad-intervals": NO_DEFAULT_VALUE,
            bindLoad: NO_DEFAULT_VALUE,
            bindError: NO_DEFAULT_VALUE,
            bindClose: NO_DEFAULT_VALUE
        };
        WebView = {
            src: NO_DEFAULT_VALUE,
            bindMessage: NO_DEFAULT_VALUE,
            bindLoad: NO_DEFAULT_VALUE,
            bindError: NO_DEFAULT_VALUE
        };
        Block = {};
        SlotView = {
            name: NO_DEFAULT_VALUE
        };
        Slot = {
            name: NO_DEFAULT_VALUE
        };
        NativeSlot = {
            name: NO_DEFAULT_VALUE
        };
        Script = {};
        internalComponents = {
            View,
            Icon,
            Progress,
            RichText,
            Text,
            Button,
            Checkbox,
            CheckboxGroup,
            Form,
            Input,
            Label,
            Picker,
            PickerView,
            PickerViewColumn,
            Radio,
            RadioGroup,
            Slider,
            Switch,
            CoverImage,
            Textarea,
            CoverView,
            MovableArea,
            MovableView,
            ScrollView,
            Swiper,
            SwiperItem,
            Navigator,
            Audio,
            Camera,
            Image,
            LivePlayer,
            Video,
            Canvas,
            Ad,
            WebView,
            Block,
            Map: MapComp,
            Slot,
            SlotView,
            NativeSlot,
            Script
        };
        controlledComponent = /* @__PURE__ */ new Set([
            "input",
            "checkbox",
            "picker",
            "picker-view",
            "radio",
            "slider",
            "switch",
            "textarea"
        ]);
    }
});
// node_modules/@tarojs/shared/dist/constants.js
var PLATFORM_TYPE, PLATFORM_CONFIG_MAP;
var init_constants = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/constants.js" () {
        (function(PLATFORM_TYPE2) {
            PLATFORM_TYPE2["MINI"] = "mini";
            PLATFORM_TYPE2["ASCF"] = "ascf";
            PLATFORM_TYPE2["WEB"] = "web";
            PLATFORM_TYPE2["RN"] = "rn";
            PLATFORM_TYPE2["HARMONY"] = "harmony";
            PLATFORM_TYPE2["QUICK"] = "quickapp";
        })(PLATFORM_TYPE || (PLATFORM_TYPE = {}));
        PLATFORM_CONFIG_MAP = {
            h5: {
                type: PLATFORM_TYPE.WEB
            },
            harmony: {
                type: PLATFORM_TYPE.HARMONY
            },
            ascf: {
                type: PLATFORM_TYPE.ASCF
            },
            mini: {
                type: PLATFORM_TYPE.MINI
            },
            rn: {
                type: PLATFORM_TYPE.RN
            },
            quickapp: {
                type: PLATFORM_TYPE.QUICK
            }
        };
    }
});
// node_modules/@tarojs/shared/dist/event-channel.js
var PageEvts, pageChannel, RouteEvts, routeChannel;
var init_event_channel = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/event-channel.js" () {
        init_event_emitter();
        PageEvts = class extends Events {
            on(eventName, callback) {
                super.on(eventName, callback, this);
                this.exeList = this.exeList.reduce((prev, item)=>{
                    if (item.eventName === eventName) {
                        super.trigger(item.eventName, item.data);
                    } else {
                        prev.push(item);
                    }
                    return prev;
                }, []);
                return this;
            }
            emit(events, data) {
                routeChannel.trigger(events, data);
            }
            constructor(){
                super(...arguments);
                this.exeList = [];
            }
        };
        pageChannel = new PageEvts();
        RouteEvts = class extends Events {
            emit(events, data) {
                pageChannel.off(events);
                pageChannel.exeList.push({
                    eventName: events,
                    data
                });
            }
            addEvents(events) {
                if (!events || typeof events !== "object") return;
                Object.keys(events).forEach((key)=>{
                    this.off(key);
                    this.on(key, events[key], this);
                });
            }
        };
        routeChannel = new RouteEvts();
    }
});
// node_modules/@tarojs/shared/dist/utils.js
function toDashed(s) {
    return s.replace(/([a-z0-9])([A-Z])/g, "$1-$2").toLowerCase();
}
function toCamelCase(s) {
    let camel = "";
    let nextCap = false;
    for(let i = 0; i < s.length; i++){
        if (s[i] !== "-") {
            camel += nextCap ? s[i].toUpperCase() : s[i];
            nextCap = false;
        } else {
            nextCap = true;
        }
    }
    return camel;
}
function capitalize(s) {
    return s.charAt(0).toUpperCase() + s.slice(1);
}
function ensure(condition, msg) {
    if (!condition) {
        if (true) {
            const reportIssue = "\n\u5982\u6709\u7591\u95EE\uFF0C\u8BF7\u63D0\u4EA4 issue \u81F3\uFF1Ahttps://github.com/nervjs/taro/issues";
            throw new Error(msg + reportIssue);
        } else {}
    }
}
function warn(condition, msg) {
    if (true) {
        if (condition) {
            console.warn(`[taro warn] ${msg}`);
        }
    }
}
function getUniqueKey() {
    return _loadTime + _uniqueId++;
}
function mergeInternalComponents(components) {
    Object.keys(components).forEach((name)=>{
        if (name in internalComponents) {
            Object.assign(internalComponents[name], components[name]);
        } else {
            internalComponents[name] = components[name];
        }
    });
    return internalComponents;
}
function getComponentsAlias(origin) {
    const mapping = {};
    const viewAttrs = origin.View;
    const extraList = {
        "#text": {},
        StaticView: viewAttrs,
        StaticImage: origin.Image,
        StaticText: origin.Text,
        PureView: viewAttrs,
        CatchView: viewAttrs,
        ClickView: viewAttrs
    };
    origin = Object.assign(Object.assign({}, origin), extraList);
    Object.keys(origin).sort((a, b)=>{
        const reg = /^(Static|Pure|Catch|Click)*(View|Image|Text)$/;
        const isACommonly = reg.test(a);
        const isBCommonly = reg.test(b);
        if (isACommonly && isBCommonly) {
            return a > b ? 1 : -1;
        } else if (isACommonly) {
            return -1;
        } else if (isBCommonly) {
            return 1;
        } else {
            return a >= b ? 1 : -1;
        }
    }).forEach((key, num)=>{
        const obj = {
            _num: String(num)
        };
        Object.keys(origin[key]).filter((attr)=>!/^bind/.test(attr) && ![
                "focus",
                "blur"
            ].includes(attr)).sort().forEach((attr, index)=>{
            obj[toCamelCase(attr)] = "p" + index;
        });
        mapping[toDashed(key)] = obj;
    });
    return mapping;
}
function mergeReconciler(hostConfig, hooksForTest) {
    const obj = hooksForTest || hooks;
    const keys = Object.keys(hostConfig);
    keys.forEach((key)=>{
        obj.tap(key, hostConfig[key]);
    });
}
function nonsupport(api) {
    return function() {
        console.warn(`\u5C0F\u7A0B\u5E8F\u6682\u4E0D\u652F\u6301 ${api}`);
    };
}
function setUniqueKeyToRoute(key, obj) {
    const routerParamsPrivateKey = "__key_";
    const useDataCacheApis = [
        "navigateTo",
        "redirectTo",
        "reLaunch",
        "switchTab"
    ];
    if (useDataCacheApis.indexOf(key) > -1) {
        const url = obj.url = obj.url || "";
        const hasMark = url.indexOf("?") > -1;
        const cacheKey = getUniqueKey();
        obj.url += (hasMark ? "&" : "?") + `${routerParamsPrivateKey}=${cacheKey}`;
    }
}
var EMPTY_OBJ, noop, _uniqueId, _loadTime;
var init_utils = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/utils.js" () {
        init_components();
        init_constants();
        init_runtime_hooks();
        EMPTY_OBJ = {};
        noop = (..._)=>{};
        _uniqueId = 1;
        _loadTime = /* @__PURE__ */ new Date().getTime().toString();
    }
});
// node_modules/@tarojs/shared/dist/native-apis.js
function getCanIUseWebp(taro) {
    return function() {
        var _a;
        const res = (_a = taro.getSystemInfoSync) === null || _a === void 0 ? void 0 : _a.call(taro);
        if (!res) {
            if (true) {
                console.error("\u4E0D\u652F\u6301 API canIUseWebp");
            }
            return false;
        }
        const { platform } = res;
        const platformLower = platform.toLowerCase();
        if (platformLower === "android" || platformLower === "devtools") {
            return true;
        }
        return false;
    };
}
function getNormalRequest(global) {
    return function request(options) {
        options = options ? isString(options) ? {
            url: options
        } : options : {};
        const originSuccess = options.success;
        const originFail = options.fail;
        const originComplete = options.complete;
        let requestTask;
        const p = new Promise((resolve, reject)=>{
            options.success = (res)=>{
                originSuccess && originSuccess(res);
                resolve(res);
            };
            options.fail = (res)=>{
                originFail && originFail(res);
                reject(res);
            };
            options.complete = (res)=>{
                originComplete && originComplete(res);
            };
            requestTask = global.request(options);
        });
        equipTaskMethodsIntoPromise(requestTask, p);
        p.abort = (cb)=>{
            cb && cb();
            if (requestTask) {
                requestTask.abort();
            }
            return p;
        };
        return p;
    };
}
function processApis(taro, global, config = {}) {
    const patchNeedPromiseApis = config.needPromiseApis || [];
    const _needPromiseApis = /* @__PURE__ */ new Set([
        ...patchNeedPromiseApis,
        ...needPromiseApis
    ]);
    const preserved = [
        "getEnv",
        "interceptors",
        "Current",
        "getCurrentInstance",
        "options",
        "nextTick",
        "eventCenter",
        "Events",
        "preload",
        "webpackJsonp"
    ];
    const apis = new Set(!config.isOnlyPromisify ? Object.keys(global).filter((api)=>preserved.indexOf(api) === -1) : patchNeedPromiseApis);
    if (config.modifyApis) {
        config.modifyApis(apis);
    }
    apis.forEach((key)=>{
        if (_needPromiseApis.has(key)) {
            const originKey = key;
            taro[originKey] = (options = {}, ...args)=>{
                let key2 = originKey;
                if (typeof options === "string") {
                    if (args.length) {
                        return global[key2](options, ...args);
                    }
                    return global[key2](options);
                }
                if (config.transformMeta) {
                    const transformResult = config.transformMeta(key2, options);
                    key2 = transformResult.key;
                    options = transformResult.options;
                    if (!global.hasOwnProperty(key2)) {
                        return nonsupport(key2)();
                    }
                }
                let task = null;
                const obj = Object.assign({}, options);
                setUniqueKeyToRoute(key2, options);
                const p = new Promise((resolve, reject)=>{
                    obj.success = (res)=>{
                        var _a, _b;
                        (_a = config.modifyAsyncResult) === null || _a === void 0 ? void 0 : _a.call(config, key2, res);
                        (_b = options.success) === null || _b === void 0 ? void 0 : _b.call(options, res);
                        if (key2 === "connectSocket") {
                            resolve(Promise.resolve().then(()=>task ? Object.assign(task, res) : res));
                        } else {
                            resolve(res);
                        }
                    };
                    obj.fail = (res)=>{
                        var _a;
                        (_a = options.fail) === null || _a === void 0 ? void 0 : _a.call(options, res);
                        reject(res);
                    };
                    obj.complete = (res)=>{
                        var _a;
                        (_a = options.complete) === null || _a === void 0 ? void 0 : _a.call(options, res);
                    };
                    if (args.length) {
                        task = global[key2](obj, ...args);
                    } else {
                        task = global[key2](obj);
                    }
                });
                if ([
                    "uploadFile",
                    "downloadFile"
                ].includes(key2)) {
                    equipTaskMethodsIntoPromise(task, p);
                    p.progress = (cb)=>{
                        task === null || task === void 0 ? void 0 : task.onProgressUpdate(cb);
                        return p;
                    };
                    p.abort = (cb)=>{
                        cb === null || cb === void 0 ? void 0 : cb();
                        task === null || task === void 0 ? void 0 : task.abort();
                        return p;
                    };
                }
                return p;
            };
        } else {
            let platformKey = key;
            if (config.transformMeta) {
                platformKey = config.transformMeta(key, {}).key;
            }
            if (!global.hasOwnProperty(platformKey)) {
                taro[key] = nonsupport(key);
                return;
            }
            if (isFunction(global[key])) {
                taro[key] = (...args)=>{
                    if (config.handleSyncApis) {
                        return config.handleSyncApis(key, global, args);
                    } else {
                        return global[platformKey].apply(global, args);
                    }
                };
            } else {
                taro[key] = global[platformKey];
            }
        }
    });
    !config.isOnlyPromisify && equipCommonApis(taro, global, config);
}
function equipCommonApis(taro, global, apis = {}) {
    taro.canIUseWebp = getCanIUseWebp(taro);
    taro.getCurrentPages = getCurrentPages || nonsupport("getCurrentPages");
    taro.getApp = getApp || nonsupport("getApp");
    taro.env = global.env || {};
    try {
        taro.requirePlugin = requirePlugin || nonsupport("requirePlugin");
    } catch (error) {
        taro.requirePlugin = nonsupport("requirePlugin");
    }
    const request = apis.request || getNormalRequest(global);
    function taroInterceptor(chain) {
        return request(chain.requestParams);
    }
    const link = new taro.Link(taroInterceptor);
    taro.request = link.request.bind(link);
    taro.addInterceptor = link.addInterceptor.bind(link);
    taro.cleanInterceptors = link.cleanInterceptors.bind(link);
    taro.miniGlobal = taro.options.miniGlobal = global;
    taro.getAppInfo = function() {
        return {
            platform: "mini",
            taroVersion: "4.1.3",
            designWidth: taro.config.designWidth
        };
    };
    taro.createSelectorQuery = delayRef(taro, global, "createSelectorQuery", "exec");
    taro.createIntersectionObserver = delayRef(taro, global, "createIntersectionObserver", "observe");
}
function equipTaskMethodsIntoPromise(task, promise) {
    if (!task || !promise) return;
    const taskMethods = [
        "abort",
        "onHeadersReceived",
        "offHeadersReceived",
        "onProgressUpdate",
        "offProgressUpdate",
        "onChunkReceived",
        "offChunkReceived"
    ];
    task && taskMethods.forEach((method)=>{
        if (method in task) {
            promise[method] = task[method].bind(task);
        }
    });
}
function delayRef(taro, global, name, method) {
    return function(...args) {
        const res = global[name](...args);
        const raw = res[method].bind(res);
        res[method] = function(...methodArgs) {
            taro.nextTick(()=>raw(...methodArgs));
        };
        return res;
    };
}
var needPromiseApis;
var init_native_apis = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/native-apis.js" () {
        init_is();
        init_utils();
        needPromiseApis = /* @__PURE__ */ new Set([
            "addPhoneContact",
            "authorize",
            "canvasGetImageData",
            "canvasPutImageData",
            "canvasToTempFilePath",
            "checkSession",
            "chooseAddress",
            "chooseImage",
            "chooseInvoiceTitle",
            "chooseLocation",
            "chooseVideo",
            "clearStorage",
            "closeBLEConnection",
            "closeBluetoothAdapter",
            "closeSocket",
            "compressImage",
            "connectSocket",
            "createBLEConnection",
            "downloadFile",
            "exitMiniProgram",
            "getAvailableAudioSources",
            "getBLEDeviceCharacteristics",
            "getBLEDeviceServices",
            "getBatteryInfo",
            "getBeacons",
            "getBluetoothAdapterState",
            "getBluetoothDevices",
            "getClipboardData",
            "getConnectedBluetoothDevices",
            "getConnectedWifi",
            "getExtConfig",
            "getFileInfo",
            "getImageInfo",
            "getLocation",
            "getNetworkType",
            "getSavedFileInfo",
            "getSavedFileList",
            "getScreenBrightness",
            "getSetting",
            "getStorage",
            "getStorageInfo",
            "getSystemInfo",
            "getUserInfo",
            "getWifiList",
            "hideHomeButton",
            "hideShareMenu",
            "hideTabBar",
            "hideTabBarRedDot",
            "loadFontFace",
            "login",
            "makePhoneCall",
            "navigateBack",
            "navigateBackMiniProgram",
            "navigateTo",
            "navigateToBookshelf",
            "navigateToMiniProgram",
            "notifyBLECharacteristicValueChange",
            "hideKeyboard",
            "hideLoading",
            "hideNavigationBarLoading",
            "hideToast",
            "openBluetoothAdapter",
            "openDocument",
            "openLocation",
            "openSetting",
            "pageScrollTo",
            "previewImage",
            "queryBookshelf",
            "reLaunch",
            "readBLECharacteristicValue",
            "redirectTo",
            "removeSavedFile",
            "removeStorage",
            "removeTabBarBadge",
            "requestSubscribeMessage",
            "saveFile",
            "saveImageToPhotosAlbum",
            "saveVideoToPhotosAlbum",
            "scanCode",
            "sendSocketMessage",
            "setBackgroundColor",
            "setBackgroundTextStyle",
            "setClipboardData",
            "setEnableDebug",
            "setInnerAudioOption",
            "setKeepScreenOn",
            "setNavigationBarColor",
            "setNavigationBarTitle",
            "setScreenBrightness",
            "setStorage",
            "setTabBarBadge",
            "setTabBarItem",
            "setTabBarStyle",
            "showActionSheet",
            "showFavoriteGuide",
            "showLoading",
            "showModal",
            "showShareMenu",
            "showTabBar",
            "showTabBarRedDot",
            "showToast",
            "startBeaconDiscovery",
            "startBluetoothDevicesDiscovery",
            "startDeviceMotionListening",
            "startPullDownRefresh",
            "stopBeaconDiscovery",
            "stopBluetoothDevicesDiscovery",
            "stopCompass",
            "startCompass",
            "startAccelerometer",
            "stopAccelerometer",
            "showNavigationBarLoading",
            "stopDeviceMotionListening",
            "stopPullDownRefresh",
            "switchTab",
            "uploadFile",
            "vibrateLong",
            "vibrateShort",
            "writeBLECharacteristicValue"
        ]);
    }
});
// node_modules/@tarojs/shared/dist/shortcuts.js
var Shortcuts;
var init_shortcuts = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/shortcuts.js" () {
        (function(Shortcuts2) {
            Shortcuts2["Container"] = "container";
            Shortcuts2["Childnodes"] = "cn";
            Shortcuts2["Text"] = "v";
            Shortcuts2["NodeType"] = "nt";
            Shortcuts2["NodeName"] = "nn";
            Shortcuts2["Sid"] = "sid";
            Shortcuts2["Style"] = "st";
            Shortcuts2["Class"] = "cl";
            Shortcuts2["Src"] = "src";
        })(Shortcuts || (Shortcuts = {}));
    }
});
// node_modules/@tarojs/shared/dist/index.js
var init_dist = (0,_chunk_QRPWKJ4C_js__WEBPACK_IMPORTED_MODULE_0__.__esm)({
    "node_modules/@tarojs/shared/dist/index.js" () {
        init_components();
        init_constants();
        init_event_channel();
        init_event_emitter();
        init_is();
        init_native_apis();
        init_runtime_hooks();
        init_shortcuts();
        init_utils();
    }
});



/***/ })

}]);